# 值得买API调用工具

根据[值得买开放平台官方文档](https://openapi.zhidemai.com/pages/rookie/2.API%E8%B0%83%E7%94%A8%E6%96%B9%E6%B3%95%E8%AF%A6%E8%A7%A3.html)实现的Python API调用工具。

## 功能特点

- ✅ 完全按照官方文档实现签名算法
- ✅ 自动生成时间戳和签名
- ✅ 支持所有API端点
- ✅ 参数自动URL编码
- ✅ 签名算法已验证正确

## 文件说明

- `zhidemai_api.py` - 核心API URL生成器
- `example_usage.py` - 使用示例和HTTP请求演示
- `API_README.md` - 本说明文档

## 快速开始

### 1. 基本使用

```python
from zhidemai_api import generate_api_url

# 生成API URL
url = generate_api_url(
    endpoint='haojia/third/list',
    app_key='your_app_key',
    app_secret='your_app_secret',
    params={
        'page': '1',
        'page_size': '10',
        'order_status': '1'
    }
)

print(url)
# 输出: https://openapi.smzdm.com/haojia/third/list?app_key=your_app_key&timestamp=1234567890&page=1&page_size=10&order_status=1&sign=ABCD1234...
```

### 2. HTTP请求调用

```python
import requests
from zhidemai_api import generate_api_url

# 生成URL
url = generate_api_url(
    endpoint='haojia/third/list',
    app_key='your_app_key',
    app_secret='your_app_secret',
    params={'page': '1', 'page_size': '5'}
)

# 发起请求
response = requests.get(url)
data = response.json()
print(data)
```

## 配置说明

### 获取密钥

1. 访问[值得买开放平台](https://openapi.smzdm.com/)
2. 注册并创建应用
3. 获取 `app_key` 和 `app_secret`

### 替换密钥

将代码中的以下部分替换为真实值：

```python
APP_KEY = "your_app_key_here"      # 替换为真实的app_key
APP_SECRET = "your_app_secret_here" # 替换为真实的app_secret
```

## 支持的API端点

### 好价相关
- `haojia/third/list` - 好价列表
- `haojia/third/info` - 好价详情
- `haojia/third/events` - 好价事件

### 文章相关
- `article/third/list` - 文章列表
- `article/third/info` - 文章详情

### 搜索相关
- `search/article/list` - 搜索文章
- `search/goods/list` - 搜索商品
- `search/haojia/list` - 搜索好价

### 更多端点
参考[官方API文档](https://openapi.smzdm.com/)获取完整的端点列表。

## 签名算法验证

运行测试验证签名算法：

```bash
python3 zhidemai_api.py
```

输出应该显示：
```
=== 签名验证 ===
排序后的参数: ['app_key', 'order_status', 'page', 'page_size', 'timestamp']
拼接字符串: app_key123456789order_status1page1page_size10timestamp1480411125
签名字符串: 88888888app_key123456789order_status1page1page_size10timestamp148041112588888888
生成签名: 27C4D45CE6F51B71493FC2B9AA80DB23
文档签名: 27C4D45CE6F51B71493FC2B9AA80DB23
验证结果: ✓ 正确
```

## 使用示例

运行完整示例：

```bash
python3 example_usage.py
```

## 注意事项

1. **时间戳有效期**: API服务端允许时间戳误差为10分钟
2. **参数编码**: 所有参数值会自动进行URL编码
3. **签名大小写**: 签名必须是大写的MD5值
4. **空值过滤**: 空值和null值会被自动过滤

## 错误处理

常见错误码：

- `100111` - 缺少app_key参数
- `100113` - 密钥配置错误
- `100114` - sign或timestamp字段为空
- `100120` - timestamp字段过期
- `100130` - 签名校验失败

## 技术实现

签名生成步骤：

1. 收集所有参数（公共参数 + 业务参数）
2. 过滤空值参数
3. 按ASCII升序排序参数名
4. 拼接为 `key1value1key2value2...` 格式
5. 前后添加 `app_secret`
6. MD5加密并转为大写
7. 添加签名参数生成最终URL

## 许可证

本工具基于值得买开放平台官方文档实现，仅供学习和开发使用。
