# 异步日志记录功能使用指南

## 概述
`agent_flow_async_logger.py` 是一个基于原始 `agent_flow.py` 的增强版本，添加了异步日志记录功能，使用 Supabase 存储 LLM 交互日志，不影响主链路性能。

## 主要特性
- ✅ **异步日志记录**：使用后台线程处理日志存储，不影响主链路延迟
- ✅ **Supabase 集成**：自动将 LLM 交互数据保存到 Supabase
- ✅ **零侵入性**：完全复制原始文件，不影响现有业务逻辑
- ✅ **自动启动**：初始化时自动启动异步日志处理器

## 使用方法

### 1. 基本使用

```python
from redis import Redis
from src.fc_module.agent_flow_async_logger import AgentFlowAsyncLogger

# 初始化
cache = Redis(host="127.0.0.1", port=6379, db=0)
agent_flow = AgentFlowAsyncLogger(cache)

# 使用方式与原始 AgentFlow 完全相同
result = agent_flow.main_agent_flow(
    user_id="user_123",
    req_id="req_456",
    conversation_id="conv_789",
    intent="10007",
    inp="打车去北京天安门",
    history_str="",
    hist_messages=[],
    coords="116.480881,39.989410",
    token="your_token"
)

print(result.content)
```

### 2. 日志记录内容
每次 LLM 调用后，系统会自动记录以下信息到 Supabase：
- 用户ID
- 会话ID
- 用户查询
- 完整的对话消息
- 使用的工具列表
- 模型名称
- 模型响应
- 时间戳

### 3. 数据结构
日志数据存储在 `function_calling_llm_input_param` 表中，包含以下字段：
- `user_id`: 用户标识
- `conversation_id`: 会话标识
- `user_query`: 用户原始查询
- `messages`: 完整的对话历史（JSON格式）
- `tools`: 使用的工具列表（JSON格式）
- `model_name`: 使用的模型名称
- `response`: 模型响应内容
- `created_at`: 记录创建时间

### 4. 测试方法

运行测试脚本：
```bash
python test_async_logger.py
```

## 注意事项
1. **性能影响**：异步日志记录对主链路延迟几乎无影响
2. **错误处理**：日志记录失败不会影响主业务流程
3. **资源清理**：AgentFlowAsyncLogger 实例销毁时会自动停止日志处理器
4. **线程安全**：使用线程安全的队列实现异步处理

## 与原始 AgentFlow 的区别
- 类名从 `AgentFlow` 改为 `AgentFlowAsyncLogger`
- 增加了异步日志记录功能
- 其他 API 和用法完全一致
- 可以直接替换使用，无需修改调用代码
