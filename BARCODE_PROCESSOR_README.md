# 条形码处理器 (Barcode Processor)

一个用于从商品条形码链接中提取条形码code的Python工具模块。

## 功能特点

- ✅ 支持多种URL格式的条形码提取
- ✅ 智能识别常见条形码类型 (EAN-13, UPC-A, EAN-8, GTIN-14, ISBN-10)
- ✅ 条形码校验位验证
- ✅ 国家/地区代码识别
- ✅ 简单易用的API接口
- ✅ 完善的错误处理

## 安装和导入

```python
from src.fc_module.tools.barcode_processor import get_barcode_code_from_url, process_barcode_url
```

## 快速开始

### 基本使用 - 直接获取条形码

```python
from src.fc_module.tools.barcode_processor import get_barcode_code_from_url

# 从URL中提取条形码
barcode_url = "https://example.com/barcode?code=6901234567890"
barcode_code = get_barcode_code_from_url(barcode_url)
print(f"条形码: {barcode_code}")  # 输出: 条形码: 6901234567890
```

### 详细使用 - 获取完整信息

```python
from src.fc_module.tools.barcode_processor import process_barcode_url

# 获取详细的条形码信息
barcode_url = "https://example.com/barcode?code=6901234567890"
result = process_barcode_url(barcode_url)

if result['success']:
    print(f"条形码: {result['barcode_code']}")
    print(f"类型: {result['barcode_info']['type']}")
    print(f"长度: {result['barcode_info']['length']}")
    print(f"国家: {result['barcode_info']['country_code']}")
    print(f"校验: {'有效' if result['barcode_info']['is_valid'] else '无效'}")
else:
    print(f"错误: {result['error']}")
```

## 支持的URL格式

### 1. URL参数形式
```
https://example.com/api?code=1234567890123
https://shop.com/product?barcode=6901234567890
https://api.com/lookup?ean=9876543210987
https://store.com/item?upc=123456789012
```

### 2. URL路径形式
```
https://barcode.com/product/6901234567890
https://shop.com/item/1234567890123/details
https://api.barcode.com/lookup/barcode_6901234567890
https://store.com/product_9876543210987
```

### 3. 关键词形式
```
https://example.com/barcode_1234567890123
https://api.com/product-6901234567890
https://shop.com/item/code_123456789012
```

## 支持的条形码类型

| 类型 | 长度 | 描述 | 示例 |
|------|------|------|------|
| EAN-13 | 13位 | 国际商品条码 | 6901234567890 |
| UPC-A | 12位 | 美国统一商品代码 | 123456789012 |
| EAN-8 | 8位 | 短版国际商品条码 | 12345678 |
| GTIN-14 | 14位 | 全球贸易项目代码 | 12345678901234 |
| ISBN-10 | 10位 | 国际标准书号 | 1234567890 |

## API参考

### get_barcode_code_from_url(barcode_url)

**简化版函数**，直接返回条形码字符串。

**参数:**
- `barcode_url` (str): 商品条形码的链接

**返回:**
- `str`: 条形码code字符串，提取失败时返回空字符串

**示例:**
```python
code = get_barcode_code_from_url("https://example.com/barcode?code=1234567890123")
print(code)  # "1234567890123"
```

### process_barcode_url(barcode_url)

**完整版函数**，返回详细的条形码信息。

**参数:**
- `barcode_url` (str): 商品条形码的链接

**返回:**
- `dict`: 包含以下字段的字典
  - `success` (bool): 是否成功提取
  - `barcode_code` (str): 条形码字符串
  - `barcode_info` (dict): 条形码详细信息
  - `original_url` (str): 原始URL
  - `error` (str): 错误信息（如果失败）

**条形码信息字段:**
- `type` (str): 条形码类型
- `length` (int): 条形码长度
- `country_code` (str): 国家/地区代码
- `check_digit` (str): 校验位
- `is_valid` (bool): 校验位是否有效

**示例:**
```python
result = process_barcode_url("https://example.com/barcode?code=6901234567890")
# {
#     'success': True,
#     'barcode_code': '6901234567890',
#     'barcode_info': {
#         'type': 'EAN-13',
#         'length': 13,
#         'country_code': 'CN',
#         'check_digit': '0',
#         'is_valid': False
#     },
#     'original_url': 'https://example.com/barcode?code=6901234567890',
#     'error': None
# }
```

## 批量处理示例

```python
from src.fc_module.tools.barcode_processor import get_barcode_code_from_url

# 批量处理多个URL
urls = [
    "https://example.com/barcode?code=6901234567890",
    "https://barcode.com/product/1234567890123",
    "https://shop.com/item/123456789012/details",
]

results = []
for url in urls:
    barcode_code = get_barcode_code_from_url(url)
    results.append({
        'url': url,
        'barcode': barcode_code,
        'success': bool(barcode_code)
    })

# 统计成功率
success_count = sum(1 for r in results if r['success'])
print(f"成功提取: {success_count}/{len(results)}")
```

## 错误处理

函数具有完善的错误处理机制：

- 无效URL格式
- 无法提取条形码
- 条形码长度不符合标准
- 网络请求异常

```python
# 错误处理示例
result = process_barcode_url("https://invalid-url.com/no-barcode")
if not result['success']:
    print(f"处理失败: {result['error']}")
```

## 测试

运行测试脚本：

```bash
python test_barcode_processor.py
python barcode_example.py
```

## 注意事项

1. **条形码校验**: 函数会验证条形码的校验位，但某些测试用的条形码可能校验失败
2. **URL格式**: 支持常见的URL格式，如果有特殊格式需求可以扩展正则表达式
3. **性能**: 函数针对单个URL优化，批量处理时建议使用异步方式
4. **国家代码**: 目前支持中国(CN)和美国/加拿大(US/CA)的识别，可根据需要扩展

## 许可证

本工具模块遵循项目的整体许可证。
