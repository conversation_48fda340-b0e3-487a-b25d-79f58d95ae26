# 条码查询MCP服务集成

这个模块提供了完整的条形码识别和商品信息查询功能，支持从图片识别条形码并通过MCP服务查询详细的商品信息。

## 🚀 功能特性

- **图片条形码识别**: 从图片URL中自动识别条形码
- **商品信息查询**: 通过MCP服务查询详细商品信息
- **一体化处理**: 从图片到商品信息的完整流程
- **时间预估**: 智能预估处理时间
- **批量处理**: 支持批量条码查询
- **错误处理**: 完善的错误处理和重试机制

## 📋 MCP服务配置

### 服务规格
- **服务ID**: `cmapi01`
- **请求方法**: POST
- **请求格式**: JSON
- **响应格式**: 符合提供的示例格式

### 配置示例
```json
{
  "mcpServers": {
    "cmapi011806": {
      "url": "YOUR_MCP_SERVER_URL",
      "type": "streamableHttp"
    }
  }
}
```

## 🔧 安装和配置

### 1. 依赖安装
```bash
# 使用uv安装依赖
uv add Pillow pyzbar opencv-python requests

# 或使用pip
pip install Pillow pyzbar opencv-python requests

# macOS需要安装zbar系统库
brew install zbar
```

### 2. 服务配置
```python
from src.fc_module.tools.image_barcode_processor import configure_barcode_query_service

# 配置MCP服务器URL
configure_barcode_query_service("http://your-mcp-server.com/api")
```

## 📖 使用方法

### 基础用法

#### 1. 仅识别条形码
```python
from src.fc_module.tools.image_barcode_processor import get_barcode_from_image

image_url = "https://example.com/barcode-image.png"
barcode = get_barcode_from_image(image_url)
print(f"条形码: {barcode}")
```

#### 2. 查询商品信息
```python
from src.fc_module.tools.image_barcode_processor import query_barcode_info

result = query_barcode_info("6973497203336")
if result['success']:
    product = result['product_info']
    print(f"商品名称: {product['name']}")
    print(f"品牌: {product['brand']}")
    print(f"生产商: {product['company']}")
```

#### 3. 一体化处理
```python
from src.fc_module.tools.image_barcode_processor import get_barcode_from_image_and_query_info

image_url = "https://example.com/product-image.png"
result = get_barcode_from_image_and_query_info(image_url)

if result['success']:
    barcode = result['final_result']['barcode']
    product = result['final_result']['product_info']
    
    print(f"条形码: {barcode}")
    print(f"商品: {product['name']}")
    print(f"品牌: {product['brand']}")
```

### 高级用法

#### 带时间预估的处理
```python
from src.fc_module.tools.image_barcode_processor import get_barcode_from_image_with_timing

result = get_barcode_from_image_with_timing(image_url)
print(f"条形码: {result['barcode_code']}")
print(f"预估准确度: {result['timing']['comparison']['accuracy_percentage']:.1f}%")
```

#### 批量处理
```python
barcodes = ["6973497203336", "6975512173235", "1234567890123"]

for barcode in barcodes:
    result = query_barcode_info(barcode)
    if result['success']:
        print(f"{barcode}: {result['product_info']['name']}")
    else:
        print(f"{barcode}: 查询失败")
```

## 📊 返回数据格式

### 商品信息字段
```python
{
    'barcode': '6973497203336',
    'name': '茉莉龙井茉莉花味龙井茶饮品 970ml',
    'english_name': '',
    'brand': '果子熟了',
    'type': '970毫升',
    'origin_country': '中国',
    'company': '菓子熟了(南京)食品有限公司',
    'net_content': '',
    'net_weight': '',
    'dimensions': {
        'width': '',
        'height': '',
        'depth': ''
    },
    'picture_url': 'http://api.jisuapi.com/barcode2/upload/202504/20091953_18647.png',
    'keyword': '茉莉龙井茉莉花味龙井茶饮品',
    'description': '',
    # ... 更多字段
}
```

## 🔍 API参考

### 核心函数

| 函数名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `configure_barcode_query_service` | `server_url: str` | `None` | 配置MCP服务器地址 |
| `get_barcode_from_image` | `image_url: str` | `str` | 从图片识别条形码 |
| `query_barcode_info` | `barcode: str` | `Dict` | 查询条码商品信息 |
| `get_barcode_from_image_and_query_info` | `image_url: str` | `Dict` | 一体化处理流程 |

### 时间预估函数

| 函数名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `estimate_processing_time` | `image_url: str` | `Dict` | 预估处理时间 |
| `get_barcode_from_image_with_timing` | `image_url: str` | `Dict` | 带时间统计的识别 |

## 🧪 测试

### 运行测试
```bash
# 基础功能测试
python test_final_barcode_recognition.py

# 时间预估测试
python test_barcode_with_timing.py

# MCP服务测试（需要先配置URL）
python test_barcode_query_service.py

# 使用示例
python barcode_query_example.py
```

### 测试图片
项目包含测试图片URL：
```
https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png
```
识别结果：`6975512173235`

## ⚠️ 注意事项

1. **MCP服务器配置**: 必须先配置MCP服务器URL才能使用查询功能
2. **网络连接**: 需要稳定的网络连接来下载图片和查询服务
3. **图片格式**: 支持PNG、JPEG等常见格式
4. **条形码类型**: 支持EAN13、EAN8、UPC-A、Code128等
5. **系统依赖**: macOS需要安装zbar系统库

## 🔧 故障排除

### 常见问题

1. **pyzbar导入失败**
   ```bash
   # macOS
   brew install zbar
   
   # Ubuntu/Debian
   sudo apt-get install libzbar0
   ```

2. **MCP服务连接失败**
   - 检查服务器URL是否正确
   - 确认网络连接
   - 验证服务器是否支持cmapi01接口

3. **图片下载失败**
   - 检查图片URL是否可访问
   - 确认网络连接
   - 尝试使用不同的图片URL

## 📝 更新日志

- **v1.0.0**: 基础条形码识别功能
- **v1.1.0**: 添加时间预估功能
- **v1.2.0**: 集成MCP服务商品查询
- **v1.3.0**: 添加一体化处理流程

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
