# 🔧 OAuth2.0回调地址配置指南

## 📋 概述

要获取access_token，需要配置一个回调地址来接收授权码。本指南提供了几种配置方法。

## 🚀 方法1: 使用本地回调服务器（推荐）

### 步骤1: 启动本地服务器
```bash
python local_callback_server.py
```

### 步骤2: 配置回调地址
- **回调URL**: `http://localhost:8080/callback`
- **端口**: 8080（可修改）
- **自动处理**: 服务器会自动提取授权码并获取token

### 步骤3: 完成授权
1. 服务器启动后会自动打开浏览器
2. 登录值得买账号并授权
3. 系统自动重定向到本地服务器
4. 服务器自动获取并显示access_token

### 优点
- ✅ 完全自动化
- ✅ 实时获取token
- ✅ 自动保存到文件
- ✅ 友好的Web界面

## 🌐 方法2: 使用在线回调服务

### 可用的在线服务
1. **httpbin.org**
   - 回调URL: `https://httpbin.org/get`
   - 查看参数: 访问返回的JSON中的args字段

2. **webhook.site**
   - 访问: https://webhook.site
   - 获取唯一URL作为回调地址
   - 实时查看回调数据

3. **requestbin.com**
   - 创建临时接收地址
   - 查看收到的请求参数

### 使用步骤
```python
from src.fc_module.tools.zhidemai_info import generate_authorization_url

# 使用在线服务作为回调地址
callback_url = "https://webhook.site/your-unique-id"
auth_url = generate_authorization_url(callback_url)
print(f"请访问: {auth_url}")
```

## 📱 方法3: 手动提取授权码

### 步骤1: 使用任意回调地址
```python
# 可以使用任何URL，即使无法访问
callback_url = "https://example.com/callback"
auth_url = generate_authorization_url(callback_url)
```

### 步骤2: 完成授权
1. 访问生成的授权URL
2. 完成登录和授权
3. 浏览器会跳转到回调地址（可能显示错误页面）

### 步骤3: 从URL提取授权码
授权后的URL格式：
```
https://example.com/callback?code=AUTHORIZATION_CODE&state=STATE_VALUE
```

从URL中复制`code=`后面的值

### 步骤4: 手动获取token
```python
from src.fc_module.tools.zhidemai_info import get_access_token_by_code

code = "从URL中提取的授权码"
token_response = get_access_token_by_code(code)
print(token_response)
```

## 🔧 开发者平台配置

### 重要提醒
无论使用哪种方法，都需要确保：

1. **回调地址一致性**
   - 代码中使用的回调地址
   - 必须与开发者平台注册的地址完全一致

2. **HTTPS要求**
   - 生产环境建议使用HTTPS回调地址
   - 本地开发可以使用HTTP

3. **端口配置**
   - 如果使用特定端口，确保端口可访问
   - 防火墙和路由器设置

## 📝 完整示例

### 使用本地服务器的完整流程

```bash
# 1. 启动本地回调服务器
python local_callback_server.py

# 2. 按提示完成授权（自动打开浏览器）

# 3. 查看生成的token文件
cat access_token.txt

# 4. 使用token测试API
python -c "
from src.fc_module.tools.zhidemai_info import get_haojia_detail_simple
with open('access_token.txt', 'r') as f:
    token = f.readline().split('=')[1].strip()
data = get_haojia_detail_simple('13284568', token)
print(data)
"
```

### 手动方式的完整流程

```python
#!/usr/bin/env python3
from src.fc_module.tools.zhidemai_info import *

# 1. 生成授权URL
callback_url = "https://example.com/callback"
auth_url = generate_authorization_url(callback_url, "test_state")
print(f"请访问: {auth_url}")

# 2. 用户手动完成授权并提取code
code = input("请输入从回调URL中提取的授权码: ")

# 3. 获取access_token
token_response = get_access_token_by_code(code)
if token_response and token_response.get("error_code") == "0":
    token_info = parse_access_token_response(token_response)
    access_token = token_info['access_token']
    print(f"获取成功! Token: {access_token}")
    
    # 4. 测试API调用
    data = get_haojia_detail_simple("13284568", access_token)
    if data:
        print(f"API测试成功: {data.get('title')}")
else:
    print("获取token失败")
```

## 🛠️ 故障排除

### 常见问题

1. **404 Not Found**
   - 检查回调地址是否正确
   - 确认服务器是否正在运行

2. **403 Forbidden**
   - 检查APP_KEY和APP_SECRET
   - 确认应用是否有相应权限

3. **授权码过期**
   - 授权码通常5-10分钟内有效
   - 需要重新进行授权流程

4. **回调地址不匹配**
   - 确保代码中的回调地址与开发者平台注册的完全一致
   - 注意HTTP/HTTPS、端口号、路径等细节

### 调试技巧

1. **查看详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **使用curl测试**
   ```bash
   curl -X POST "https://openapi.smzdm.com/v1/oauth/check/code" \
        -d "code=YOUR_CODE&app_secret=YOUR_SECRET"
   ```

3. **检查网络连接**
   ```bash
   ping openapi.smzdm.com
   ```

## 📚 相关文档

- [OAuth2.0官方文档](https://openapi.zhidemai.com/pages/rookie/3.OAuth2.0%E8%B0%83%E7%94%A8%E8%AF%A6%E8%A7%A3.html)
- [获取access_token接口](https://openapi.zhidemai.com/pages/oauth2/1.%E6%A0%B9%E6%8D%AEcode%E8%8E%B7%E5%8F%96access_token.html)
- [RFC 6749 OAuth2.0规范](https://tools.ietf.org/html/rfc6749)
