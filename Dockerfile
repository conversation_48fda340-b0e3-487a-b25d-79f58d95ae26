# 使用官方 Python 3.13 镜像作为基础镜像
FROM spotlight-registry.cn-beijing.cr.aliyuncs.com/spotlight-middleware/python:3.13-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 安装 uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

# 设置环境变量
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 复制 uv 配置文件
COPY pyproject.toml uv.lock ./

# 使用 uv 安装依赖
RUN uv sync --frozen --no-cache

# 复制应用代码
COPY . .

# 创建必要的目录
# RUN mkdir -p logs sft-logs

# 环境变量
ENV PYTHONPATH /app

# 暴露端口
EXPOSE 8000

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health/check || exit 1

# 使用 uv 运行应用
CMD ["uv", "run", "uvicorn", "main:app"]
