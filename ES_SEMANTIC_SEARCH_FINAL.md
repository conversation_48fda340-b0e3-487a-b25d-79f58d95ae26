# ES语义检索查询语句和降级数据计算 - 完整解决方案

## 🎯 项目概述
基于现有的`llm_router_evaluation_results`索引，提供完整的Elasticsearch语义检索查询语句和可降级数据计算方法。

## 📊 数据模型
索引: `llm_router_evaluation_results`
- `userContent`: 用户查询文本
- `userContentEmb`: 用户查询的1024维向量
- `userContextContent`: 用户上下文内容
- `userContextContentEmb`: 用户上下文的1024维向量
- `functionCallingResponse`: 函数调用响应
- `candidateList`: 候选模型列表
- `downgradable`: 是否可降级 (0=不可降级, 1=可降级)

## 🔍 语义检索查询语句

### 1. 基础语义检索
```json
POST /llm_router_evaluation_results/_search
{
  "size": 10,
  "_source": [
    "userId", "requestId", "userContent", "userContextContent",
    "functionCallingResponse", "candidateList", "downgradable"
  ],
  "query": {
    "script_score": {
      "query": {"match_all": {}},
      "script": {
        "source": "cosineSimilarity(params.query_vector, 'userContentEmb') + 1.0",
        "params": {"query_vector": [/* 1024维向量 */]}
      }
    }
  },
  "min_score": 1.7
}
```

### 2. 上下文增强语义检索
```json
POST /llm_router_evaluation_results/_search
{
  "size": 10,
  "_source": [
    "userId", "requestId", "userContent", "userContextContent",
    "functionCallingResponse", "candidateList", "downgradable"
  ],
  "query": {
    "script_score": {
      "query": {"match_all": {}},
      "script": {
        "source": """
          Math.max(
            cosineSimilarity(params.query_vector, 'userContentEmb'),
            cosineSimilarity(params.query_vector, 'userContextContentEmb')
          ) + 1.0
        """,
        "params": {"query_vector": [/* 1024维向量 */]}
      }
    }
  },
  "min_score": 1.7
}
```

### 3. 混合检索（文本+向量）
```json
POST /llm_router_evaluation_results/_search
{
  "size": 10,
  "_source": [
    "userId", "requestId", "userContent", "userContextContent",
    "functionCallingResponse", "candidateList", "downgradable"
  ],
  "query": {
    "bool": {
      "should": [
        {"match": {"userContent": "查询文本"}},
        {"match": {"userContextContent": "查询文本"}},
        {
          "script_score": {
            "query": {"match_all": {}},
            "script": {
              "source": "cosineSimilarity(params.query_vector, 'userContentEmb') + 1.0",
              "params": {"query_vector": [/* 1024维向量 */]}
            }
          }
        }
      ]
    }
  }
}
```

## 📈 可降级数据计算

### 1. 总体统计查询
```json
POST /llm_router_evaluation_results/_search
{
  "size": 0,
  "aggs": {
    "total_records": {"value_count": {"field": "userId"}},
    "downgradable_stats": {"terms": {"field": "downgradable", "size": 10}},
    "candidate_models": {"terms": {"field": "candidateList.keyword", "size": 50}}
  }
}
```

### 2. 按查询类型统计
```json
POST /llm_router_evaluation_results/_search
{
  "size": 0,
  "query": {
    "bool": {
      "should": [
        {"match": {"userContent": "打车"}},
        {"match": {"userContextContent": "打车"}}
      ]
    }
  },
  "aggs": {
    "total_records": {"value_count": {"field": "userId"}},
    "downgradable_stats": {"terms": {"field": "downgradable", "size": 10}}
  }
}
```

## 🐍 Python实现

### 完整代码实现
```python
import os
import json
from openai import OpenAI
from fc_module.utils.elastic_search_service import ElasticsearchService

class ESSemanticSearchGenerator:
    def __init__(self):
        self.es_service = ElasticsearchService(index_name="llm_router_evaluation_results")
        self.embedding_client
