# ES语义检索查询语句和降级数据计算

## 概述
本文档提供了完整的Elasticsearch语义检索查询语句和可降级数据计算方法，基于现有的`llm_router_evaluation_results`索引。

## 1. 语义检索查询语句

### 1.1 基础语义检索查询
```json
{
  "size": 10,
  "_source": [
    "userId", "requestId", "userContent", "userContextContent",
    "functionCallingResponse", "candidateList", "downgradable"
  ],
  "query": {
    "script_score": {
      "query": {"match_all": {}},
      "script": {
        "source": "cosineSimilarity(params.query_vector, 'userContentEmb') + 1.0",
        "params": {"query_vector": [/* 1024维向量 */]}
      }
    }
  },
  "min_score": 1.7
}
```

### 1.2 混合检索查询（文本+向量）
```json
{
  "size": 10,
  "_source": [
    "userId", "requestId", "userContent", "userContextContent",
    "functionCallingResponse", "candidateList", "downgradable"
  ],
  "query": {
    "bool": {
      "should": [
        {"match": {"userContent": "查询文本"}},
        {"match": {"userContextContent": "查询文本"}},
        {
          "script_score": {
            "query": {"match_all": {}},
            "script": {
              "source": "cosineSimilarity(params.query_vector, 'userContentEmb') + 1.0",
              "params": {"query_vector": [/* 1024维向量 */]}
            }
          }
        }
      ]
    }
  }
}
```

## 2. 可降级数据计算查询

### 2.1 总体统计
```json
{
  "size": 0,
  "aggs": {
    "total_records": {"value_count": {"field": "userId"}},
    "downgradable_stats": {"terms": {"field": "downgradable", "size": 10}},
    "candidate_models": {"terms": {"field": "candidateList.keyword", "size": 50}}
  }
}
```

### 2.2 按查询类型统计
```json
{
  "size": 0,
  "query": {
    "bool": {
      "should": [
        {"match": {"userContent": "打车"}},
        {"match": {"userContextContent": "打车"}}
      ]
    }
  },
  "aggs": {
    "total_records": {"value_count": {"field": "userId"}},
    "downgradable_stats": {"terms": {"field": "downgradable", "size": 10}}
  }
}
```

## 3. Python实现代码

### 3.1 语义检索查询生成器
```python
from fc_module.utils.elastic_search_service import ElasticsearchService
from openai import OpenAI

class ESSemanticSearchGenerator:
    def __init__(self):
        self.es_service = ElasticsearchService(index_name="llm_router_evaluation_results")
        self.embedding_client = OpenAI(
            api_key="your-api-key",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
    
    def generate_semantic_search_query(self, query_text: str, k: int = 10):
        """生成语义检索查询"""
        query_embedding = self.create_embedding(query_text)
        return {
            "size": k,
            "query": {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'userContentEmb') + 1.0",
                        "params": {"query_vector": query_embedding}
                    }
                }
            },
            "min_score": 1.7
        }
    
    def calculate_downgradable_data(self, query_filter=None):
        """计算可降级数据量"""
        agg_query = {
            "size": 0,
            "aggs": {
                "total_records": {"value_count": {"field": "userId"}},
                "downgradable_stats": {"terms": {"field": "downgradable", "size": 10}}
            }
        }
        
        if query_filter:
            agg_query["query"] = {
                "bool": {
                    "should": [
                        {"match": {"userContent": query_filter}},
                        {"match": {"userContextContent": query_filter}}
                    ]
                }
            }
        
        # 执行查询并返回统计结果
        response = self.es_service.client.search(index="llm_router_evaluation_results", body=agg_query)
