# Zhidemai OAuth2.0 授权流程使用指南

## 📋 概述

本指南详细说明如何使用封装的函数完成Zhidemai开放平台的OAuth2.0授权流程，获取access_token用于API调用。

## 🔐 OAuth2.0授权码模式流程

### 流程图
```
用户 -> 应用 -> 授权服务器 -> 用户 -> 应用 -> API服务器
 |      |         |          |      |        |
 |   生成授权URL   |       用户授权   |    获取token
 |      |      访问授权页面    |      |        |
 |      |         |       返回code   |    调用API
```

### 详细步骤

#### 1️⃣ 生成授权URL
```python
from src.fc_module.tools.zhidemai_info import generate_authorization_url

# 配置回调地址（必须与开发者平台注册的地址一致）
redirect_uri = "https://your-app.com/callback"
state = "random_state_string"  # 可选，用于防CSRF攻击

# 生成授权URL
auth_url = generate_authorization_url(redirect_uri, state)
print(f"请访问: {auth_url}")
```

#### 2️⃣ 用户授权
用户在浏览器中访问授权URL，完成以下操作：
- 登录值得买账号
- 确认授权应用访问权限
- 系统自动重定向到回调地址

#### 3️⃣ 获取授权码
从回调URL中提取code参数：
```
https://your-app.com/callback?code=AUTHORIZATION_CODE&state=random_state_string
```

#### 4️⃣ 交换access_token
```python
from src.fc_module.tools.zhidemai_info import (
    get_access_token_by_code,
    parse_access_token_response,
    is_access_token_valid
)

# 使用授权码获取token
code = "AUTHORIZATION_CODE_FROM_CALLBACK"
token_response = get_access_token_by_code(code)

if token_response and token_response.get("error_code") == "0":
    # 解析token信息
    token_info = parse_access_token_response(token_response)
    
    access_token = token_info['access_token']
    expires_in = token_info['expires_in']  # 7200秒
    union_id = token_info['union_id']      # 用户唯一标识
    
    print(f"获取成功! access_token: {access_token}")
else:
    error_msg = token_response.get("error_msg", "未知错误")
    print(f"获取失败: {error_msg}")
```

#### 5️⃣ 使用access_token调用API
```python
from src.fc_module.tools.zhidemai_info import get_haojia_detail_simple

# 检查token有效性
if is_access_token_valid(token_info):
    # 调用API
    article_data = get_haojia_detail_simple("13284568", access_token)
    if article_data:
        print(f"文章标题: {article_data.get('title')}")
else:
    print("Token已过期，需要重新获取")
```

## 🛠️ 核心函数说明

### `generate_authorization_url(redirect_uri, state=None)`
生成OAuth2.0授权URL

**参数:**
- `redirect_uri`: 回调地址，必须与开发者平台注册一致
- `state`: 可选状态参数，用于防CSRF攻击

**返回:** 授权URL字符串

### `get_access_token_by_code(code, app_secret=None)`
使用授权码获取access_token

**参数:**
- `code`: 用户授权后获取的授权码
- `app_secret`: 应用密钥，可选（默认使用全局配置）

**返回:** API响应数据字典

### `parse_access_token_response(response_data)`
解析access_token响应数据

**参数:**
- `response_data`: API响应数据

**返回:** 包含token信息的字典

### `is_access_token_valid(token_info)`
检查access_token是否有效

**参数:**
- `token_info`: 包含expires_at字段的token信息

**返回:** 布尔值，True表示有效

## 🧪 测试和演示

### 运行交互式演示
```bash
python test_oauth2_flow.py
# 选择 "1. 交互式OAuth2.0演示"
```

### 运行自动化测试
```bash
python test_oauth2_flow.py
# 选择 "2. 自动化测试套件"
```

### 运行完整流程演示
```bash
python test_oauth2_flow.py
# 选择 "3. 完整流程演示"
```

## ⚠️ 注意事项

### 安全性
- 妥善保管APP_KEY和APP_SECRET
- 使用HTTPS回调地址
- 验证state参数防止CSRF攻击
- 安全存储access_token

### Token管理
- access_token有效期为7200秒（2小时）
- 建议提前5分钟刷新token
- 记录union_id用于用户身份识别
- 在每次API调用前检查token有效性

### 错误处理
常见错误码及解决方案：

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 200002 | code empty | 检查授权码是否正确输入 |
| 200003 | invalid code | 授权码无效或已过期，重新获取 |
| 200004 | code is inconsistent | 授权码与APP_KEY不匹配 |

## 📚 完整示例

```python
#!/usr/bin/env python3
"""
完整的OAuth2.0授权流程示例
"""

from src.fc_module.tools.zhidemai_info import (
    generate_authorization_url,
    get_access_token_by_code,
    parse_access_token_response,
    is_access_token_valid,
    get_haojia_detail_simple
)

def main():
    # 1. 生成授权URL
    redirect_uri = "https://your-app.com/callback"
    auth_url = generate_authorization_url(redirect_uri, "state123")
    
    print(f"请访问授权URL: {auth_url}")
    
    # 2. 用户完成授权后，从回调URL获取code
    code = input("请输入授权码: ")
    
    # 3. 获取access_token
    token_response = get_access_token_by_code(code)
    
    if token_response and token_response.get("error_code") == "0":
        token_info = parse_access_token_response(token_response)
        access_token = token_info['access_token']
        
        print(f"获取成功! Token: {access_token[:20]}...")
        
        # 4. 使用token调用API
        if is_access_token_valid(token_info):
            data = get_haojia_detail_simple("13284568", access_token)
            if data:
                print(f"API调用成功: {data.get('title')}")
        
    else:
        print("获取access_token失败")

if __name__ == "__main__":
    main()
```

## 🔗 相关文档

- [官方OAuth2.0文档](https://openapi.zhidemai.com/pages/rookie/3.OAuth2.0%E8%B0%83%E7%94%A8%E8%AF%A6%E8%A7%A3.html)
- [获取access_token接口](https://openapi.zhidemai.com/pages/oauth2/1.%E6%A0%B9%E6%8D%AEcode%E8%8E%B7%E5%8F%96access_token.html)
- [RFC 6749 OAuth2.0规范](https://tools.ietf.org/html/rfc6749)
