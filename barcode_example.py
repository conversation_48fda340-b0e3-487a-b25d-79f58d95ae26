#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
条形码处理器使用示例
演示如何从商品条形码链接中提取条形码code
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from src.fc_module.tools.barcode_processor import get_barcode_code_from_url, process_barcode_url


def simple_example():
    """简单使用示例"""
    print("=== 简单使用示例 ===\n")
    
    # 示例条形码链接
    barcode_urls = [
        "https://example.com/barcode?code=6901234567890",
        "https://barcode.com/product/1234567890123",
        "https://shop.com/item/123456789012/details",
        "https://api.barcode.com/lookup/barcode_12345678",
    ]
    
    for url in barcode_urls:
        # 使用简化版函数直接获取条形码code
        barcode_code = get_barcode_code_from_url(url)
        print(f"链接: {url}")
        print(f"条形码: {barcode_code}")
        print()


def detailed_example():
    """详细使用示例"""
    print("=== 详细使用示例 ===\n")
    
    # 示例条形码链接
    url = "https://example.com/barcode?code=6901234567890"
    
    # 使用完整版函数获取详细信息
    result = process_barcode_url(url)
    
    print(f"处理链接: {url}")
    print(f"处理结果:")
    
    if result['success']:
        print(f"  ✅ 成功提取条形码")
        print(f"  条形码: {result['barcode_code']}")
        print(f"  类型: {result['barcode_info']['type']}")
        print(f"  长度: {result['barcode_info']['length']}")
        print(f"  校验: {'有效' if result['barcode_info']['is_valid'] else '无效'}")
        
        if result['barcode_info']['country_code']:
            print(f"  国家: {result['barcode_info']['country_code']}")
    else:
        print(f"  ❌ 处理失败: {result['error']}")


def batch_processing_example():
    """批量处理示例"""
    print("=== 批量处理示例 ===\n")
    
    # 批量条形码链接
    urls = [
        "https://example.com/barcode?code=6901234567890",
        "https://barcode.com/product/1234567890123", 
        "https://shop.com/item/123456789012/details",
        "https://invalid-url.com/no-barcode",
        "https://api.barcode.com/lookup/barcode_12345678",
    ]
    
    print("批量提取条形码:")
    results = []
    
    for i, url in enumerate(urls, 1):
        barcode_code = get_barcode_code_from_url(url)
        results.append({
            'url': url,
            'barcode': barcode_code,
            'success': bool(barcode_code)
        })
        
        status = "✅" if barcode_code else "❌"
        print(f"{i}. {status} {barcode_code or '提取失败'}")
    
    print(f"\n成功提取: {sum(1 for r in results if r['success'])}/{len(results)}")


def main():
    """主函数"""
    print("条形码处理器使用示例")
    print("=" * 50)
    
    simple_example()
    detailed_example()
    batch_processing_example()
    
    print("\n=== 函数说明 ===")
    print("1. get_barcode_code_from_url(url) - 简化版，直接返回条形码字符串")
    print("2. process_barcode_url(url) - 完整版，返回详细信息字典")
    print("\n支持的URL格式:")
    print("- 参数形式: https://example.com/api?code=1234567890123")
    print("- 路径形式: https://example.com/product/1234567890123")
    print("- 关键词形式: https://example.com/barcode_1234567890123")
    print("\n支持的条形码类型:")
    print("- EAN-13 (13位): 国际商品条码")
    print("- UPC-A (12位): 美国统一商品代码")
    print("- EAN-8 (8位): 短版国际商品条码")
    print("- GTIN-14 (14位): 全球贸易项目代码")
    print("- ISBN-10 (10位): 国际标准书号")


if __name__ == "__main__":
    main()
