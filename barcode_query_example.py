#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
条码查询功能使用示例
展示如何配置和使用条码查询MCP服务
"""

from src.fc_module.tools.image_barcode_processor import (
    configure_barcode_query_service,
    query_barcode_info,
    get_barcode_from_image_and_query_info
)


def setup_service():
    """设置MCP服务"""
    print("🔧 配置条码查询服务")
    print("-" * 30)
    
    # TODO: 用户需要替换为实际的MCP服务器URL
    server_url = "YOUR_MCP_SERVER_URL"
    
    print(f"📝 配置说明:")
    print(f"   1. 将 '{server_url}' 替换为您的MCP服务器地址")
    print(f"   2. 确保服务器支持 cmapi01 接口")
    print(f"   3. 服务器返回格式符合预期")
    print()
    
    if server_url != "YOUR_MCP_SERVER_URL":
        configure_barcode_query_service(server_url)
        return True
    else:
        print("⚠️  请先配置服务器URL")
        return False


def example_query_barcode():
    """示例：查询条码信息"""
    print("🔍 示例1: 查询条码信息")
    print("-" * 30)
    
    # 测试条码
    barcode = "6973497203336"
    
    print(f"查询条码: {barcode}")
    
    result = query_barcode_info(barcode)
    
    if result['success']:
        product = result['product_info']
        
        print("✅ 查询成功!")
        print(f"商品名称: {product.get('name', 'N/A')}")
        print(f"品牌: {product.get('brand', 'N/A')}")
        print(f"规格: {product.get('type', 'N/A')}")
        print(f"产地: {product.get('origin_country', 'N/A')}")
        print(f"生产商: {product.get('company', 'N/A')}")
        
        if product.get('picture_url'):
            print(f"商品图片: {product['picture_url']}")
            
    else:
        print(f"❌ 查询失败: {result.get('error')}")
    
    print()


def example_image_to_product():
    """示例：从图片到商品信息"""
    print("🖼️  示例2: 从图片识别到商品信息")
    print("-" * 30)
    
    # 测试图片URL
    image_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    print(f"处理图片: {image_url}")
    print("正在识别条码并查询商品信息...")
    
    result = get_barcode_from_image_and_query_info(image_url)
    
    if result['success']:
        barcode = result['final_result']['barcode']
        product = result['final_result']['product_info']
        
        print("✅ 完整流程成功!")
        print(f"识别的条码: {barcode}")
        print(f"商品名称: {product.get('name', 'N/A')}")
        print(f"品牌: {product.get('brand', 'N/A')}")
        
    else:
        print(f"❌ 流程失败: {result.get('error')}")
        
        # 显示详细错误信息
        if 'barcode_recognition' in result:
            br = result['barcode_recognition']
            print(f"条码识别: {'成功' if br['success'] else '失败'}")
            if br['success']:
                print(f"识别的条码: {br['barcode']}")
        
        if 'product_query' in result:
            pq = result['product_query']
            print(f"商品查询: {'成功' if pq['success'] else '失败'}")
            if not pq['success']:
                print(f"查询错误: {pq.get('error')}")
    
    print()


def show_api_reference():
    """显示API参考"""
    print("📚 API参考")
    print("-" * 30)
    
    api_docs = """
🔧 配置函数:
   configure_barcode_query_service(server_url: str)
   - 配置MCP服务器地址
   - 必须在查询前调用

🔍 查询函数:
   query_barcode_info(barcode: str) -> Dict
   - 输入: 条形码字符串
   - 输出: 包含商品信息的字典
   - 返回字段: success, product_info, error

🖼️  一体化函数:
   get_barcode_from_image_and_query_info(image_url: str) -> Dict
   - 输入: 图片URL
   - 输出: 条码识别 + 商品查询的完整结果
   - 自动处理整个流程

📊 返回的商品信息字段:
   - name: 商品名称
   - brand: 品牌
   - type: 规格/型号
   - origin_country: 产地国家
   - company: 生产商
   - picture_url: 商品图片URL
   - net_weight: 净重
   - dimensions: 尺寸信息
   - 等等...

🔧 MCP服务配置:
   服务ID: cmapi01
   请求格式: {"method": "cmapi01", "params": {"barcode": "条码"}}
   响应格式: 符合您提供的示例格式
"""
    
    print(api_docs)


def show_integration_example():
    """显示集成示例"""
    print("🔗 集成示例")
    print("-" * 30)
    
    integration_code = '''
# 完整的集成示例
from src.fc_module.tools.image_barcode_processor import (
    configure_barcode_query_service,
    get_barcode_from_image_and_query_info
)

def process_product_image(image_url: str):
    """处理商品图片，返回完整商品信息"""
    
    # 1. 配置服务（只需要配置一次）
    configure_barcode_query_service("http://your-mcp-server.com/api")
    
    # 2. 处理图片
    result = get_barcode_from_image_and_query_info(image_url)
    
    if result['success']:
        return {
            'barcode': result['final_result']['barcode'],
            'product_name': result['final_result']['product_info']['name'],
            'brand': result['final_result']['product_info']['brand'],
            'company': result['final_result']['product_info']['company'],
            'origin': result['final_result']['product_info']['origin_country']
        }
    else:
        return {'error': result['error']}

# 使用示例
image_url = "https://example.com/product-image.png"
product_info = process_product_image(image_url)
print(product_info)
'''
    
    print(integration_code)


def main():
    """主函数"""
    print("🚀 条码查询功能使用示例")
    print("=" * 50)
    
    # 设置服务
    if not setup_service():
        print("请先配置MCP服务器URL，然后重新运行此示例")
        show_api_reference()
        show_integration_example()
        return
    
    # 运行示例
    example_query_barcode()
    example_image_to_product()
    show_api_reference()
    show_integration_example()
    
    print("🎉 示例演示完成!")
    print("现在您可以根据需要配置自己的MCP服务器URL并使用这些功能。")


if __name__ == "__main__":
    main()
