#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
尝试绕过知道创宇云防御(CloudWAF)的拦截
使用不同的请求头和方法
"""

import requests
import json
import time
import hashlib
import random
from urllib.parse import urlencode

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def get_random_user_agent():
    """获取随机User-Agent"""
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    ]
    return random.choice(user_agents)

def get_bypass_headers():
    """获取绕过WAF的请求头"""
    headers = {
        'User-Agent': get_random_user_agent(),
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'Origin': 'https://www.smzdm.com',
        'Referer': 'https://www.smzdm.com/',
        'X-Requested-With': 'XMLHttpRequest'
    }
    return headers

def try_waf_bypass_oauth():
    """尝试绕过WAF进行OAuth2请求"""
    print("🛡️ 尝试绕过CloudWAF进行OAuth2认证")
    print("=" * 50)
    
    # 尝试不同的请求方式
    methods = [
        ("标准请求头", get_bypass_headers()),
        ("简化请求头", {'User-Agent': get_random_user_agent()}),
        ("移动端请求头", {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Accept': 'application/json'
        }),
        ("API客户端请求头", {
            'User-Agent': 'SMZDM-API-Client/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
    ]
    
    # 测试token交换端点
    token_url = "https://openapi.smzdm.com/v1/oauth/check/code"
    
    for method_name, headers in methods:
        print(f"\n🔧 测试方法: {method_name}")
        print(f"请求头: {headers}")
        
        # 模拟有效的授权码参数
        params = {
            "app_key": APP_KEY,
            "code": "test_code_12345",  # 模拟授权码
            "app_secret": APP_SECRET
        }
        
        try:
            # 添加随机延迟避免频率限制
            time.sleep(random.uniform(1, 3))
            
            response = requests.post(token_url, data=params, headers=headers, timeout=15)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code != 403:
                print(f"✅ 成功绕过WAF! 状态码: {response.status_code}")
                
                try:
                    result = response.json()
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    # 检查是否是有效的API响应
                    if "error_code" in result:
                        print("🎉 找到有效的API响应格式!")
                        return True, headers
                        
                except json.JSONDecodeError:
                    print(f"响应内容: {response.text[:200]}...")
                    
            else:
                print("❌ 仍被WAF拦截")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return False, None

def try_different_endpoints():
    """尝试不同的API端点"""
    print("\n🔍 测试不同的API端点")
    print("=" * 40)
    
    headers = get_bypass_headers()
    
    # 尝试不同的域名和端点
    test_endpoints = [
        ("主站API", "https://api.smzdm.com/v1/oauth/check/code"),
        ("开放平台", "https://openapi.smzdm.com/v1/oauth/check/code"),
        ("移动端API", "https://m-api.smzdm.com/v1/oauth/check/code"),
        ("CDN端点", "https://cdn.smzdm.com/v1/oauth/check/code"),
        ("备用域名", "https://zhidemai.com/api/v1/oauth/check/code")
    ]
    
    params = {
        "app_key": APP_KEY,
        "code": "test_code_12345",
        "app_secret": APP_SECRET
    }
    
    for name, endpoint in test_endpoints:
        print(f"\n测试 {name}: {endpoint}")
        
        try:
            time.sleep(random.uniform(1, 2))
            response = requests.post(endpoint, data=params, headers=headers, timeout=10)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code != 403:
                print(f"✅ 端点可用: {endpoint}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)[:300]}...")
                        return endpoint
                    except:
                        print(f"响应内容: {response.text[:200]}...")
                        
            else:
                print("❌ 被WAF拦截")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    return None

def try_proxy_request():
    """尝试通过代理请求"""
    print("\n🌐 尝试代理请求方式")
    print("=" * 30)
    
    # 这里可以添加代理配置
    # proxies = {
    #     'http': 'http://proxy-server:port',
    #     'https': 'https://proxy-server:port'
    # }
    
    print("💡 代理配置需要用户提供")
    print("如果你有可用的代理服务器，可以修改代码添加代理配置")
    
    return False

def manual_browser_method():
    """手动浏览器方法说明"""
    print("\n🌍 手动浏览器方法")
    print("=" * 30)
    
    print("由于WAF拦截，建议使用浏览器手动获取token:")
    print()
    print("1. 打开浏览器开发者工具 (F12)")
    print("2. 切换到 Network 标签页")
    print("3. 访问值得买网站并登录")
    print("4. 在开发者工具中查找OAuth2相关的请求")
    print("5. 复制请求的Headers和参数")
    print("6. 手动构造API请求")
    
    print("\n🔧 或者尝试以下方法:")
    print("1. 使用浏览器插件 (如Postman)")
    print("2. 使用curl命令行工具")
    print("3. 联系值得买技术支持申请IP白名单")
    
    return False

def generate_working_authorization_url():
    """生成可能工作的授权URL"""
    print("\n🔗 生成授权URL (浏览器访问)")
    print("=" * 40)
    
    redirect_uri = "https://example.com/callback"  # 使用外部回调
    state = f"manual_{int(time.time())}"
    
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    auth_url = f"https://smzdm.com/oauth2/authorize?{urlencode(params)}"
    
    print(f"授权URL: {auth_url}")
    print()
    print("📝 使用步骤:")
    print("1. 复制上面的URL到浏览器")
    print("2. 登录并完成授权")
    print("3. 从重定向的URL中获取code参数")
    print("4. 手动调用token交换接口")
    
    return auth_url

def main():
    """主函数"""
    print("🚨 CloudWAF绕过尝试工具")
    print("=" * 50)
    
    print("检测到所有API请求被知道创宇云防御拦截")
    print("错误信息: 当前访问疑似黑客攻击，已被网站管理员设置为拦截")
    print()
    
    success = False
    
    # 1. 尝试不同的请求头绕过WAF
    print("🛡️ 步骤1: 尝试绕过WAF")
    bypass_success, working_headers = try_waf_bypass_oauth()
    
    if bypass_success:
        print("✅ 成功绕过WAF!")
        success = True
    else:
        print("❌ WAF绕过失败")
    
    # 2. 尝试不同的端点
    if not success:
        print("\n🔍 步骤2: 尝试不同端点")
        working_endpoint = try_different_endpoints()
        if working_endpoint:
            print(f"✅ 找到可用端点: {working_endpoint}")
            success = True
    
    # 3. 提供手动方法
    if not success:
        print("\n📋 步骤3: 提供替代方案")
        
        # 生成授权URL供手动使用
        auth_url = generate_working_authorization_url()
        
        # 说明手动方法
        manual_browser_method()
        
        print(f"\n💡 建议:")
        print("1. 联系值得买技术支持，说明CloudWAF误拦截问题")
        print("2. 申请将你的IP地址加入白名单")
        print("3. 或者使用不同的网络环境进行测试")
        print("4. 考虑使用官方SDK或工具")

if __name__ == "__main__":
    main()
