#!/usr/bin/env python3
"""
计算当前ES索引中的可降级数据具体数值
"""

import os
import json
import sys
import pathlib

# 添加项目路径
sys.path.insert(0, str(pathlib.Path(__file__).parent / "src"))

from fc_module.utils.elastic_search_service import ElasticsearchService

def calculate_actual_stats():
    """计算实际的可降级数据数值"""
    print("=== 计算当前ES索引中的可降级数据数值 ===\n")
    
    try:
        es_service = ElasticsearchService(index_name="llm_router_evaluation_results")
        
        # 1. 总体统计
        print("1. 总体数据统计:")
        total_query = {
            "size": 0,
            "aggs": {
                "total_records": {"value_count": {"field": "userId"}},
                "downgradable_stats": {"terms": {"field": "downgradable", "size": 10}},
                "candidate_models": {"terms": {"field": "candidateList.keyword", "size": 50}}
            }
        }
        
        response = es_service.client.search(
            index=es_service.index_name,
            body=total_query
        )
        
        aggregations = response.get("aggregations", {})
        total = aggregations.get("total_records", {}).get("value", 0)
        buckets = aggregations.get("downgradable_stats", {}).get("buckets", [])
        
        downgradable = 0
        non_downgradable = 0
        
        for bucket in buckets:
            if bucket.get("key") == 1:
                downgradable = bucket.get("doc_count", 0)
            elif bucket.get("key") == 0:
                non_downgradable = bucket.get("doc_count", 0)
        
        percentage = (downgradable / total * 100) if total > 0 else 0
        
        print(f"总记录数: {total}")
        print(f"可降级数据: {downgradable}")
        print(f"不可降级数据: {non_downgradable}")
        print(f"可降级比例: {percentage:.2f}%")
        
        # 2. 按功能类型统计
        print("\n2. 按功能类型统计:")
        function_types = ["打车", "酒店", "天气", "翻译", "航班", "日程"]
        
        for func_type in function_types:
            func_query = {
                "size": 0,
                "query": {
                    "bool": {
                        "should": [
                            {"match": {"userContent": func_type}},
                            {"match": {"userContextContent": func_type}}
                        ]
                    }
                },
                "aggs": {
                    "total_records": {"value_count": {"field": "userId"}},
                    "downgradable_stats": {"terms": {"field": "downgradable", "size": 10}}
                }
            }
            
            try:
                func_response = es_service.client.search(
                    index=es_service.index_name,
                    body=func_query
                )
                
                func_aggregations = func_response.get("aggregations", {})
                func_total = func_aggregations.get("total_records", {}).get("value", 0)
                func_buckets = func_aggregations.get("downgradable_stats", {}).get("buckets", [])
                
                func_downgradable = 0
                for bucket in func_buckets:
                    if bucket.get("key") == 1:
                        func_downgradable = bucket.get("doc_count", 0)
                
                func_percentage = (func_downgradable / func_total * 100) if func_total > 0 else 0
                
                print(f"{func_type}: {func_downgradable}/{func_total} ({func_percentage:.2f}%)")
                
            except Exception as e:
                print(f"{func_type}: 查询错误 - {str(e)}")
        
        # 3. 按候选模型统计
        print("\n3. 候选模型分布:")
        model_query = {
            "size": 0,
            "aggs": {
                "candidate_models": {"terms": {"field": "candidateList.keyword", "size": 20}}
            }
        }
        
        model_response = es_service.client.search(
            index=es_service.index_name,
            body=model_query
        )
        
        model_buckets = model_response.get("aggregations", {}).get("candidate_models", {}).get("buckets", [])
        
        for bucket in model_buckets:
            model_name = bucket.get("key", "")
            count = bucket.get("doc_count", 0)
            print(f"{model_name}: {count}条记录")
        
        # 4. 总结报告
        print("\n=== 总结报告 ===")
        print(f"当前索引: {es_service.index_name}")
        print(f"总数据
