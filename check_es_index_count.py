#!/usr/bin/env python3
"""
Check the document count in llm_difficulty_aware_router_emb Elasticsearch index
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.fc_module.utils.elastic_search_service import ElasticsearchService

def check_index_count():
    """Check the document count in the Elasticsearch index"""
    print("=== Checking Elasticsearch Index Document Count ===")
    
    try:
        # Initialize Elasticsearch service with the correct index name
        es_service = ElasticsearchService(index_name="llm_difficulty_aware_router_emb")
        
        # Check if Elasticsearch is connected
        if not es_service.client or not es_service.client.ping():
            print("❌ Failed to connect to Elasticsearch")
            return
        
        print("✅ Connected to Elasticsearch")
        
        # Check if index exists
        index_name = "llm_difficulty_aware_router_emb"
        if not es_service.client.indices.exists(index=index_name):
            print(f"❌ Index '{index_name}' does not exist")
            return
        
        print(f"✅ Index '{index_name}' exists")
        
        # Get document count
        count_response = es_service.client.count(index=index_name)
        doc_count = count_response['count']
        
        print(f"📊 Document count in '{index_name}': {doc_count}")
        
        # Get index stats
        stats = es_service.client.indices.stats(index=index_name)
        if '_all' in stats['indices'][index_name]['total']:
            store_size = stats['indices'][index_name]['total']['store']['size_in_bytes']
            print(f"💾 Index size: {store_size} bytes")
        
        # Show some sample documents if there are any
        if doc_count > 0:
            print("\n=== Sample Documents ===")
            search_body = {
                "size": 3,
                "_source": ["userId", "requestId", "userContent", "updatedTime"]
            }
            search_response = es_service.client.search(index=index_name, body=search_body)
            hits = search_response['hits']['hits']
            
            for i, hit in enumerate(hits, 1):
                source = hit['_source']
                print(f"\nDocument {i}:")
                print(f"  User ID: {source.get('userId', 'N/A')}")
                print(f"  Request ID: {source.get('requestId', 'N/A')}")
                print(f"  Content: {source.get('userContent', 'N/A')[:100]}...")
                print(f"  Updated: {source.get('updatedTime', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error checking index count: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_index_count()
