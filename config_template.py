#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
值得买API配置模板
请复制此文件为config.py并填入真实的配置信息
"""

# 值得买开放平台配置
ZHIDEMAI_CONFIG = {
    # 从值得买开放平台获取的AppKey
    'app_key': 'your_app_key_here',
    
    # 从值得买开放平台获取的AppSecret  
    'app_secret': 'your_app_secret_here',
    
    # API基础URL，通常不需要修改
    'base_url': 'https://openapi.smzdm.com',
    
    # 请求超时时间（秒）
    'timeout': 30,
    
    # 是否启用调试模式
    'debug': False
}

# OAuth2.0配置（如果需要用户授权）
OAUTH_CONFIG = {
    # 授权回调地址
    'redirect_uri': 'your_redirect_uri_here',
    
    # OAuth授权URL
    'auth_url': 'https://oauth.smzdm.com/oauth/authorize',
    
    # 获取token的URL
    'token_url': 'https://oauth.smzdm.com/oauth/token'
}

# API端点配置
API_ENDPOINTS = {
    # 好价相关
    'haojia_list': 'haojia/third/list',
    'haojia_detail': 'haojia/third/info',
    'haojia_events': 'haojia/third/events',
    
    # 文章相关
    'article_detail': 'article/third/info',
    'article_list': 'article/third/list',
    'article_video_rss': 'article/third/video/rss',
    
    # 搜索相关
    'search_articles': 'search/article/list',
    'search_notes': 'search/note/list',
    'search_goods': 'search/goods/list',
    'search_haojia': 'search/haojia/list',
    
    # 分类相关
    'category_tree': 'category/tree',
    'category_query': 'category/query',
    
    # 品牌相关
    'brand_detail': 'brand/detail',
    'brand_story': 'brand/story',
    'brand_category': 'brand/category',
    
    # OAuth相关
    'oauth_token': 'oauth/token',
    'user_info': 'user/info'
}

# 默认请求参数
DEFAULT_PARAMS = {
    'page': 1,
    'page_size': 10,
    'version': '2'
}

# 错误重试配置
RETRY_CONFIG = {
    'max_retries': 3,
    'retry_delay': 1,  # 秒
    'backoff_factor': 2
}


def get_config():
    """获取配置信息"""
    return ZHIDEMAI_CONFIG


def get_oauth_config():
    """获取OAuth配置"""
    return OAUTH_CONFIG


def get_endpoints():
    """获取API端点配置"""
    return API_ENDPOINTS


def validate_config():
    """验证配置是否完整"""
    required_keys = ['app_key', 'app_secret']
    
    for key in required_keys:
        if not ZHIDEMAI_CONFIG.get(key) or ZHIDEMAI_CONFIG[key] == f'your_{key}_here':
            return False, f"请配置 {key}"
    
    return True, "配置验证通过"


if __name__ == "__main__":
    is_valid, message = validate_config()
    print(f"配置验证结果: {message}")
    
    if is_valid:
        print("✓ 配置完整，可以开始使用API")
    else:
        print("⚠️  请先完善配置信息")
        print("\n配置步骤:")
        print("1. 复制此文件为 config.py")
        print("2. 在值得买开放平台申请AppKey和AppSecret")
        print("3. 将真实的配置信息填入config.py")
        print("4. 运行测试确认配置正确")
