#!/bin/bash
# 简化的 curl 命令测试 /api/agent_flow 接口

# 基本配置
BASE_URL="http://localhost:8000"
ENDPOINT="/api/agent_flow"

# 生成测试数据
USER_ID="test_user_$(date +%s)"
REQ_ID="req_$(date +%s)"
CONVERSATION_ID="conv_$(date +%s)"

echo "=== 测试 /api/agent_flow 接口 ==="
echo "URL: ${BASE_URL}${ENDPOINT}"
echo "用户ID: ${USER_ID}"
echo "请求ID: ${REQ_ID}"
echo "会话ID: ${CONVERSATION_ID}"
echo ""

# 最基本的测试请求
echo "发送测试请求..."
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "'"${USER_ID}"'",
    "req_id": "'"${REQ_ID}"'",
    "conversation_id": "'"${CONVERSATION_ID}"'",
    "intent": "test",
    "inp": "你好，请介绍一下自己",
    "images": [],
    "coords": "39.9042,116.4074",
    "token": "test_token",
    "history_str": "",
    "hist_messages": []
  }'

echo -e "\n\n=== 测试完成 ==="
echo "如果服务正常运行，应该能看到JSON响应"
