#!/usr/bin/env python3
"""
Exact AgentFlow debug script
"""

import os
import sys
import numpy as np
from src.fc_module.utils.elastic_search_service import ElasticsearchService
from src.fc_module.agent_flow import AgentFlow
import redis
from src.fc_module.utils.supabase_service import SupabaseService
from src.fc_module.async_log_worker import AsyncLogWorker
import asyncio

def debug_agentflow_exact():
    """Debug exact AgentFlow calls"""
    print("=== Exact AgentFlow Debug ===")
    
    try:
        # Initialize services
        cache = redis.Redis(host="127.0.0.1", port=6379, db=0)
        supabase_service = SupabaseService()
        asyncio.run(supabase_service.ensure_ready())
        async_logger = AsyncLogWorker(supabase_service)
        
        # Initialize AgentFlow
        agent_flow = AgentFlow(cache, supabase_service, async_logger)
        
        print(f"AgentFlow ES index: {agent_flow.es_service.index_name}")
        
        # Test the exact same call as in difficulty_aware_route
        test_context = "用户查询: 打车去北京天安门"
        
        # Test embedding creation
        embedding = agent_flow.create_embedding(test_context)
        print(f"✅ Embedding created: {len(embedding)} dims")
        
        # Test exact ES call
        print("\n=== Testing exact ES call ===")
        try:
            results = agent_flow.es_service.search_by_dense_vector(
                vector_field="userContextContentEmb",
                query_vector=embedding,
                k=5
            )
            print(f"✅ ES search successful: {len(results)} results")
            for i, result in enumerate(results[:2]):
                print(f"  Result {i+1}: score={result.get('score_cosine', 'N/A')}")
                
        except Exception as e:
            print(f"❌ ES search failed: {e}")
            print(f"Error type: {type(e).__name__}")
            
            # Try direct client call
            print("\n=== Testing direct client call ===")
            try:
                body = {
                    "size": 5,
                    "query": {
                        "script_score": {
                            "query": {"match_all": {}},
                            "script": {
                                "source": "cosineSimilarity(params.query_vector, 'userContextContentEmb') + 1.0",
                                "params": {"query_vector": embedding}
                            }
                        }
                    }
                }
                
                response = agent_flow.es_service.client.search(
                    index=agent_flow.es_service.index_name, 
                    body=body
                )
                hits = response["hits"]["hits"]
                print(f"✅ Direct client call successful: {len(hits)} results")
                
            except Exception as e2:
                print(f"❌ Direct client call failed: {e2}")
        
        # Test difficulty_aware_route
        print("\n=== Testing difficulty_aware_route ===")
        try:
            result = agent_flow.difficulty_aware_route("测试查询", test_context)
            print(f"🎯 Route result: {result}")
        except Exception as e:
            print(f"❌ difficulty_aware_route failed: {e}")
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_agentflow_exact()
