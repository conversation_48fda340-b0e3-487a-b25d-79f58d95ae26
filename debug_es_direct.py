#!/usr/bin/env python3
"""
Direct Elasticsearch debug script
"""

import os
import json
import numpy as np
from src.fc_module.utils.elastic_search_service import ElasticsearchService

def debug_es_direct():
    """Debug Elasticsearch directly"""
    print("=== Direct Elasticsearch Debug ===")
    
    # Initialize Elasticsearch service
    es_service = ElasticsearchService(index_name="llm_difficulty_aware_router_emb")
    
    if not es_service.client or not es_service.client.ping():
        print("❌ Elasticsearch connection failed")
        return
    
    print("✅ Elasticsearch connected")
    
    # Test index
    index_name = "llm_difficulty_aware_router_emb"
    print(f"Using index: {index_name}")
    
    # Check index exists
    exists = es_service.client.indices.exists(index=index_name)
    print(f"Index exists: {exists}")
    
    if not exists:
        print("Creating index...")
        es_service.create_router_index()
    
    # Get mapping
    try:
        mapping = es_service.client.indices.get_mapping(index=index_name)
        print(f"Index mapping:")
        print(json.dumps(mapping, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"Failed to get mapping: {e}")
    
    # Test vector search with detailed error
    test_vector = np.random.rand(1024).tolist()
    print(f"\nTest vector length: {len(test_vector)}")
    
    # Test direct query
    print("\n=== Testing Direct Query ===")
    try:
        body = {
            "size": 5,
            "_source": ["userId", "requestId", "userContent", "userContextContent", "updatedTime"],
            "query": {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'userContextContentEmb') + 1.0",
                        "params": {"query_vector": test_vector}
                    }
                }
            }
        }
        
        print("Query body:")
        print(json.dumps(body, indent=2, ensure_ascii=False))
        
        response = es_service.client.search(index=index_name, body=body)
        hits = response["hits"]["hits"]
        print(f"✅ Direct query successful: {len(hits)} results")
        
    except Exception as e:
        print(f"❌ Direct query failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Try to get more details
        try:
            # Test with explain
            explain_body = {
                "size": 1,
                "explain": True,
                "query": {
                    "script_score": {
                        "query": {"match_all": {}},
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'userContextContentEmb') + 1.0",
                            "params": {"query_vector": test_vector}
                        }
                    }
                }
            }
            
            response = es_service.client.search(index=index_name, body=explain_body)
            print("Explain response:", response)
            
        except Exception as e2:
            print(f"Explain also failed: {e2}")
    
    # Test field exists
    print("\n=== Testing Field Existence ===")
    try:
        field_mapping = es_service.client.indices.get_field_mapping(
            index=index_name,
            fields=["userContextContentEmb", "userContentEmb"]
        )
        print("Field mapping:")
        print(json.dumps(field_mapping, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"Field mapping failed: {e}")

if __name__ == "__main__":
    debug_es_direct()
