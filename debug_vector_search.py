#!/usr/bin/env python3
"""
Debug script for vector search issue in difficulty_aware_route
"""

import os
import sys
import numpy as np
from src.fc_module.utils.elastic_search_service import ElasticsearchService
from src.fc_module.agent_flow import Agent<PERSON>low
import redis
from src.fc_module.utils.supabase_service import SupabaseService
from src.fc_module.async_log_worker import AsyncLogWorker
import asyncio

def test_vector_search():
    """Test vector search functionality"""
    print("=== Testing Vector Search Debug ===")
    
    # Initialize Elasticsearch service
    es_service = ElasticsearchService()
    
    # Test connection
    if not es_service.client or not es_service.client.ping():
        print("❌ Elasticsearch connection failed")
        return
    
    print("✅ Elasticsearch connected")
    
    # Test index existence
    index_name = "llm_difficulty_aware_router_emb"
    if not es_service.client.indices.exists(index=index_name):
        print(f"❌ Index {index_name} does not exist")
        # Try to create it
        if es_service.create_router_index():
            print("✅ Index created successfully")
        else:
            print("❌ Failed to create index")
            return
    else:
        print(f"✅ Index {index_name} exists")
    
    # Check index mapping
    try:
        mapping = es_service.client.indices.get_mapping(index=index_name)
        print(f"📋 Index mapping: {json.dumps(mapping, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"❌ Failed to get mapping: {e}")
    
    # Test with sample vector
    sample_vector = np.random.rand(1024).tolist()
    
    print("\n=== Testing Vector Search ===")
    try:
        results = es_service.search_by_dense_vector(
            vector_field="userContextContentEmb",
            query_vector=sample_vector,
            k=5
        )
        
        if results:
            print(f"✅ Vector search successful, found {len(results)} results")
            for i, result in enumerate(results[:3]):
                print(f"  Result {i+1}: score={result['score_cosine']}")
        else:
            print("⚠️ Vector search returned empty results")
            
    except Exception as e:
        print(f"❌ Vector search failed: {e}")
        print(f"Error type: {type(e)}")
        
    # Test document count
    try:
        count = es_service.client.count(index=index_name)
        print(f"📊 Total documents in index: {count['count']}")
    except Exception as e:
        print(f"❌ Failed to get document count: {e}")

def test_agent_flow_integration():
    """Test AgentFlow integration"""
    print("\n=== Testing AgentFlow Integration ===")
    
    try:
        # Initialize services
        cache = redis.Redis(host="127.0.0.1", port=6379, db=0)
        supabase_service = SupabaseService()
        asyncio.run(supabase_service.ensure_ready())
        async_logger = AsyncLogWorker(supabase_service)
        
        # Initialize AgentFlow
        agent_flow = AgentFlow(cache, supabase_service, async_logger)
        
        # Test embedding creation
        test_query = "打车去北京天安门"
        test_context = "用户查询: 打车去北京天安门"
        
        print("Testing embedding creation...")
        embedding = agent_flow.create_embedding(test_context)
        
        if embedding:
            print(f"✅ Embedding created successfully, length: {len(embedding)}")
            
            # Test vector search through AgentFlow
            print("Testing difficulty_aware_route...")
            result = agent_flow.difficulty_aware_route(test_query, test_context)
            print(f"🎯 Route result: {result}")
        else:
            print("❌ Failed to create embedding")
            
    except Exception as e:
        print(f"❌ AgentFlow integration test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import json
    
    print("Starting vector search debug...")
    test_vector_search()
    test_agent_flow_integration()
    print("\n=== Debug Complete ===")
