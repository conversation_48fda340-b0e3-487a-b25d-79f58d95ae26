#!/usr/bin/env python3
"""
Detailed debug script for vector search issue
"""

import os
import sys
import json
import numpy as np
from elasticsearch import Elasticsearch
from src.fc_module.utils.elastic_search_service import ElasticsearchService

def test_vector_search_detailed():
    """Test vector search with detailed error analysis"""
    print("=== Detailed Vector Search Debug ===")
    
    # Initialize Elasticsearch service
    es_service = ElasticsearchService()
    
    if not es_service.client or not es_service.client.ping():
        print("❌ Elasticsearch connection failed")
        return
    
    print("✅ Elasticsearch connected")
    
    index_name = "llm_difficulty_aware_router_emb"
    
    # Check if index exists and get mapping
    try:
        exists = es_service.client.indices.exists(index=index_name)
        print(f"Index exists: {exists}")
        
        if exists:
            # Get mapping
            mapping = es_service.client.indices.get_mapping(index=index_name)
            print(f"Index mapping keys: {list(mapping.keys())}")
            if index_name in mapping:
                print(f"Mapping for {index_name}:")
                print(json.dumps(mapping[index_name], indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"❌ Failed to check index: {e}")
    
    # Check document count
    try:
        count = es_service.client.count(index=index_name)
        print(f"📊 Documents in index: {count['count']}")
    except Exception as e:
        print(f"❌ Failed to get count: {e}")
    
    # Test different vector search approaches
    test_vector = np.random.rand(1024).tolist()
    
    print(f"\n=== Testing Vector Search (vector length: {len(test_vector)}) ===")
    
    # Test 1: Current implementation
    print("\n1. Testing current implementation...")
    try:
        results = es_service.search_by_dense_vector(
            vector_field="userContextContentEmb",
            query_vector=test_vector,
            k=5
        )
        print(f"✅ Current implementation: {len(results)} results")
    except Exception as e:
        print(f"❌ Current implementation failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Try to get more error details
        try:
            # Direct Elasticsearch call
            body = {
                "size": 5,
                "_source": ["userId", "requestId", "userContent", "updatedTime"],
                "query": {
                    "script_score": {
                        "query": {"match_all": {}},
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'userContextContentEmb') + 1.0",
                            "params": {"query_vector": test_vector}
                        }
                    }
                }
            }
            
            response = es_service.client.search(index=index_name, body=body)
            print(f"Direct call successful: {response['hits']['total']['value']} hits")
            
        except Exception as inner_e:
            print(f"Direct call also failed: {inner_e}")
    
    # Test 2: Alternative vector search with knn
    print("\n2. Testing knn vector search...")
    try:
        knn_body = {
            "size": 5,
            "_source": ["userId", "requestId", "userContent", "updatedTime"],
            "knn": {
                "field": "userContextContentEmb",
                "query_vector": test_vector,
                "k": 5,
                "num_candidates": 10
            }
        }
        
        response = es_service.client.search(index=index_name, body=knn_body)
        hits = response["hits"]["hits"]
        print(f"✅ KNN search successful: {len(hits)} results")
        
        for hit in hits:
            print(f"  Score: {hit['_score']}")
            
    except Exception as e:
        print(f"❌ KNN search failed: {e}")
    
    # Test 3: Check if field exists in mapping
    print("\n3. Checking field mapping...")
    try:
        field_mapping = es_service.client.indices.get_field_mapping(
            index=index_name,
            fields=["userContextContentEmb", "userContentEmb"]
        )
