#!/usr/bin/env python3
"""
Simplified debug script for vector search issue
"""

import os
import json
import numpy as np
from src.fc_module.utils.elastic_search_service import ElasticsearchService

def debug_vector_search():
    """Debug vector search step by step"""
    print("=== Vector Search Debug ===")
    
    # Initialize Elasticsearch service
    es_service = ElasticsearchService()
    
    if not es_service.client or not es_service.client.ping():
        print("❌ Elasticsearch connection failed")
        return
    
    print("✅ Elasticsearch connected")
    
    index_name = "llm_difficulty_aware_router_emb"
    
    # Check index
    try:
        exists = es_service.client.indices.exists(index=index_name)
        print(f"Index exists: {exists}")
        
        if not exists:
            print("Creating index...")
            es_service.create_router_index()
    except Exception as e:
        print(f"Index check failed: {e}")
        return
    
    # Check document count
    try:
        count = es_service.client.count(index=index_name)
        print(f"Documents: {count['count']}")
    except Exception as e:
        print(f"Count failed: {e}")
    
    # Test vector search
    test_vector = np.random.rand(1024).tolist()
    print(f"Test vector length: {len(test_vector)}")
    
    # Test 1: Direct vector search
    print("\n=== Testing Vector Search ===")
    try:
        body = {
            "size": 5,
            "query": {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'userContextContentEmb') + 1.0",
                        "params": {"query_vector": test_vector}
                    }
                }
            }
        }
        
        response = es_service.client.search(index=index_name, body=body)
        hits = response["hits"]["hits"]
        print(f"✅ Vector search: {len(hits)} results")
        
    except Exception as e:
        print(f"❌ Vector search failed: {e}")
        
        # Test 2: Check if field exists
        try:
            mapping = es_service.client.indices.get_mapping(index=index_name)
            properties = mapping[index_name]["mappings"]["properties"]
            print(f"Available fields: {list(properties.keys())}")
            
            if "userContextContentEmb" not in properties:
                print("❌ userContextContentEmb field missing!")
                return
                
            field_type = properties["userContextContentEmb"].get("type")
            print(f"userContextContentEmb type: {field_type}")
            
        except Exception as e2:
            print(f"Mapping check failed: {e2}")
    
    # Add sample data if empty
    try:
        if es_service.client.count(index=index_name)['count'] == 0:
            print("\nAdding sample data...")
            sample_doc = {
                "userId": 9999,
                "requestId": 99999,
                "userContent": "测试查询",
                "userContentEmb": test_vector,
                "userContextContent": "测试上下文",
                "userContextContentEmb": test_vector,
                "functionCallingResponse": "测试响应",
                "candidateList": ["qwen-max", "local/qwen3_235b_a22b"],
                "downgradable": 1
            }
            
            response = es_service.client.index(index=index_name, document=sample_doc)
            es_service.client.indices.refresh(index=index_name)
            print(f"✅ Sample added: {response['_id']}")
            
    except Exception as e:
        print(f"❌ Sample data failed: {e}")

if __name__ == "__main__":
    debug_vector_search()
