#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断OAuth2授权问题
检查授权端点和参数是否正确
"""

import requests
import webbrowser
from urllib.parse import urlencode

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def test_authorization_endpoint():
    """测试授权端点"""
    print("🔍 诊断OAuth2授权端点")
    print("=" * 50)
    
    redirect_uri = "http://localhost:8080/callback"
    state = f"diagnose_{int(__import__('time').time())}"
    
    # 测试不同的授权端点
    endpoints_to_test = [
        "https://smzdm.com/oauth2/authorize",
        "https://www.smzdm.com/oauth2/authorize", 
        "https://openapi.smzdm.com/oauth2/authorize",
        "https://smzdm.com/oauth/authorize"
    ]
    
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    query_string = urlencode(params)
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"📍 回调地址: {redirect_uri}")
    print(f"🔐 状态参数: {state}")
    print(f"\n测试参数: {params}")
    
    working_endpoints = []
    
    for i, base_url in enumerate(endpoints_to_test, 1):
        full_url = f"{base_url}?{query_string}"
        print(f"\n{i}. 测试端点: {base_url}")
        print(f"   完整URL: {full_url}")
        
        try:
            # 发送HEAD请求检查端点
            response = requests.head(full_url, timeout=10, allow_redirects=False)
            status_code = response.status_code
            
            if status_code == 200:
                print(f"   ✅ 200 OK - 端点正常")
                working_endpoints.append((base_url, full_url, "200 OK"))
            elif status_code in [301, 302]:
                location = response.headers.get('Location', 'N/A')
                print(f"   🔄 {status_code} 重定向到: {location}")
                working_endpoints.append((base_url, full_url, f"{status_code} 重定向"))
            elif status_code == 403:
                print(f"   🚫 403 禁止访问 - 可能需要特定权限")
                working_endpoints.append((base_url, full_url, "403 需要权限"))
            elif status_code == 404:
                print(f"   ❌ 404 未找到")
            elif status_code == 405:
                print(f"   ⚠️  405 方法不允许 - 尝试GET请求")
                try:
                    get_response = requests.get(full_url, timeout=10, allow_redirects=False)
                    print(f"   GET请求结果: {get_response.status_code}")
                    if get_response.status_code in [200, 301, 302]:
                        working_endpoints.append((base_url, full_url, f"GET {get_response.status_code}"))
                except:
                    print(f"   GET请求也失败")
            else:
                print(f"   ❓ {status_code} - 未知状态")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时")
        except requests.exceptions.ConnectionError:
            print(f"   🔌 连接错误")
        except Exception as e:
            print(f"   ❌ 异常: {str(e)[:50]}...")
    
    # 总结结果
    print(f"\n" + "=" * 50)
    print("📊 诊断结果")
    print("-" * 50)
    
    if working_endpoints:
        print(f"✅ 找到 {len(working_endpoints)} 个可能工作的端点:")
        for i, (base_url, full_url, status) in enumerate(working_endpoints, 1):
            print(f"\n{i}. {base_url}")
            print(f"   状态: {status}")
        
        # 推荐最佳端点
        best_endpoint = working_endpoints[0]
        print(f"\n💡 推荐使用: {best_endpoint[0]}")
        return best_endpoint[1]  # 返回完整URL
    else:
        print("❌ 未找到任何工作的授权端点")
        print("\n🤔 可能的问题:")
        print("1. APP_KEY可能无效")
        print("2. OAuth2功能可能未启用")
        print("3. 需要在开发者平台配置回调地址")
        print("4. API可能需要特殊的授权流程")
        return None

def manual_browser_test():
    """手动浏览器测试"""
    print(f"\n🌐 手动浏览器测试")
    print("-" * 30)
    
    # 生成测试URL
    redirect_uri = "http://localhost:8080/callback"
    state = f"manual_{int(__import__('time').time())}"
    
    base_url = "https://smzdm.com/oauth2/authorize"
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    query_string = urlencode(params)
    test_url = f"{base_url}?{query_string}"
    
    print(f"测试URL: {test_url}")
    
    open_browser = input("\n是否在浏览器中打开测试URL? (y/n): ").strip().lower()
    
    if open_browser == 'y':
        try:
            print("🌍 正在打开浏览器...")
            webbrowser.open(test_url)
            print("✅ 浏览器已打开")
            
            print(f"\n📝 请在浏览器中检查:")
            print("1. 页面是否正常加载?")
            print("2. 是否显示值得买的登录页面?")
            print("3. 登录后是否显示授权确认页面?")
            print("4. 是否有任何错误信息?")
            
            feedback = input("\n请描述你在浏览器中看到的内容: ").strip()
            print(f"用户反馈: {feedback}")
            
            return feedback
        except Exception as e:
            print(f"❌ 无法打开浏览器: {e}")
            return None
    else:
        print("请手动复制URL到浏览器测试")
        return None

def check_app_credentials():
    """检查应用凭据"""
    print(f"\n🔐 检查应用凭据")
    print("-" * 30)
    
    print(f"APP_KEY: {APP_KEY}")
    print(f"APP_SECRET: {APP_SECRET[:8]}...")
    
    if not APP_KEY or APP_KEY == "your_app_key_here":
        print("❌ APP_KEY无效")
        return False
    
    if not APP_SECRET or APP_SECRET == "your_app_secret_here":
        print("❌ APP_SECRET无效")
        return False
    
    print("✅ 应用凭据格式正确")
    return True

def main():
    """主函数"""
    print("🔧 OAuth2授权问题诊断工具")
    print("=" * 50)
    
    # 1. 检查应用凭据
    if not check_app_credentials():
        print("请先配置正确的APP_KEY和APP_SECRET")
        return
    
    # 2. 测试授权端点
    working_url = test_authorization_endpoint()
    
    # 3. 手动浏览器测试
    if working_url:
        print(f"\n找到工作的端点，进行手动测试...")
        feedback = manual_browser_test()
        
        if feedback:
            print(f"\n📋 诊断建议:")
            if "404" in feedback or "not found" in feedback.lower():
                print("- 授权端点可能不存在，尝试其他端点")
            elif "403" in feedback or "forbidden" in feedback.lower():
                print("- 可能需要在开发者平台配置权限")
            elif "登录" in feedback:
                print("- 授权端点工作正常，请完成登录流程")
            elif "授权" in feedback:
                print("- 已到达授权页面，请点击授权按钮")
            else:
                print("- 请提供更多详细信息以便进一步诊断")
    else:
        print(f"\n❌ 未找到工作的授权端点")
        print("建议联系API提供方确认OAuth2配置")

if __name__ == "__main__":
    main()
