#!/usr/bin/env python3
"""
ES语义检索查询语句生成器 - 完整版
用于生成Elasticsearch的语义检索查询语句，并计算可降级数据量
"""

import os
import json
import sys
import pathlib

# 添加项目路径
current_dir = pathlib.Path(__file__).parent
parent_dir = str(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from fc_module.utils.elastic_search_service import ElasticsearchService
from openai import OpenAI

class ESSemanticSearchGenerator:
    """ES语义检索查询生成器"""
    
    def __init__(self):
        self.es_service = ElasticsearchService(index_name="llm_router_evaluation_results")
        
        # 初始化embedding客户端
        self.embedding_client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY", "sk-fa8336aed5c345eebafc6df0b83c48be"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
        self.embedding_model = "text-embedding-v4"
    
    def create_embedding(self, text: str):
        """使用embedding模型获取向量"""
        try:
            completion = self.embedding_client.embeddings.create(
                model=self.embedding_model,
                input=text,
                encoding_format="float"
            )
            return completion.data[0].embedding if completion.data else None
        except Exception as e:
            print(f"创建embedding时出错: {e}")
            return None
    
    def generate_semantic_search_query(self, query_text: str, k: int = 10, min_score: float = 0.7):
        """生成语义检索查询语句"""
        query_embedding = self.create_embedding(query_text)
        if not query_embedding:
            raise ValueError("无法生成查询文本的embedding")
        
        return {
            "size": k,
            "_source": [
                "userId", "requestId", "userContent", "userContextContent",
                "functionCallingResponse", "candidateList", "downgradable"
            ],
            "query": {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'userContentEmb') + 1.0",
                        "params": {"query_vector": query_embedding}
                    }
                }
            },
            "min_score": min_score + 1.0
        }
    
    def generate_hybrid_search_query(self, query_text: str, k: int = 10):
        """生成混合检索查询"""
        query_embedding = self.create_embedding(query_text)
        if not query_embedding:
            raise ValueError("无法生成查询文本的embedding")
        
        return {
            "size": k,
            "_source": [
                "userId", "requestId", "userContent", "userContextContent",
                "functionCallingResponse", "candidateList", "downgradable"
            ],
            "query": {
                "bool": {
                    "should": [
                        {"match": {"userContent": query_text}},
                        {"match": {"userContextContent": query_text}},
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": "cosineSimilarity(params.query_vector, 'userContentEmb') + 1.0",
                                    "params": {"query_vector": query_embedding}
                                }
                            }
                        }
                    ]
                }
            }
        }
    
    def calculate_downgradable_data(self, query_filter=None):
        """计算可降级数据量"""
        agg_query = {
            "size": 0,
            "aggs": {
                "total_records": {"value_count": {"field": "userId"}},
                "downgradable_stats": {"terms": {"field": "downgradable", "size": 10}},
                "candidate_models": {"terms": {"field": "candidateList.keyword", "size": 50}}
            }
        }
        
        if query_filter:
            agg_query["query"] = {
                "bool": {
                    "should": [
                        {"match": {"userContent": query_filter}},
                        {"match": {"userContextContent": query_filter}}
                    ]
                }
            }
        
        try:
            response = self.es_service.client.search(
                index=self.es_service.index_name,
                body=agg_query
            )
            
            aggregations = response.get("aggregations", {})
            total = aggregations.get("total_records", {}).get("value", 0)
            buckets = aggregations.get("downgradable_stats", {}).get("buckets", [])
            
            downgradable = 0
            for bucket in buckets:
                if bucket.get("key") == 1:
                    downgradable = bucket.get("doc_count
