#!/usr/bin/env python3
"""
ES语义检索查询语句生成器
用于生成Elasticsearch的语义检索查询语句，并计算可降级数据量
"""

import os
import json
import numpy as np
from typing import List, Dict, Any, Optional
from datetime import datetime
import sys
import pathlib

# 添加项目路径
current_dir = pathlib.Path(__file__).parent
parent_dir = str(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from fc_module.utils.elastic_search_service import ElasticsearchService
from fc_module.utils.local_host_llm_service import LocalHostLLMService
from openai import OpenAI

class ESSemanticSearchGenerator:
    """ES语义检索查询生成器"""
    
    def __init__(self):
        self.es_service = ElasticsearchService(index_name="llm_router_evaluation_results")
        
        # 初始化embedding客户端
        self.embedding_client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY", "sk-fa8336aed5c345eebafc6df0b83c48be"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
        self.embedding_model = "text-embedding-v4"
        self.embedding_dimensions = 1024
    
    def create_embedding(self, text: str) -> Optional[List[float]]:
        """使用embedding模型获取向量"""
        try:
            completion = self.embedding_client.embeddings.create(
                model=self.embedding_model,
                input=text,
                dimensions=self.embedding_dimensions,
                encoding_format="float"
            )
            
            if completion.data and len(completion.data) > 0:
                return completion.data[0].embedding
            else:
                print("警告: 未返回embedding数据")
                return None
                
        except Exception as e:
            print(f"创建embedding时出错: {e}")
            return None
    
    def generate_semantic_search_query(self, query_text: str, k: int = 10, 
                                     min_score: float = 0.7) -> Dict[str, Any]:
        """
        生成语义检索查询语句
        
        Args:
            query_text: 查询文本
            k: 返回结果数量
            min_score: 最小相似度分数
            
        Returns:
            完整的ES查询语句
        """
        # 获取查询文本的embedding
        query_embedding = self.create_embedding(query_text)
        if not query_embedding:
            raise ValueError("无法生成查询文本的embedding")
        
        # 构建语义检索查询
        query = {
            "size": k,
            "_source": [
                "userId",
                "requestId",
                "userContent",
                "userContextContent",
                "functionCallingResponse",
                "candidateList",
                "downgradable",
                "evaluationMetadata",
                "updatedTime"
            ],
            "query": {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": """
                            Math.max(
                                cosineSimilarity(params.query_vector, 'userContentEmb'),
                                cosineSimilarity(params.query_vector, 'userContextContentEmb')
                            ) + 1.0
                        """,
                        "params": {
                            "query_vector": query_embedding
                        }
                    }
                }
            },
            "min_score": min_score + 1.0,  # 因为脚本中加了1.0
            "sort": [
                {"_score": {"order": "desc"}}
            ]
        }
        
        return query
    
    def generate_hybrid_search_query(self, query_text: str, k: int = 10,
                                   text_weight: float = 0.3, vector_weight: float = 0.7) -> Dict[str, Any]:
        """
        生成混合检索查询（文本+向量）
        
        Args:
            query_text: 查询文本
            k: 返回结果数量
            text_weight: 文本检索权重
            vector_weight: 向量检索权重
            
        Returns:
            混合检索查询语句
        """
        # 获取查询文本的embedding
        query_embedding = self.create_embedding(query_text)
        if not query_embedding:
            raise ValueError("无法生成查询文本的embedding")
        
        # 构建混合检索查询
        query = {
            "size": k,
            "_source": [
                "userId",
                "requestId",
                "userContent",
                "userContextContent",
                "functionCallingResponse",
                "candidateList",
                "downgradable",
                "evaluationMetadata",
                "updatedTime"
            ],
            "query": {
                "script_score": {
                    "query": {
                        "bool":
