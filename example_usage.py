#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
值得买API使用示例
演示如何生成API URL并发起HTTP请求
"""

from zhidemai_api import generate_api_url
import requests


def example_api_calls():
    """API调用示例"""
    
    # 配置你的密钥（从值得买开放平台获取）
    APP_KEY = "your_app_key_here"  # 替换为真实的app_key
    APP_SECRET = "your_app_secret_here"  # 替换为真实的app_secret
    
    print("=== 值得买API调用示例 ===\n")
    
    # 示例1: 获取好价列表
    print("1. 获取好价列表")
    url1 = generate_api_url(
        endpoint='haojia/third/list',
        app_key=APP_KEY,
        app_secret=APP_SECRET,
        params={
            'page': '1',
            'page_size': '5',
            'order_status': '1'
        }
    )
    print(f"URL: {url1}")
    
    # 如果配置了真实密钥，可以取消注释进行实际调用
    # try:
    #     response = requests.get(url1, timeout=10)
    #     print(f"状态码: {response.status_code}")
    #     print(f"响应: {response.json()}")
    # except Exception as e:
    #     print(f"请求失败: {e}")
    
    print("\n" + "-"*50 + "\n")
    
    # 示例2: 获取好价详情
    print("2. 获取好价详情")
    url2 = generate_api_url(
        endpoint='haojia/third/info',
        app_key=APP_KEY,
        app_secret=APP_SECRET,
        params={
            'article_id': '12345',
            'version': '2'
        }
    )
    print(f"URL: {url2}")
    
    print("\n" + "-"*50 + "\n")
    
    # 示例3: 搜索文章
    print("3. 搜索文章")
    url3 = generate_api_url(
        endpoint='search/article/list',
        app_key=APP_KEY,
        app_secret=APP_SECRET,
        params={
            'keyword': '手机',
            'page': '1',
            'page_size': '3'
        }
    )
    print(f"URL: {url3}")
    
    print("\n" + "="*50 + "\n")
    
    print("使用说明:")
    print("1. 将 APP_KEY 和 APP_SECRET 替换为从值得买开放平台获取的真实值")
    print("2. 取消注释 requests.get() 代码进行实际API调用")
    print("3. 生成的URL可以直接在浏览器中访问测试")


def test_with_requests():
    """使用requests库进行HTTP调用的示例"""
    
    # 这里使用测试密钥，实际使用时请替换
    APP_KEY = "z13408f3a0"
    APP_SECRET = "e4973a4589bd44e0201db869cf77279e"
    
    if APP_KEY == "your_real_app_key":
        print("⚠️  请先配置真实的APP_KEY和APP_SECRET")
        return
    
    # 生成API URL
    url = generate_api_url(
        endpoint='haojia/third/list',
        app_key=APP_KEY,
        app_secret=APP_SECRET,
        params={
            'page': '1',
            'page_size': '3',
            'order_status': '1'
        }
    )
    
    print(f"请求URL: {url}")
    
    try:
        # 发起HTTP GET请求
        response = requests.get(url, timeout=10)
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {data}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"网络请求异常: {e}")
    except Exception as e:
        print(f"其他异常: {e}")


if __name__ == "__main__":
    # 运行示例
    example_api_calls()
    
    print("\n" + "="*60 + "\n")
    
    # HTTP请求示例（需要真实密钥）
    print("=== HTTP请求示例 ===")
    test_with_requests()
