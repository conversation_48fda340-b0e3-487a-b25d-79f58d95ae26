#!/usr/bin/env python3
"""
qwen3_30b_a3b_fp8模型集成示例
展示如何无缝替换现有的llm_api.py
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def example1_direct_usage():
    """直接使用本地qwen3 API"""
    print("=== 示例1: 直接使用本地qwen3 API ===")
    
    from src.local_qwen3_api import local_qwen3
    
    # 简单对话
    response = local_qwen3.simple_chat("你好，请介绍一下qwen3模型")
    print(f"简单回复: {response}")
    
    # 标准对话
    messages = [
        {"role": "system", "content": "你是一个专业的AI助手"},
        {"role": "user", "content": "请解释一下机器学习的基本概念"}
    ]
    
    result = local_qwen3.get_responses(
        model="qwen3_30b_a3b_fp8",
        messages=messages
    )
    
    print(f"标准对话响应: {result['message']}")
    print(f"耗时: {result['cost_time']:.2f}秒")
    print(f"Token使用: {result['usage']}")

def example2_compatible_usage():
    """兼容现有llm_api.py的用法"""
    print("\n=== 示例2: 兼容现有llm_api.py的用法 ===")
    
    # 方式1: 使用兼容包装器
    from src.qwen3_llm_wrapper import llm_wrap
    
    messages = [
        {"role": "user", "content": "你好，请介绍一下自己"}
    ]
    
    result = llm_wrap.get_responses(
        model="qwen3_30b_a3b_fp8",
        messages=messages
    )
    
    print(f"兼容接口响应: {result['message']}")
    
    # 方式2: 直接替换导入
    # 只需将原来的: from src.llm_api import llm_wrap
    # 改为: from src.qwen3_llm_wrapper import llm_wrap
    # 其余代码无需修改

def example3_with_tools():
    """工具调用示例"""
    print("\n=== 示例3: 工具调用功能 ===")
    
    from src.local_qwen3_api import LocalQwen3API
    
    api = LocalQwen3API()
    
    # 定义工具
    tools = [
        {
            "type": "function",
            "function": {
                "name": "weather_forecast",
                "description": "获取天气预报",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "city": {"type": "string", "description": "城市名称"},
                        "days": {"type": "integer", "description": "预报天数"}
                    },
                    "required": ["city"]
                }
            }
        }
    ]
    
    messages = [
        {"role": "user", "content": "请帮我查询北京的天气"}
    ]
    
    result = api.get_responses(
        model="qwen3_30b_a3b_fp8",
        messages=messages,
        tools=tools
    )
    
    print(f"用户消息: {messages[0]['content']}")
    print(f"模型响应: {result['message']}")
    
    if result['tools']:
        print("检测到的工具调用:")
        for tool in result['tools']:
            print(f"  - 工具: {tool['function']['name']}")
            print(f"  - 参数: {tool['function']['arguments']}")

def example4_batch_testing():
    """批量测试不同场景"""
    print("\n=== 示例4: 批量测试不同场景 ===")
    
    from src.local_qwen3_api import LocalQwen3API
    
    api = LocalQwen3API()
    
    test_cases = [
        {
            "name": "数学计算",
            "messages": [{"role": "user", "content": "计算 123 * 456 等于多少？"}]
        },
        {
            "name": "代码解释",
            "messages": [{"role": "user", "content": "请解释Python中的装饰器是什么"}]
        },
        {
            "name": "翻译",
            "messages": [{"role": "user", "content": "请将'Hello, World!'翻译成中文"}]
        },
        {
            "name": "创意写作",
            "messages": [{"role": "user", "content": "写一个关于AI的短故事，100字以内"}]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        try:
            result = api.get_responses(
                model="qwen3_30b_a3b_fp8",
                messages=test_case['messages']
            )
            print(f"   响应: {result['message']}")
            print(f"   耗时: {result['cost_time']:.2f}秒")
        except Exception as e:
            print(f"   错误: {e}")

def example5_migration_guide():
    """迁移指南示例"""
    print("\n=== 示例5: 从llm_api.py迁移到qwen3 ===")
    
    print("迁移步骤:")
    print("1. 替换导入语句:")
    print("   原来: from src.llm_api import llm_wrap")
    print("   现在: from src.qwen3_llm_wrapper import llm_wrap")
    print("\n2. 修改模型名称:")
    print("   原来: model='deepseek-chat'")
    print("   现在: model='qwen3_30b_a3b_fp8'")
    print("\n3. 其余代码保持不变")
    
    # 演示迁移后的代码
    from src.qwen3_llm_wrapper import llm_wrap
    
    # 这是与原来完全相同的用法
    messages = [
        {"role": "system", "content": "你是一个助手"},
        {"role": "user", "content": "你好"}
    ]
    
    result = llm_wrap.get_responses(
        model="qwen3_30b_a3b_fp8",
        messages=messages
    )
    
    print(f"\n迁移后测试结果: {result['message']}")

if __name__ == '__main__':
    print("开始运行qwen3_30b_a3b_fp8模型集成示例...")
    
    # 运行各个示例
    example1_direct_usage()
    example2_compatible_usage()
    example3_with_tools()
    example4_batch_testing()
    example5_migration_guide()
    
    print("\n=== 所有示例运行完成 ===")
    print("更多信息请参考: LOCAL_QWEN3_USAGE.md")
