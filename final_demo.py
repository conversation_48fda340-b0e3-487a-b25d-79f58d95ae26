#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的条形码识别与商品查询演示
从图片URL到产品文本描述的完整功能展示
"""

from src.fc_module.tools.image_barcode_processor import (
    get_product_description_from_image,
    get_product_summary_from_image
)


def main():
    """主演示函数"""
    print("🎉 完整的条形码识别与商品查询系统")
    print("=" * 60)
    
    # 测试图片URL
    image_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    print(f"📷 处理图片: {image_url}")
    print()
    
    # 演示1: 结构化描述（按您的示例格式）
    print("📋 演示1: 结构化描述")
    print("-" * 40)
    
    structured_desc = get_product_description_from_image(image_url, "structured")
    print(structured_desc)
    print()
    
    # 演示2: 详细描述
    print("📋 演示2: 详细描述")
    print("-" * 40)
    
    detailed_desc = get_product_description_from_image(image_url, "detailed")
    print(detailed_desc)
    print()
    
    # 演示3: 简单描述
    print("📋 演示3: 简单描述")
    print("-" * 40)
    
    simple_desc = get_product_description_from_image(image_url, "simple")
    print(simple_desc)
    print()
    
    # 演示4: 产品摘要
    print("📋 演示4: 产品摘要")
    print("-" * 40)
    
    summary = get_product_summary_from_image(image_url)
    if summary['success']:
        print("✅ 摘要生成成功!")
        print(f"条形码: {summary['barcode']}")
        print(f"商品名称: {summary['name']}")
        print(f"品牌: {summary['brand']}")
        print(f"规格: {summary['specification']}")
        print(f"产地: {summary['origin']}")
        print(f"厂商: {summary['manufacturer']}")
        print(f"类别: {summary['category']}")
        
        if summary['image_url']:
            print(f"商品图片: {summary['image_url']}")
    else:
        print(f"❌ 摘要生成失败: {summary['error']}")
    
    print()
    
    # 功能总结
    print("🎊 功能总结")
    print("-" * 40)
    print("✅ 图片条形码识别 - 完成")
    print("✅ MCP服务商品查询 - 完成")
    print("✅ 多种描述格式 - 完成")
    print("✅ 完整流程集成 - 完成")
    print()
    
    print("📚 使用方法:")
    print("1. 结构化描述: get_product_description_from_image(url, 'structured')")
    print("2. 详细描述: get_product_description_from_image(url, 'detailed')")
    print("3. 简单描述: get_product_description_from_image(url, 'simple')")
    print("4. 产品摘要: get_product_summary_from_image(url)")
    print()
    
    print("🎉 演示完成！系统已完全就绪，可以投入使用！")


if __name__ == "__main__":
    main()
