#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复OAuth2授权确认页面问题
尝试不同的参数组合和授权方式
"""

import webbrowser
import time
from urllib.parse import urlencode

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def generate_authorization_urls():
    """生成不同参数组合的授权URL"""
    redirect_uri = "http://localhost:8080/callback"
    state = f"fix_{int(time.time())}"
    
    # 尝试不同的参数组合
    url_variants = []
    
    # 1. 标准OAuth2参数
    params1 = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    # 2. 添加scope参数
    params2 = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state,
        "scope": "read"
    }
    
    # 3. 添加强制授权参数
    params3 = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state,
        "approval_prompt": "force"
    }
    
    # 4. 添加访问类型参数
    params4 = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state,
        "access_type": "offline"
    }
    
    # 5. 组合多个参数
    params5 = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state,
        "scope": "read",
        "approval_prompt": "force",
        "access_type": "offline"
    }
    
    # 6. 尝试不同的回调地址
    params6 = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": "https://example.com/callback",
        "state": state
    }
    
    base_urls = [
        "https://smzdm.com/oauth2/authorize",
        "https://openapi.smzdm.com/oauth2/authorize"
    ]
    
    param_sets = [
        ("标准参数", params1),
        ("添加scope", params2), 
        ("强制授权", params3),
        ("离线访问", params4),
        ("组合参数", params5),
        ("外部回调", params6)
    ]
    
    for base_url in base_urls:
        for desc, params in param_sets:
            query_string = urlencode(params)
            full_url = f"{base_url}?{query_string}"
            url_variants.append((desc, base_url, full_url, params))
    
    return url_variants

def test_authorization_variants():
    """测试不同的授权URL变体"""
    print("🔧 修复OAuth2授权确认页面问题")
    print("=" * 50)
    
    print("📋 问题分析:")
    print("- 登录成功但没有显示授权确认页面")
    print("- 可能原因:")
    print("  1. 缺少必要的OAuth2参数")
    print("  2. 应用未在开发者平台正确配置")
    print("  3. 回调地址未注册")
    print("  4. 需要特定的scope或权限参数")
    
    url_variants = generate_authorization_urls()
    
    print(f"\n🧪 生成了 {len(url_variants)} 个测试URL变体")
    print("=" * 50)
    
    for i, (desc, base_url, full_url, params) in enumerate(url_variants, 1):
        print(f"\n{i}. {desc} ({base_url.split('//')[1].split('/')[0]})")
        print(f"   参数: {params}")
        print(f"   URL: {full_url}")
        
        test_this = input(f"   是否测试这个URL? (y/n/q退出): ").strip().lower()
        
        if test_this == 'q':
            break
        elif test_this == 'y':
            try:
                print("   🌍 正在打开浏览器...")
                webbrowser.open(full_url)
                print("   ✅ 浏览器已打开")
                
                result = input("   请描述浏览器中的结果 (登录页/授权页/404/其他): ").strip()
                
                if "授权" in result:
                    print("   🎉 找到工作的授权URL!")
                    print(f"   推荐使用: {full_url}")
                    
                    # 询问是否继续完成授权
                    complete = input("   是否继续完成授权流程? (y/n): ").strip().lower()
                    if complete == 'y':
                        return full_url, params
                elif "登录" in result:
                    print("   ℹ️  显示登录页面，请登录后查看是否有授权页面")
                elif "404" in result:
                    print("   ❌ 404错误，此URL无效")
                else:
                    print(f"   📝 记录结果: {result}")
                    
            except Exception as e:
                print(f"   ❌ 打开浏览器失败: {e}")
    
    return None, None

def alternative_solutions():
    """提供替代解决方案"""
    print("\n🔄 替代解决方案")
    print("=" * 30)
    
    print("如果所有URL都无法显示授权页面，可能的解决方案:")
    print("\n1. 📞 联系API提供方")
    print("   - 确认OAuth2功能是否已启用")
    print("   - 确认APP_KEY是否有OAuth2权限")
    print("   - 询问正确的授权流程")
    
    print("\n2. 🔍 检查开发者平台设置")
    print("   - 登录值得买开发者平台")
    print("   - 检查应用配置")
    print("   - 确认回调地址设置")
    
    print("\n3. 📚 查看官方文档")
    print("   - 重新阅读OAuth2文档")
    print("   - 查看是否有特殊要求")
    print("   - 确认授权流程步骤")
    
    print("\n4. 🧪 尝试其他认证方式")
    print("   - 检查是否支持其他OAuth2流程")
    print("   - 尝试客户端凭据模式")
    print("   - 查看是否有API密钥认证")

def manual_code_input():
    """手动输入授权码的备用方案"""
    print("\n🔧 手动授权码输入 (备用方案)")
    print("=" * 40)
    
    print("如果你通过其他方式获得了授权码，可以直接输入:")
    
    code = input("请输入授权码 (如果没有请直接回车): ").strip()
    
    if code:
        print(f"收到授权码: {code[:10]}...")
        
        # 尝试交换token
        import requests
        import json
        
        url = "https://openapi.smzdm.com/v1/oauth/check/code"
        params = {
            "app_key": APP_KEY,
            "code": code,
            "app_secret": APP_SECRET
        }
        
        try:
            print("🔄 正在交换access_token...")
            response = requests.post(url, data=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get("error_code") == "0":
                    data = result.get("data", {})
                    access_token = data.get("access_token", "")
                    
                    print(f"🎉 成功获取access_token!")
                    print(f"Token: {access_token}")
                    
                    # 保存token
                    with open('access_token.txt', 'w') as f:
                        f.write(f"access_token={access_token}\n")
                        f.write(f"expires_in={data.get('expires_in', 0)}\n")
                        f.write(f"union_id={data.get('union_id', '')}\n")
                    
                    print("💾 Token已保存到 access_token.txt")
                    return True
                else:
                    print(f"❌ Token交换失败: {result.get('error_msg')}")
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return False

def main():
    """主函数"""
    print("🚀 OAuth2授权确认页面修复工具")
    
    # 1. 测试不同的URL变体
    working_url, working_params = test_authorization_variants()
    
    if working_url:
        print(f"\n✅ 找到工作的授权URL: {working_url}")
        print("请在浏览器中完成授权流程")
    else:
        print(f"\n❌ 未找到工作的授权URL")
        
        # 2. 提供替代解决方案
        alternative_solutions()
        
        # 3. 手动输入授权码
        success = manual_code_input()
        
        if not success:
            print(f"\n📞 建议联系值得买技术支持获取帮助")

if __name__ == "__main__":
    main()
