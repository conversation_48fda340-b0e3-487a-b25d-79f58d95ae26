#!/usr/bin/env python3
"""
手动获取access_token的简化脚本
适用于无法运行本地服务器的情况

使用方法:
python get_access_token_manual.py
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    generate_authorization_url,
    get_access_token_by_code,
    parse_access_token_response,
    is_access_token_valid,
    APP_KEY,
    APP_SECRET
)

def manual_oauth_flow():
    """手动OAuth2.0流程"""
    print("🔐 手动获取Zhidemai access_token")
    print("=" * 50)
    
    if not APP_KEY or not APP_SECRET:
        print("❌ 错误: APP_KEY和APP_SECRET必须设置")
        return None
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"🔐 APP_SECRET: {APP_SECRET[:8]}...")
    
    # 步骤1: 配置回调地址
    print(f"\n📋 步骤1: 配置回调地址")
    print("选择回调地址类型:")
    print("1. 使用示例地址 (https://example.com/callback)")
    print("2. 使用httpbin.org (https://httpbin.org/get)")
    print("3. 自定义地址")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        callback_url = "https://example.com/callback"
    elif choice == "2":
        callback_url = "https://httpbin.org/get"
    elif choice == "3":
        callback_url = input("请输入回调地址: ").strip()
        if not callback_url:
            print("❌ 回调地址不能为空")
            return None
    else:
        print("❌ 无效选择，使用默认地址")
        callback_url = "https://example.com/callback"
    
    print(f"✅ 回调地址: {callback_url}")
    
    # 步骤2: 生成授权URL
    print(f"\n📋 步骤2: 生成授权URL")
    state = f"manual_flow_{int(__import__('time').time())}"
    
    try:
        auth_url = generate_authorization_url(callback_url, state)
        print(f"✅ 授权URL生成成功")
        print(f"\n🔗 请在浏览器中打开以下URL进行授权:")
        print(f"{auth_url}")
        
    except Exception as e:
        print(f"❌ 生成授权URL失败: {e}")
        return None
    
    # 步骤3: 用户手动授权
    print(f"\n📋 步骤3: 完成授权")
    print("请按以下步骤操作:")
    print("1. 复制上面的授权URL到浏览器")
    print("2. 登录您的值得买账号")
    print("3. 点击授权按钮")
    print("4. 浏览器会跳转到回调地址")
    print("5. 从回调URL中复制code参数的值")
    
    print(f"\n💡 回调URL格式示例:")
    print(f"{callback_url}?code=AUTHORIZATION_CODE&state={state}")
    print(f"您需要复制 code= 后面的值")
    
    # 步骤4: 输入授权码
    print(f"\n📋 步骤4: 输入授权码")
    code = input("请输入从回调URL中提取的授权码: ").strip()
    
    if not code:
        print("❌ 授权码不能为空")
        return None
    
    print(f"✅ 授权码: {code[:20]}...")
    
    # 步骤5: 获取access_token
    print(f"\n📋 步骤5: 获取access_token")
    print("🔄 正在请求access_token...")
    
    try:
        token_response = get_access_token_by_code(code)
        
        if token_response and token_response.get("error_code") == "0":
            print("🎉 access_token获取成功!")
            
            # 解析token信息
            token_info = parse_access_token_response(token_response)
            
            access_token = token_info['access_token']
            expires_in = token_info['expires_in']
            union_id = token_info['union_id']
            expires_at = token_info['expires_at']
            
            print(f"\n📋 Token信息:")
            print(f"   🎫 access_token: {access_token}")
            print(f"   ⏰ expires_in: {expires_in}秒 (约{expires_in//3600}小时)")
            print(f"   👤 union_id: {union_id}")
            print(f"   📅 expires_at: {expires_at}")
            print(f"   ✅ 有效性: {'有效' if is_access_token_valid(token_info) else '已过期'}")
            
            # 保存到文件
            save_token_to_file(token_info)
            
            return token_info
            
        else:
            error_msg = token_response.get("error_msg", "未知错误") if token_response else "请求失败"
            print(f"❌ 获取access_token失败: {error_msg}")
            
            # 提供错误解决建议
            print(f"\n🔧 可能的解决方案:")
            if "200002" in str(token_response):
                print("   - 检查授权码是否正确输入")
            elif "200003" in str(token_response):
                print("   - 授权码可能已过期，请重新获取")
            elif "200004" in str(token_response):
                print("   - 授权码与APP_KEY不匹配，请检查配置")
            else:
                print("   - 检查网络连接")
                print("   - 确认APP_KEY和APP_SECRET正确")
                print("   - 验证回调地址配置")
            
            return None
            
    except Exception as e:
        print(f"❌ 获取access_token过程中发生异常: {e}")
        return None

def save_token_to_file(token_info):
    """保存token信息到文件"""
    try:
        with open('access_token.txt', 'w', encoding='utf-8') as f:
            f.write(f"access_token={token_info['access_token']}\n")
            f.write(f"expires_in={token_info['expires_in']}\n")
            f.write(f"union_id={token_info['union_id']}\n")
            f.write(f"expires_at={token_info['expires_at']}\n")
        
        print(f"\n💾 Token信息已保存到 access_token.txt")
        print(f"   您可以在代码中读取此文件使用token")
        
    except Exception as e:
        print(f"⚠️  保存token文件失败: {e}")

def test_token_with_api(token_info):
    """使用token测试API调用"""
    print(f"\n🧪 测试API调用")
    print("-" * 30)
    
    access_token = token_info['access_token']
    
    try:
        from fc_module.tools.zhidemai_info import get_haojia_detail_simple
        
        test_article_id = "13284568"
        print(f"测试文章ID: {test_article_id}")
        print(f"使用token: {access_token[:20]}...")
        
        data = get_haojia_detail_simple(test_article_id, access_token)
        
        if data:
            print("✅ API调用成功!")
            print(f"   标题: {data.get('title', 'N/A')}")
            print(f"   价格: {data.get('subtitle', 'N/A')}")
            print(f"   商城: {data.get('mall', 'N/A')}")
            return True
        else:
            print("❌ API调用失败")
            return False
            
    except Exception as e:
        print(f"❌ API测试过程中发生异常: {e}")
        return False

def main():
    """主函数"""
    try:
        # 获取access_token
        token_info = manual_oauth_flow()
        
        if token_info:
            # 询问是否测试API
            test_api = input(f"\n是否使用此token测试API调用? (Y/n): ").strip().lower()
            if test_api != 'n':
                test_token_with_api(token_info)
            
            print(f"\n🎉 完成!")
            print(f"💡 使用方法:")
            print(f"   1. Token已保存到 access_token.txt")
            print(f"   2. 在代码中使用:")
            print(f"      from src.fc_module.tools.zhidemai_info import get_haojia_detail_simple")
            print(f"      data = get_haojia_detail_simple('13284568', '{token_info['access_token']}')")
            print(f"   3. 或运行: python test_haojia_detail_fixed.py 进行完整测试")
        else:
            print(f"\n❌ 获取access_token失败")
            print(f"请检查配置并重试")
            
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
