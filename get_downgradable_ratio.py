#!/usr/bin/env python3
"""
直接获取ES索引中downgradable=1的具体比例数值
"""

import sys
import pathlib

# 添加项目路径
sys.path.insert(0, str(pathlib.Path(__file__).parent / "src"))

from fc_module.utils.elastic_search_service import ElasticsearchService

def get_downgradable_ratio():
    """获取downgradable=1的具体比例"""
    try:
        es_service = ElasticsearchService(index_name="llm_router_evaluation_results")
        
        # 查询downgradable字段的分布
        query = {
            "size": 0,
            "aggs": {
                "downgradable_counts": {
                    "terms": {
                        "field": "downgradable",
                        "size": 10
                    }
                },
                "total": {
                    "value_count": {
                        "field": "userId"
                    }
                }
            }
        }
        
        response = es_service.client.search(
            index=es_service.index_name,
            body=query
        )
        
        aggregations = response.get("aggregations", {})
        buckets = aggregations.get("downgradable_counts", {}).get("buckets", [])
        total = aggregations.get("total", {}).get("value", 0)
        
        downgradable_1 = 0
        downgradable_0 = 0
        
        for bucket in buckets:
            key = bucket.get("key")
            count = bucket.get("doc_count", 0)
            if key == 1:
                downgradable_1 = count
            elif key == 0:
                downgradable_0 = count
        
        ratio = (downgradable_1 / total * 100) if total > 0 else 0
        
        print("=== 当前ES索引中downgradable=1的具体数值 ===")
        print(f"总记录数: {total}")
        print(f"downgradable=1: {downgradable_1} 条")
        print(f"downgradable=0: {downgradable_0} 条")
        print(f"downgradable=1的比例: {ratio:.2f}%")
        
        return {
            "total_records": total,
            "downgradable_1": downgradable_1,
            "downgradable_0": downgradable_0,
            "downgradable_1_ratio": f"{ratio:.2f}%"
        }
        
    except Exception as e:
        print(f"查询错误: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    result = get_downgradable_ratio()
    print(f"\n结果: {result}")
