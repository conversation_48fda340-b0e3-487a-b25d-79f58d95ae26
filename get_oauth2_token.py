#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Zhidemai OAuth2.0 授权码获取和access_token交换工具
根据官方文档实现: https://openapi.zhidemai.com/pages/oauth2/1.%E6%A0%B9%E6%8D%AEcode%E8%8E%B7%E5%8F%96access_token.html
"""

import requests
import json
from urllib.parse import urlencode

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def generate_authorization_url(redirect_uri, state=None):
    """
    生成OAuth2.0授权URL

    Args:
        redirect_uri (str): 回调地址，必须与开发者平台注册的地址一致
        state (str): 可选的状态参数，用于防止CSRF攻击

    Returns:
        str: 授权URL
    """
    # 使用正确的授权端点 - 测试发现smzdm.com会重定向到www.smzdm.com
    base_url = "https://smzdm.com/oauth2/authorize"
    
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri
    }
    
    if state:
        params["state"] = state
    
    query_string = urlencode(params)
    authorization_url = f"{base_url}?{query_string}"
    
    return authorization_url

def get_access_token_by_code(code):
    """
    根据授权码获取access_token
    
    Args:
        code (str): 用户授权后获取到的code参数
    
    Returns:
        dict: API响应数据
    """
    if not code:
        raise ValueError("code 参数不能为空")
    
    # 根据官方文档的接口地址
    url = "https://openapi.smzdm.com/v1/oauth/check/code"
    
    # 请求参数
    params = {
        "app_key": APP_KEY,
        "code": code,
        "app_secret": APP_SECRET
    }
    
    print(f"正在获取access_token...")
    print(f"请求URL: {url}")
    print(f"请求参数: {{'app_key': '{APP_KEY}', 'code': '{code[:10]}...', 'app_secret': '***'}}")
    
    try:
        # 发起POST请求
        response = requests.post(url, data=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            error_code = result.get("error_code", "")
            if error_code == "0":
                print("✅ access_token获取成功!")
                data = result.get("data", {})
                access_token = data.get("access_token", "")
                expires_in = data.get("expires_in", 0)
                union_id = data.get("union_id", "")
                
                print(f"  🔑 access_token: {access_token}")
                print(f"  ⏰ expires_in: {expires_in}秒 ({expires_in//3600}小时)")
                print(f"  👤 union_id: {union_id}")
                
                return result
            else:
                error_msg = result.get("error_msg", "未知错误")
                print(f"❌ 获取access_token失败: {error_code} - {error_msg}")
                return result
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        print(f"原始响应: {response.text}")
        return None

def main():
    """
    主函数 - 完整的OAuth2.0流程
    """
    print("🔐 Zhidemai OAuth2.0 授权码流程")
    print("=" * 50)

    # 步骤1: 生成授权URL
    print("\n📋 步骤1: 生成授权URL")
    print("-" * 30)

    # 配置回调地址 - 默认使用本地地址
    default_redirect = "http://localhost:8080/callback"
    redirect_uri = input(f"请输入回调地址 (默认: {default_redirect}): ").strip()
    if not redirect_uri:
        redirect_uri = default_redirect
        print(f"使用本地回调地址: {redirect_uri}")

    state = input("请输入状态参数 (可选，直接回车跳过): ").strip() or None
    
    # 生成授权URL
    try:
        auth_url = generate_authorization_url(redirect_uri, state)
        print(f"\n🔗 授权URL:")
        print(f"{auth_url}")
        
        print(f"\n📝 使用说明:")
        print("1. 复制上面的授权URL")
        print("2. 在浏览器中打开该URL")
        print("3. 登录您的值得买账号")
        print("4. 点击授权按钮")
        print("5. 浏览器会跳转到回调地址，URL中包含code参数")
        print("6. 从URL中复制code参数的值")
        
        print(f"\n💡 回调URL格式示例:")
        print(f"{redirect_uri}?code=AUTHORIZATION_CODE&state={state or 'N/A'}")
        
    except Exception as e:
        print(f"❌ 生成授权URL失败: {e}")
        return
    
    # 步骤2: 获取授权码并交换token
    print("\n" + "=" * 50)
    print("📋 步骤2: 获取access_token")
    print("-" * 30)
    
    code = input("\n请输入从回调URL中获取的授权码 (code参数): ").strip()
    
    if not code:
        print("❌ 未输入授权码，程序退出")
        return
    
    # 交换access_token
    token_response = get_access_token_by_code(code)
    
    if token_response and token_response.get("error_code") == "0":
        print("\n🎉 OAuth2.0流程完成!")
        data = token_response.get("data", {})
        access_token = data.get("access_token", "")
        
        print(f"\n📋 Token信息:")
        print(f"access_token: {access_token}")
        print(f"expires_in: {data.get('expires_in', 0)}秒")
        print(f"union_id: {data.get('union_id', '')}")
        
        print(f"\n💻 使用示例:")
        print("现在您可以使用这个access_token调用需要OAuth2.0认证的API接口")
        
        # 保存到文件
        save_choice = input("\n是否保存token到文件? (y/n): ").strip().lower()
        if save_choice == 'y':
            with open('access_token.txt', 'w') as f:
                f.write(f"access_token={access_token}\n")
                f.write(f"expires_in={data.get('expires_in', 0)}\n")
                f.write(f"union_id={data.get('union_id', '')}\n")
            print("✅ Token已保存到 access_token.txt 文件")
    else:
        print("\n❌ 获取access_token失败")
        if token_response:
            error_msg = token_response.get("error_msg", "未知错误")
            print(f"错误信息: {error_msg}")

if __name__ == "__main__":
    main()
