#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Zhidemai OAuth2.0 本地授权工具
支持本地回调服务器自动获取授权码
"""

import requests
import json
import webbrowser
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlencode, urlparse, parse_qs

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

class SimpleCallbackHandler(BaseHTTPRequestHandler):
    """简单的回调处理器"""
    
    def do_GET(self):
        if self.path.startswith('/callback'):
            # 解析URL参数
            parsed_url = urlparse(self.path)
            query_params = parse_qs(parsed_url.query)
            
            code = query_params.get('code', [None])[0]
            error = query_params.get('error', [None])[0]
            
            if code:
                # 保存授权码到服务器对象
                self.server.authorization_code = code
                
                # 发送成功页面
                html = """
                <!DOCTYPE html>
                <html>
                <head><title>授权成功</title><meta charset="utf-8"></head>
                <body style="font-family: Arial; text-align: center; margin-top: 100px;">
                    <h1 style="color: green;">✅ 授权成功!</h1>
                    <p>已收到授权码，正在获取access_token...</p>
                    <p>您可以关闭此页面，返回终端查看结果。</p>
                </body>
                </html>
                """
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            else:
                # 授权失败
                self.server.authorization_code = None
                html = f"""
                <!DOCTYPE html>
                <html>
                <head><title>授权失败</title><meta charset="utf-8"></head>
                <body style="font-family: Arial; text-align: center; margin-top: 100px;">
                    <h1 style="color: red;">❌ 授权失败</h1>
                    <p>错误: {error or '未知错误'}</p>
                    <p>请返回终端重试。</p>
                </body>
                </html>
                """
                self.send_response(400)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        """禁用日志输出"""
        pass

def generate_authorization_url(redirect_uri, state=None):
    """生成OAuth2.0授权URL"""
    # 使用正确的授权端点 - 测试发现smzdm.com会重定向到www.smzdm.com
    base_url = "https://smzdm.com/oauth2/authorize"
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri
    }
    
    if state:
        params["state"] = state
    
    query_string = urlencode(params)
    return f"{base_url}?{query_string}"

def get_access_token_by_code(code):
    """根据授权码获取access_token"""
    if not code:
        raise ValueError("code 参数不能为空")
    
    url = "https://openapi.smzdm.com/v1/oauth/check/code"
    params = {
        "app_key": APP_KEY,
        "code": code,
        "app_secret": APP_SECRET
    }
    
    print(f"🔄 正在获取access_token...")
    print(f"请求URL: {url}")
    print(f"授权码: {code[:10]}...")
    
    try:
        response = requests.post(url, data=params, timeout=10)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            error_code = result.get("error_code", "")
            if error_code == "0":
                print("✅ access_token获取成功!")
                data = result.get("data", {})
                access_token = data.get("access_token", "")
                expires_in = data.get("expires_in", 0)
                union_id = data.get("union_id", "")
                
                print(f"  🔑 access_token: {access_token}")
                print(f"  ⏰ expires_in: {expires_in}秒 ({expires_in//3600}小时)")
                print(f"  👤 union_id: {union_id}")
                
                return result
            else:
                error_msg = result.get("error_msg", "未知错误")
                print(f"❌ 获取access_token失败: {error_code} - {error_msg}")
                return result
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def start_local_oauth_flow():
    """启动本地OAuth2.0流程"""
    print("🚀 启动本地OAuth2.0授权流程")
    print("=" * 50)
    
    # 配置
    port = 8080
    redirect_uri = f"http://localhost:{port}/callback"
    state = f"oauth_{int(time.time())}"
    
    print(f"📍 本地回调地址: {redirect_uri}")
    print(f"🔐 状态参数: {state}")
    
    # 生成授权URL
    auth_url = generate_authorization_url(redirect_uri, state)
    print(f"🔗 授权URL: {auth_url}")
    
    # 启动本地服务器
    try:
        server = HTTPServer(('localhost', port), SimpleCallbackHandler)
        server.authorization_code = None
        
        print(f"\n🌐 启动本地服务器 (端口: {port})...")
        
        # 在新线程中运行服务器
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        print("✅ 本地服务器已启动")
        
        # 自动打开浏览器
        print("\n🌍 正在打开浏览器进行授权...")
        try:
            webbrowser.open(auth_url)
            print("✅ 浏览器已打开，请完成授权")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"请手动复制以下URL到浏览器:")
            print(f"{auth_url}")
        
        print("\n⏳ 等待授权完成...")
        print("💡 完成授权后，浏览器会自动跳转到本地页面")
        print("🛑 按 Ctrl+C 可以随时停止")
        
        # 等待授权码
        timeout = 300  # 5分钟超时
        start_time = time.time()
        
        while server.authorization_code is None:
            if time.time() - start_time > timeout:
                print("\n⏰ 等待超时，请重试")
                server.shutdown()
                return None
            time.sleep(1)
        
        # 获取授权码
        code = server.authorization_code
        server.shutdown()
        
        if code:
            print(f"\n✅ 收到授权码: {code[:10]}...")
            
            # 交换access_token
            token_response = get_access_token_by_code(code)
            
            if token_response and token_response.get("error_code") == "0":
                print(f"\n🎉 OAuth2.0流程完成!")
                data = token_response.get("data", {})
                access_token = data.get("access_token", "")
                expires_in = data.get("expires_in", 0)
                union_id = data.get("union_id", "")
                
                # 保存到文件
                with open('access_token.txt', 'w') as f:
                    f.write(f"access_token={access_token}\n")
                    f.write(f"expires_in={expires_in}\n")
                    f.write(f"union_id={union_id}\n")
                    f.write(f"code={code}\n")
                
                print("💾 Token信息已保存到 access_token.txt")
                
                return {
                    'access_token': access_token,
                    'expires_in': expires_in,
                    'union_id': union_id,
                    'code': code
                }
            else:
                print(f"\n❌ 获取access_token失败")
                return None
        else:
            print(f"\n❌ 未收到有效的授权码")
            return None
            
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ 端口 {port} 已被占用，请关闭占用该端口的程序或使用其他端口")
        else:
            print(f"❌ 启动服务器失败: {e}")
        return None
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断授权流程")
        if 'server' in locals():
            server.shutdown()
        return None

def manual_oauth_flow():
    """手动OAuth2.0流程"""
    print("📋 手动OAuth2.0授权流程")
    print("=" * 40)
    
    # 生成授权URL
    redirect_uri = "http://localhost:8080/callback"
    state = f"manual_{int(time.time())}"
    
    auth_url = generate_authorization_url(redirect_uri, state)
    
    print(f"🔗 授权URL:")
    print(f"{auth_url}")
    
    print(f"\n📝 操作步骤:")
    print("1. 复制上面的授权URL")
    print("2. 在浏览器中打开该URL")
    print("3. 登录值得买账号并授权")
    print("4. 从重定向的URL中复制code参数")
    
    code = input("\n请输入获取到的授权码: ").strip()
    
    if code:
        token_response = get_access_token_by_code(code)
        
        if token_response and token_response.get("error_code") == "0":
            data = token_response.get("data", {})
            access_token = data.get("access_token", "")
            
            # 保存到文件
            with open('access_token.txt', 'w') as f:
                f.write(f"access_token={access_token}\n")
                f.write(f"expires_in={data.get('expires_in', 0)}\n")
                f.write(f"union_id={data.get('union_id', '')}\n")
                f.write(f"code={code}\n")
            
            print("💾 Token信息已保存到 access_token.txt")
            return True
    
    return False

def main():
    """主函数"""
    print("🔐 Zhidemai OAuth2.0 本地授权工具")
    print("选择授权方式:")
    print("1. 自动模式 (推荐) - 启动本地服务器自动获取")
    print("2. 手动模式 - 手动复制授权码")
    
    choice = input("\n请选择 (1-2): ").strip()
    
    try:
        if choice == "1":
            result = start_local_oauth_flow()
            if result:
                print(f"\n🎉 授权成功! 可以使用test_access_token.py测试token")
        elif choice == "2":
            success = manual_oauth_flow()
            if success:
                print(f"\n🎉 授权成功! 可以使用test_access_token.py测试token")
        else:
            print("❌ 无效选择")
    except Exception as e:
        print(f"❌ 发生异常: {e}")

if __name__ == "__main__":
    main()
