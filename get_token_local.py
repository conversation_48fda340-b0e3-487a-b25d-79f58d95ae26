#!/usr/bin/env python3
"""
本地获取Zhidemai access_token的简化方法
基于官方文档：
- https://openapi.zhidemai.com/pages/oauth2/1.%E6%A0%B9%E6%8D%AEcode%E8%8E%B7%E5%8F%96access_token.html
- https://openapi.zhidemai.com/pages/rookie/3.OAuth2.0%E8%B0%83%E7%94%A8%E8%AF%A6%E8%A7%A3.html

使用方法:
python get_token_local.py
"""

import sys
import os
import requests
import json

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import APP_KEY, APP_SECRET

def test_different_auth_urls():
    """
    测试不同的授权URL，找到正确的端点
    """
    print("🔍 测试不同的授权URL端点")
    print("=" * 50)
    
    # 可能的授权URL列表
    possible_urls = [
        "https://openapi.smzdm.com/oauth2/authorize",
        "https://openapi.zhidemai.com/oauth2/authorize", 
        "https://www.smzdm.com/oauth2/authorize",
        "https://zhidemai.com/oauth2/authorize",
        "https://smzdm.com/oauth2/authorize"
    ]
    
    redirect_uri = "https://example.com/callback"
    
    for base_url in possible_urls:
        print(f"\n🔗 测试: {base_url}")
        
        # 构建授权URL
        auth_url = f"{base_url}?response_type=code&client_id={APP_KEY}&redirect_uri={redirect_uri}"
        
        try:
            # 发送HEAD请求检查URL是否存在
            response = requests.head(auth_url, timeout=10, allow_redirects=True)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 可用: {base_url}")
                return base_url
            elif response.status_code == 404:
                print(f"   ❌ 404: 端点不存在")
            elif response.status_code == 302 or response.status_code == 301:
                print(f"   🔄 重定向: 可能是正确的授权端点")
                return base_url
            else:
                print(f"   ⚠️  状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 请求失败: {e}")
    
    print(f"\n❌ 未找到可用的授权URL端点")
    return None

def manual_oauth_flow():
    """
    手动OAuth2.0流程 - 让用户自己处理授权
    """
    print("\n🔐 手动OAuth2.0授权流程")
    print("=" * 50)
    
    if not APP_KEY or not APP_SECRET:
        print("❌ 错误: APP_KEY和APP_SECRET必须设置")
        return None
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"🔐 APP_SECRET: {APP_SECRET[:8]}...")
    
    # 让用户选择授权方式
    print(f"\n📋 授权方式选择:")
    print("1. 我来提供正确的授权URL")
    print("2. 使用常见的授权URL模式")
    print("3. 我已经有授权码，直接获取token")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        auth_url = input("请输入正确的授权URL (不包含参数): ").strip()
        if not auth_url:
            print("❌ 授权URL不能为空")
            return None
    elif choice == "2":
        # 使用最可能的授权URL
        auth_url = "https://www.smzdm.com/oauth2/authorize"
        print(f"使用默认URL: {auth_url}")
    elif choice == "3":
        # 直接跳到获取token步骤
        return get_token_by_code()
    else:
        print("❌ 无效选择")
        return None
    
    # 构建完整的授权URL
    redirect_uri = "https://example.com/callback"
    state = f"local_flow_{int(__import__('time').time())}"
    
    full_auth_url = f"{auth_url}?response_type=code&client_id={APP_KEY}&redirect_uri={redirect_uri}&state={state}"
    
    print(f"\n🔗 完整授权URL:")
    print(f"{full_auth_url}")
    
    print(f"\n📋 授权步骤:")
    print("1. 复制上面的URL到浏览器")
    print("2. 登录你的值得买账号")
    print("3. 点击授权按钮")
    print("4. 浏览器会跳转到回调地址")
    print("5. 从回调URL中复制code参数")
    
    print(f"\n💡 回调URL格式:")
    print(f"{redirect_uri}?code=AUTHORIZATION_CODE&state={state}")
    
    # 获取授权码
    code = input(f"\n请输入授权码: ").strip()
    if not code:
        print("❌ 授权码不能为空")
        return None
    
    # 获取access_token
    return get_token_by_code(code)

def get_token_by_code(code=None):
    """
    根据授权码获取access_token
    """
    if not code:
        code = input("请输入授权码: ").strip()
        if not code:
            print("❌ 授权码不能为空")
            return None
    
    print(f"\n🔄 正在获取access_token...")
    print(f"授权码: {code[:20]}...")
    
    # 根据官方文档的接口地址
    token_url = "https://openapi.smzdm.com/v1/oauth/check/code"
    
    # 请求参数
    data = {
        "code": code,
        "app_secret": APP_SECRET
    }
    
    print(f"🔗 Token接口: {token_url}")
    print(f"📋 请求参数: {list(data.keys())}")
    
    try:
        response = requests.post(token_url, data=data, timeout=30)
        print(f"📡 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            error_code = result.get("error_code", "")
            if error_code == "0":
                # 成功获取token
                data = result.get("data", {})
                access_token = data.get("access_token", "")
                expires_in = data.get("expires_in", 0)
                union_id = data.get("union_id", "")
                
                print(f"\n🎉 access_token获取成功!")
                print(f"🎫 Token: {access_token}")
                print(f"⏰ 有效期: {expires_in}秒 (约{expires_in//3600}小时)")
                print(f"👤 用户ID: {union_id}")
                
                # 保存到文件
                save_token_to_file(access_token, expires_in, union_id)
                
                return access_token
            else:
                error_msg = result.get("error_msg", "未知错误")
                print(f"❌ 获取token失败: {error_code} - {error_msg}")
                
                # 错误码分析
                if error_code == "200002":
                    print("💡 可能原因: 请求中没有code参数")
                elif error_code == "200003":
                    print("💡 可能原因: code无效或已过期")
                elif error_code == "200004":
                    print("💡 可能原因: code与app_key不匹配")
                
                return None
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return None

def save_token_to_file(access_token, expires_in, union_id):
    """
    保存token到文件
    """
    try:
        import time
        expires_at = int(time.time()) + expires_in
        
        with open('access_token.txt', 'w', encoding='utf-8') as f:
            f.write(f"access_token={access_token}\n")
            f.write(f"expires_in={expires_in}\n")
            f.write(f"union_id={union_id}\n")
            f.write(f"expires_at={expires_at}\n")
        
        print(f"💾 Token已保存到 access_token.txt")
        
    except Exception as e:
        print(f"⚠️  保存文件失败: {e}")

def test_token_with_api(access_token):
    """
    使用token测试API调用
    """
    print(f"\n🧪 测试API调用")
    print("-" * 30)
    
    try:
        from fc_module.tools.zhidemai_info import get_haojia_info_detail
        
        test_article_id = "13284568"
        print(f"测试文章ID: {test_article_id}")
        
        data = get_haojia_info_detail(test_article_id, version="2", access_token=access_token)
        
        if data and data.get("error_code") == "0":
            print("✅ API调用成功!")
            article_data = data.get("data", {})
            print(f"   标题: {article_data.get('article_title', 'N/A')}")
            print(f"   价格: {article_data.get('article_subtitle', 'N/A')}")
            return True
        else:
            error_msg = data.get("error_msg", "未知错误") if data else "请求失败"
            print(f"❌ API调用失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return False

def main():
    """
    主函数
    """
    print("🔐 本地获取Zhidemai access_token")
    print("=" * 60)
    
    try:
        # 测试不同的授权URL
        print("🔍 首先测试授权URL端点...")
        auth_base_url = test_different_auth_urls()
        
        if auth_base_url:
            print(f"✅ 找到可用的授权URL: {auth_base_url}")
        
        # 执行OAuth2.0流程
        access_token = manual_oauth_flow()
        
        if access_token:
            # 测试API调用
            test_api = input(f"\n是否测试API调用? (Y/n): ").strip().lower()
            if test_api != 'n':
                test_token_with_api(access_token)
            
            print(f"\n🎉 完成!")
            print(f"💡 Token已保存，可以在代码中使用:")
            print(f"   from src.fc_module.tools.zhidemai_info import get_haojia_info_detail")
            print(f"   data = get_haojia_info_detail('13284568', access_token='{access_token}')")
        else:
            print(f"\n❌ 获取access_token失败")
            
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
