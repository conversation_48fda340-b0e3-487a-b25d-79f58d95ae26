#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动输入授权码获取access_token
"""

import requests
import json

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def get_access_token_by_code(code):
    """根据授权码获取access_token"""
    if not code:
        raise ValueError("code 参数不能为空")
    
    url = "https://openapi.smzdm.com/v1/oauth/check/code"
    params = {
        "app_key": APP_KEY,
        "code": code,
        "app_secret": APP_SECRET
    }
    
    print(f"🔄 正在获取access_token...")
    print(f"请求URL: {url}")
    print(f"授权码: {code[:10]}...")
    
    try:
        response = requests.post(url, data=params, timeout=10)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            error_code = result.get("error_code", "")
            if error_code == "0":
                print("✅ access_token获取成功!")
                data = result.get("data", {})
                access_token = data.get("access_token", "")
                expires_in = data.get("expires_in", 0)
                union_id = data.get("union_id", "")
                
                print(f"  🔑 access_token: {access_token}")
                print(f"  ⏰ expires_in: {expires_in}秒 ({expires_in//3600}小时)")
                print(f"  👤 union_id: {union_id}")
                
                # 保存到文件
                with open('access_token.txt', 'w') as f:
                    f.write(f"access_token={access_token}\n")
                    f.write(f"expires_in={expires_in}\n")
                    f.write(f"union_id={union_id}\n")
                    f.write(f"code={code}\n")
                
                print("💾 Token信息已保存到 access_token.txt")
                return result
            else:
                error_msg = result.get("error_msg", "未知错误")
                print(f"❌ 获取access_token失败: {error_code} - {error_msg}")
                return result
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def main():
    """主函数"""
    print("🔐 手动获取Access Token")
    print("=" * 40)
    
    print("📝 操作说明:")
    print("1. 确保你已经在浏览器中完成了授权")
    print("2. 从浏览器地址栏复制授权码")
    print("3. 输入授权码获取access_token")
    
    print(f"\n💡 授权码位置示例:")
    print("http://localhost:8080/callback?code=ABC123XYZ&state=...")
    print("                                    ^^^^^^^^^ 复制这部分")
    
    code = input("\n请输入从浏览器地址栏获取的授权码: ").strip()
    
    if not code:
        print("❌ 未输入授权码，程序退出")
        return
    
    # 获取access_token
    token_response = get_access_token_by_code(code)
    
    if token_response and token_response.get("error_code") == "0":
        print(f"\n🎉 OAuth2.0流程完成!")
        print(f"现在可以使用access_token调用需要OAuth2.0认证的API了！")
        
        # 询问是否测试token
        test_token = input("\n是否测试获取到的token? (y/n): ").strip().lower()
        if test_token == 'y':
            print("运行: python test_access_token.py")
    else:
        print(f"\n❌ 获取access_token失败")

if __name__ == "__main__":
    main()
