#!/usr/bin/env python3
"""
Zhidemai 好价信息详情接口使用示例
展示如何在实际项目中使用封装的API函数

注意: 需要有效的access_token才能运行
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    get_haojia_info_detail,
    get_haojia_detail_simple,
    print_haojia_detail_summary,
    parse_haojia_detail_v2
)

def example_usage():
    """
    实际使用示例
    """
    print("📱 Zhidemai 好价信息详情接口使用示例")
    print("=" * 50)
    
    # 示例1: 最简单的使用方式
    print("\n🔥 示例1: 简单调用")
    print("-" * 30)
    
    article_id = "13284568"  # 替换为实际的文章ID
    
    # 使用简化版函数，自动处理解析和错误
    detail_data = get_haojia_detail_simple(article_id)
    
    if detail_data:
        print("✅ 获取成功!")
        # 使用内置的格式化输出
        print_haojia_detail_summary(detail_data)
        
        # 访问具体字段
        print(f"\n📋 具体字段访问:")
        print(f"   标题: {detail_data.get('title', 'N/A')}")
        print(f"   价格: {detail_data.get('subtitle', 'N/A')}")
        print(f"   商城: {detail_data.get('mall', 'N/A')}")
        print(f"   商品链接: {detail_data.get('product', {}).get('clean_link', 'N/A')}")
        
    else:
        print("❌ 获取失败，请检查article_id和access_token")
    
    # 示例2: 高级使用方式
    print("\n🔧 示例2: 高级调用")
    print("-" * 30)
    
    # 指定版本和access_token
    your_access_token = "YOUR_ACTUAL_ACCESS_TOKEN_HERE"  # 替换为实际token
    
    # 获取版本2的详细信息
    raw_response = get_haojia_info_detail(
        article_id=article_id,
        version="2",
        access_token=your_access_token
    )
    
    if raw_response and raw_response.get("error_code") == "0":
        print("✅ API调用成功!")
        
        # 手动解析数据
        parsed_data = parse_haojia_detail_v2(raw_response)
        
        if parsed_data:
            print("✅ 数据解析成功!")
            
            # 访问详细信息
            print(f"\n📊 详细信息:")
            print(f"   文章ID: {parsed_data.get('article_id', 'N/A')}")
            print(f"   发布时间: {parsed_data.get('publish_date', 'N/A')}")
            print(f"   文章类型: {parsed_data.get('article_type', 'N/A')}")
            
            # 用户信息
            user_info = parsed_data.get('user', {})
            print(f"   爆料人: {user_info.get('nickname', 'N/A')}")
            
            # 统计信息
            stats = parsed_data.get('stats', {})
            print(f"   点赞: {stats.get('worthy', 0)}")
            print(f"   收藏: {stats.get('collection', 0)}")
            print(f"   评论: {stats.get('comment', 0)}")
            
            # 优惠券信息
            coupons = parsed_data.get('coupons', [])
            if coupons:
                print(f"\n🎫 优惠券信息 ({len(coupons)}张):")
                for i, coupon in enumerate(coupons, 1):
                    print(f"   {i}. {coupon.get('title', 'N/A')}")
                    if coupon.get('url'):
                        print(f"      链接: {coupon.get('url')}")
            
            # 特殊标签
            special_tags = parsed_data.get('special_tags', [])
            if special_tags:
                print(f"\n🏷️  特殊标签:")
                for tag in special_tags:
                    print(f"   - {tag.get('name', 'N/A')} (类型: {tag.get('tag_type', 'N/A')})")
            
            # 值法标签
            zhifa_tags = parsed_data.get('zhifa_tags', [])
            if zhifa_tags:
                print(f"\n🎯 值法标签: {', '.join(zhifa_tags)}")
        
    else:
        error_msg = raw_response.get("error_msg", "未知错误") if raw_response else "请求失败"
        print(f"❌ API调用失败: {error_msg}")
    
    # 示例3: 批量处理
    print("\n📦 示例3: 批量处理")
    print("-" * 30)
    
    article_ids = ["13284568", "21513355", "12345678"]  # 示例ID列表
    
    print(f"批量处理 {len(article_ids)} 个文章...")
    
    results = []
    for i, aid in enumerate(article_ids, 1):
        print(f"处理 {i}/{len(article_ids)}: {aid}")
        
        data = get_haojia_detail_simple(aid)
        if data:
            results.append({
                'article_id': aid,
                'title': data.get('title', 'N/A'),
                'price': data.get('subtitle', 'N/A'),
                'mall': data.get('mall', 'N/A'),
                'status': 'success'
            })
            print(f"   ✅ 成功: {data.get('title', 'N/A')[:30]}...")
        else:
            results.append({
                'article_id': aid,
                'status': 'failed'
            })
            print(f"   ❌ 失败")
    
    print(f"\n📋 批量处理结果:")
    success_count = sum(1 for r in results if r['status'] == 'success')
    print(f"   成功: {success_count}/{len(article_ids)}")
    print(f"   失败: {len(article_ids) - success_count}/{len(article_ids)}")

def main():
    """
    主函数
    """
    print("🚀 开始运行使用示例...")
    
    try:
        example_usage()
        
        print("\n" + "=" * 50)
        print("✨ 使用提示:")
        print("1. 确保设置有效的ACCESS_TOKEN")
        print("2. 使用真实的article_id进行测试")
        print("3. 处理API错误和网络异常")
        print("4. 根据需要选择版本1或版本2")
        print("5. 利用解析函数提取所需字段")
        
        print("\n📖 函数说明:")
        print("- get_haojia_detail_simple(): 推荐的简单调用方式")
        print("- get_haojia_info_detail(): 完整的API调用函数")
        print("- parse_haojia_detail_v1/v2(): 数据解析函数")
        print("- print_haojia_detail_summary(): 格式化输出")
        
    except Exception as e:
        print(f"\n❌ 运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
