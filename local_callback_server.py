#!/usr/bin/env python3
"""
本地OAuth2.0回调服务器
用于接收Zhidemai OAuth2.0授权回调并提取授权码

使用方法:
1. 运行此脚本启动本地服务器
2. 使用 http://localhost:8080/callback 作为回调地址
3. 完成授权后，服务器会自动提取并显示授权码
"""

import sys
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser
import threading
import time

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    generate_authorization_url,
    get_access_token_by_code,
    parse_access_token_response,
    APP_KEY,
    APP_SECRET
)

class CallbackHandler(BaseHTTPRequestHandler):
    """处理OAuth2.0回调请求"""
    
    def do_GET(self):
        """处理GET请求"""
        # 解析URL和参数
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        print(f"\n📡 收到回调请求: {self.path}")
        
        if parsed_url.path == '/callback':
            # 提取授权码和状态参数
            code = query_params.get('code', [None])[0]
            state = query_params.get('state', [None])[0]
            error = query_params.get('error', [None])[0]
            
            if error:
                # 授权失败
                response_html = self.generate_error_page(error)
                print(f"❌ 授权失败: {error}")
            elif code:
                # 授权成功，获取access_token
                response_html = self.handle_authorization_success(code, state)
            else:
                # 缺少授权码
                response_html = self.generate_error_page("缺少授权码参数")
                print("❌ 回调请求中缺少code参数")
        else:
            # 其他路径
            response_html = self.generate_info_page()
        
        # 发送响应
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(response_html.encode('utf-8'))
    
    def handle_authorization_success(self, code, state):
        """处理授权成功的情况"""
        print(f"✅ 授权成功!")
        print(f"   授权码: {code}")
        print(f"   状态参数: {state}")
        
        # 尝试获取access_token
        print(f"\n🔄 正在获取access_token...")
        
        try:
            token_response = get_access_token_by_code(code)
            
            if token_response and token_response.get("error_code") == "0":
                # 成功获取token
                token_info = parse_access_token_response(token_response)
                access_token = token_info['access_token']
                expires_in = token_info['expires_in']
                union_id = token_info['union_id']
                
                print(f"🎉 access_token获取成功!")
                print(f"   Token: {access_token}")
                print(f"   过期时间: {expires_in}秒")
                print(f"   用户ID: {union_id}")
                
                # 保存到文件
                self.save_token_to_file(token_info)
                
                return self.generate_success_page(access_token, expires_in, union_id)
            else:
                # 获取token失败
                error_msg = token_response.get("error_msg", "未知错误") if token_response else "请求失败"
                print(f"❌ 获取access_token失败: {error_msg}")
                return self.generate_token_error_page(code, error_msg)
                
        except Exception as e:
            print(f"❌ 获取access_token过程中发生异常: {e}")
            return self.generate_token_error_page(code, str(e))
    
    def save_token_to_file(self, token_info):
        """保存token信息到文件"""
        try:
            with open('access_token.txt', 'w', encoding='utf-8') as f:
                f.write(f"access_token={token_info['access_token']}\n")
                f.write(f"expires_in={token_info['expires_in']}\n")
                f.write(f"union_id={token_info['union_id']}\n")
                f.write(f"expires_at={token_info['expires_at']}\n")
            print(f"💾 Token信息已保存到 access_token.txt")
        except Exception as e:
            print(f"⚠️  保存token文件失败: {e}")
    
    def generate_success_page(self, access_token, expires_in, union_id):
        """生成成功页面"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>授权成功 - Zhidemai OAuth2.0</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
                .container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .success {{ color: #28a745; }}
                .token {{ background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; word-break: break-all; }}
                .info {{ margin: 10px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="success">🎉 授权成功!</h1>
                <p>恭喜！您已成功获取access_token，现在可以调用Zhidemai API了。</p>
                
                <h3>📋 Token信息:</h3>
                <div class="info"><strong>Access Token:</strong></div>
                <div class="token">{access_token}</div>
                
                <div class="info"><strong>有效期:</strong> {expires_in}秒 (约{expires_in//3600}小时)</div>
                <div class="info"><strong>用户ID:</strong> {union_id}</div>
                
                <h3>💡 使用方法:</h3>
                <p>1. Token信息已自动保存到 <code>access_token.txt</code> 文件</p>
                <p>2. 在代码中使用此token调用API:</p>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">
from src.fc_module.tools.zhidemai_info import get_haojia_detail_simple

# 使用token调用API
data = get_haojia_detail_simple("13284568", "{access_token}")
print(data)
                </pre>
                
                <p>3. 或者直接运行测试脚本验证token有效性</p>
                
                <p><strong>注意:</strong> 请妥善保管您的access_token，不要泄露给他人。</p>
            </div>
        </body>
        </html>
        """
    
    def generate_token_error_page(self, code, error_msg):
        """生成token获取失败页面"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Token获取失败 - Zhidemai OAuth2.0</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
                .container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .error {{ color: #dc3545; }}
                .code {{ background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="error">❌ Token获取失败</h1>
                <p>授权码获取成功，但在交换access_token时出现错误。</p>
                
                <h3>📋 详细信息:</h3>
                <p><strong>授权码:</strong> <span class="code">{code}</span></p>
                <p><strong>错误信息:</strong> {error_msg}</p>
                
                <h3>🔧 可能的解决方案:</h3>
                <ul>
                    <li>检查APP_KEY和APP_SECRET是否正确配置</li>
                    <li>确认授权码未过期（通常5-10分钟内有效）</li>
                    <li>验证回调地址是否与开发者平台注册一致</li>
                    <li>检查网络连接是否正常</li>
                </ul>
                
                <p>您可以重新进行授权流程，或联系技术支持。</p>
            </div>
        </body>
        </html>
        """
    
    def generate_error_page(self, error):
        """生成错误页面"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>授权失败 - Zhidemai OAuth2.0</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
                .container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .error {{ color: #dc3545; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="error">❌ 授权失败</h1>
                <p>很抱歉，OAuth2.0授权过程中出现错误。</p>
                <p><strong>错误信息:</strong> {error}</p>
                <p>请重新尝试授权流程，或检查应用配置。</p>
            </div>
        </body>
        </html>
        """
    
    def generate_info_page(self):
        """生成信息页面"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Zhidemai OAuth2.0 回调服务器</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔐 Zhidemai OAuth2.0 回调服务器</h1>
                <p>此服务器正在运行，等待OAuth2.0授权回调。</p>
                <p>回调地址: <code>http://localhost:8080/callback</code></p>
                <p>请在Zhidemai开发者平台完成授权后，系统会自动重定向到此地址。</p>
            </div>
        </body>
        </html>
        """
    
    def log_message(self, format, *args):
        """禁用默认的访问日志"""
        pass

def start_callback_server(port=8080):
    """启动回调服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, CallbackHandler)
    
    print(f"🚀 本地回调服务器已启动")
    print(f"   地址: http://localhost:{port}")
    print(f"   回调URL: http://localhost:{port}/callback")
    print(f"   按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n👋 服务器已停止")
        httpd.shutdown()

def main():
    """主函数"""
    print("🔐 Zhidemai OAuth2.0 本地回调服务器")
    print("=" * 50)
    
    if not APP_KEY or not APP_SECRET:
        print("❌ 错误: APP_KEY和APP_SECRET必须设置")
        return
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"🔐 APP_SECRET: {APP_SECRET[:8]}...")
    
    # 生成授权URL
    callback_url = "http://localhost:8080/callback"
    state = f"local_callback_{int(time.time())}"
    
    auth_url = generate_authorization_url(callback_url, state)
    
    print(f"\n📋 OAuth2.0授权流程:")
    print(f"1. 本地回调服务器将在端口8080启动")
    print(f"2. 浏览器将自动打开授权页面")
    print(f"3. 完成授权后自动获取access_token")
    
    # 询问是否继续
    confirm = input(f"\n是否开始授权流程? (Y/n): ").strip().lower()
    if confirm in ['n', 'no']:
        print("👋 已取消")
        return
    
    # 启动服务器（在后台线程）
    server_thread = threading.Thread(target=start_callback_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(1)
    
    # 打开浏览器
    print(f"\n🌐 正在打开浏览器...")
    print(f"授权URL: {auth_url}")
    
    try:
        webbrowser.open(auth_url)
        print("✅ 浏览器已打开，请完成授权")
    except Exception as e:
        print(f"⚠️  无法自动打开浏览器: {e}")
        print(f"请手动复制以下URL到浏览器:")
        print(f"{auth_url}")
    
    # 保持服务器运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print(f"\n👋 程序已退出")

if __name__ == "__main__":
    main()
