from typing import List

import structlog
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel

from src.fc_module.agent_flow import Agent<PERSON>low
from src.cache import redis_cli

# 创建 FastAPI 实例
app = FastAPI()

logger = structlog.get_logger(__name__)


# 创建 AgentFlow 实例
af = AgentFlow(cache=redis_cli)


class InterruptRequest(BaseModel):
    user_id: str
    conversation_id: str
    req_id: str
    intent: str


class BaseMessage(BaseModel):
    role: str
    content: str


# 定义请求模型（根据你的需求调整）
class AgentFlowRequest(BaseModel):
    user_id: str
    req_id: str
    conversation_id: str
    intent: str
    inp: str
    images: List = []
    coords: str
    token: str

    history_str: str = ''
    hist_messages: List[BaseMessage] = []

    @property
    def hist_messages_dict(self) -> List[dict]:
        return [h.model_dump() for h in self.hist_messages]


@app.post("/api/agent_flow")
def run_agent_flow(request: AgentFlowRequest):
    ctx_vars = dict(user_id=request.user_id, req_id=request.req_id, conversation_id=request.conversation_id)
    with structlog.contextvars.bound_contextvars(**ctx_vars):
        try:
            # 创建 AgentFlow 实例
            af = AgentFlow(cache=redis_cli)

            # 调用 main_agent_flow 方法
            res = af.main_agent_flow(
                user_id=request.user_id,
                req_id=request.req_id,
                conversation_id=request.conversation_id,
                intent=request.intent,
                inp=request.inp,
                images=request.images,
                history_str=request.history_str,
                hist_messages=request.hist_messages_dict,
                coords=request.coords,
                token=request.token
            )
            return {"data": res.model_dump()}
        except Exception as e:
            logger.exception("An error occurred: %s", str(e))
            raise HTTPException(status_code=400, detail=f"An error occurred: {str(e)}")


@app.post("/api/agent_flow/interrupt")
def agent_flow_set_interrupt(req: InterruptRequest):
    ctx_vars = dict(user_id=req.user_id, req_id=req.req_id, conversation_id=req.conversation_id)
    with structlog.contextvars.bound_contextvars(**ctx_vars):
        af.set_interrupt_flag(req.user_id, req.conversation_id, req.intent)
        return {"response": "ok"}


@app.get("/health/check")
async def health_check():
    return {"response": "ok"}


if __name__ == "__main__":
    logger.info("Starting FastAPI server...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
