#!/usr/bin/env python3
"""
处理所有tools不为空的数据，计算每个用户请求的可降级性
"""

import asyncio
import json
import logging
from datetime import datetime
import sys
import pathlib

# 添加路径
current_dir = pathlib.Path(__file__).parent
sys.path.append(str(current_dir))

from src.fc_module.offline_procedure.llm_router_evaluator import LLMRouterEvaluator
from src.fc_module.utils.elastic_search_service import ElasticsearchService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AllRecordsProcessor:
    """处理所有记录的专业类"""
    
    def __init__(self):
        self.evaluator = LLMRouterEvaluator()
        
    async def process_all_records(self):
        """处理所有记录"""
        print("开始处理所有tools不为空的数据...")
        
        # 初始化
        await self.evaluator.initialize()
        
        # 获取所有数据
        records = self.evaluator.fetch_function_calling_data(days_back=365)
        total_records = len(records)
        print(f"获取到 {total_records} 条记录")
        
        if not records:
            print("没有找到记录")
            return []
        
        # 批量处理记录
        dataset_entries = []
        batch_size = 10  # 每批处理10条记录
        total_processed = 0
        
        for i in range(0, len(records), batch_size):
            batch = records[i:i+batch_size]
            batch_entries = []
            
            print(f"处理第 {i+1}-{min(i+batch_size, len(records))} 条记录...")
            
            for record in batch:
                try:
                    entry = await self.evaluator.process_single_record(record)
                    if entry:
                        batch_entries.append(entry)
                        total_processed += 1
                        
                    if total_processed % 10 == 0:
                        print(f"已处理 {total_processed}/{total_records} 条记录")
                        
                except Exception as e:
                    logger.error(f"处理记录 {record.get('id')} 时出错: {e}")
            
            # 批量写入ES
            if batch_entries:
                print(f"写入 {len(batch_entries)} 条记录到ES...")
                es_result = self.evaluator.write_to_elasticsearch(batch_entries)
                dataset_entries.extend(batch_entries)
                print(f"ES写入结果: {es_result}")
        
        print(f"处理完成！总共处理了 {total_processed} 条记录")
        return dataset_entries
    
    async def run(self):
        """运行完整流程"""
        try:
            dataset_entries = await self.process_all_records()
            
            # 保存完整数据集
            if dataset_entries:
                output_file = f"all_records_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                self.evaluator.save_dataset(dataset_entries, output_file)
                print(f"数据集已保存到: {output_file}")
                
                # 验证ES中的数据
                es = ElasticsearchService(index_name="llm_router_evaluation_results")
                count = es.client.count(index="llm_router_evaluation_results")['count']
                print(f"ES中总文档数: {count}")
                
                return dataset_entries
            else:
                print("没有成功处理的记录")
                return []
                
        except Exception as e:
            print(f"运行过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return []

async def main():
    """主函数"""
    processor = AllRecordsProcessor()
    await processor.run()

if __name__ == "__main__":
    asyncio.run(main())
