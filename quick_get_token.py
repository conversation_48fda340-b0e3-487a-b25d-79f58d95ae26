#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速获取OAuth2 access_token
"""

import requests
import json
import webbrowser
from urllib.parse import urlencode

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def generate_authorization_url():
    """生成授权URL"""
    redirect_uri = "http://localhost:8080/callback"
    state = f"quick_{int(__import__('time').time())}"
    
    base_url = "https://smzdm.com/oauth2/authorize"
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    query_string = urlencode(params)
    return f"{base_url}?{query_string}", state

def get_access_token_by_code(code):
    """根据授权码获取access_token"""
    url = "https://openapi.smzdm.com/v1/oauth/check/code"
    params = {
        "app_key": APP_KEY,
        "code": code,
        "app_secret": APP_SECRET
    }
    
    print(f"🔄 正在获取access_token...")
    print(f"授权码: {code[:10]}...")
    
    try:
        response = requests.post(url, data=params, timeout=10)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            error_code = result.get("error_code", "")
            if error_code == "0":
                print("✅ access_token获取成功!")
                data = result.get("data", {})
                access_token = data.get("access_token", "")
                expires_in = data.get("expires_in", 0)
                union_id = data.get("union_id", "")
                
                print(f"🔑 access_token: {access_token}")
                print(f"⏰ expires_in: {expires_in}秒 ({expires_in//3600}小时)")
                print(f"👤 union_id: {union_id}")
                
                # 保存到文件
                with open('access_token.txt', 'w') as f:
                    f.write(f"access_token={access_token}\n")
                    f.write(f"expires_in={expires_in}\n")
                    f.write(f"union_id={union_id}\n")
                    f.write(f"code={code}\n")
                
                print("💾 Token信息已保存到 access_token.txt")
                return True
            else:
                error_msg = result.get("error_msg", "未知错误")
                print(f"❌ 获取access_token失败: {error_code} - {error_msg}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速获取Zhidemai Access Token")
    print("=" * 50)
    
    # 生成授权URL
    auth_url, state = generate_authorization_url()
    
    print(f"🔗 授权URL:")
    print(f"{auth_url}")
    
    print(f"\n📝 操作步骤:")
    print("1. 点击上面的URL或复制到浏览器")
    print("2. 登录值得买账号")
    print("3. 点击授权按钮")
    print("4. 浏览器会跳转到 localhost:8080/callback?code=...")
    print("5. 从地址栏复制 code= 后面的值")
    
    # 询问是否自动打开浏览器
    open_browser = input("\n是否自动打开浏览器? (y/n): ").strip().lower()
    
    if open_browser == 'y':
        try:
            print("🌍 正在打开浏览器...")
            webbrowser.open(auth_url)
            print("✅ 浏览器已打开")
        except Exception as e:
            print(f"❌ 无法打开浏览器: {e}")
    
    print(f"\n💡 回调URL示例:")
    print("http://localhost:8080/callback?code=ABC123XYZ&state=...")
    print("                                    ^^^^^^^^^ 复制这部分")
    
    # 等待用户输入授权码
    while True:
        code = input("\n请输入从浏览器地址栏获取的授权码 (输入 'q' 退出): ").strip()
        
        if code.lower() == 'q':
            print("👋 退出程序")
            break
        
        if not code:
            print("❌ 授权码不能为空，请重新输入")
            continue
        
        # 获取access_token
        success = get_access_token_by_code(code)
        
        if success:
            print(f"\n🎉 OAuth2.0流程完成!")
            print("现在可以使用access_token调用需要OAuth2.0认证的API了！")
            
            # 询问是否测试token
            test_token = input("\n是否测试获取到的token? (y/n): ").strip().lower()
            if test_token == 'y':
                print("请运行: python test_access_token.py")
            break
        else:
            print("❌ 获取token失败，请检查授权码是否正确")
            retry = input("是否重试? (y/n): ").strip().lower()
            if retry != 'y':
                break

if __name__ == "__main__":
    main()
