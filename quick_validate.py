#!/usr/bin/env python3
"""
快速验证LLM路由评估结果
"""

import json
import pandas as pd
import sys
import os
import pathlib

# 添加路径
current_dir = pathlib.Path(__file__).parent
sys.path.append(str(current_dir))

from src.fc_module.utils.elastic_search_service import ElasticsearchService

def main():
    print("=== LLM路由评估结果验证 ===\n")
    
    # 1. 检查文件
    json_files = [f for f in os.listdir('.') if f.startswith('llm_router_evaluation_full_') and f.endswith('.json')]
    csv_files = [f for f in os.listdir('.') if f.startswith('model_replacement_opportunities_') and f.endswith('.csv')]
    
    if not json_files or not csv_files:
        print("❌ 未找到评估结果文件")
        return
    
    latest_json = sorted(json_files)[-1]
    latest_csv = sorted(csv_files)[-1]
    
    print(f"📊 评估文件:")
    print(f"  JSON: {latest_json}")
    print(f"  CSV: {latest_csv}")
    
    # 2. 验证JSON
    try:
        with open(latest_json, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"\n✅ JSON: {len(data)} 条记录")
    except Exception as e:
        print(f"❌ JSON错误: {e}")
        return
    
    # 3. 验证CSV
    try:
        df = pd.read_csv(latest_csv)
        print(f"✅ CSV: {len(df)} 条替换记录")
        
        print("\n📈 替换统计:")
        print(f"  总替换机会: {len(df)}")
        
        model_counts = df['可替换模型'].value_counts()
        for model, count in model_counts.items():
            print(f"    {model}: {count} 次")
            
        type_counts = df['替换类型'].value_counts()
        for replace_type, count in type_counts.items():
            print(f"    {replace_type}: {count} 次")
            
    except Exception as e:
        print(f"❌ CSV错误: {e}")
        return
    
    # 4. 验证ES
    try:
        es = ElasticsearchService(index_name="llm_router_evaluation_results")
        if es.client.indices.exists(index="llm_router_evaluation_results"):
            count = es.client.count(index="llm_router_evaluation_results")['count']
            print(f"✅ ES: {count} 条文档")
        else:
            print("⚠️ ES索引不存在")
    except Exception as e:
        print(f"❌ ES错误: {e}")
    
    print("\n🎉 验证完成！")

if __name__ == "__main__":
    main()
