#!/usr/bin/env python3
"""
遍历所有supabase中tools不等于"[]"的数据，分析小尺寸模型替换235b的机会
"""

import asyncio
import json
import csv
from datetime import datetime
import sys
import os
import pathlib

# 添加路径
current_dir = pathlib.Path(__file__).parent
sys.path.append(str(current_dir))

from src.fc_module.offline_procedure.llm_router_evaluator import LLMRouterEvaluator

async def run_complete_evaluation():
    """运行完整的评估流程，处理所有数据"""
    print("开始遍历所有supabase中tools不等于[]的数据...")
    
    # 创建评估器实例
    evaluator = LLMRouterEvaluator()
    
    # 修改评估器以获取所有数据
    evaluator.fetch_function_calling_data = lambda: fetch_all_function_calling_data(evaluator)
    
    # 运行评估（处理所有数据）
    dataset_entries = await evaluator.run_evaluation(days_back=None, max_records=None)
    
    print(f"成功处理了 {len(dataset_entries)} 条记录")
    
    # 保存完整数据集
    output_file = f"complete_evaluation_all_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    evaluator.save_dataset(dataset_entries, output_file)
    
    return dataset_entries, output_file

def fetch_all_function_calling_data(evaluator):
    """获取所有tools不为空的记录"""
    try:
        # 使用同步客户端查询所有数据，不限制时间范围
        response = (
            evaluator.supabase_service.sync_client.table("function_calling_llm_input_param")
            .select("*")
            .neq("tools", "[]")  # 过滤掉tools为空的记录
            .execute()
        )
        
        print(f"获取到 {len(response.data)} 条记录")
        return response.data
        
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return []

def analyze_replacement_opportunities(dataset_entries):
    """分析可以替换235b的小模型机会"""
    print("分析小尺寸模型替换235b的机会...")
    
    analysis_results = []
    total_records = len(dataset_entries)
    
    for i, entry in enumerate(dataset_entries):
        if i % 50 == 0:
            print(f"处理第 {i+1}/{total_records} 条记录...")
            
        user_query = entry.get("user_query", "")
        model_comparisons = entry.get("model_comparisons", {})
        reference_model = entry.get("reference_model", "")
        
        # 检查哪些小模型可以正确完成任务
        correct_small_models = []
        
        for model_name, comparison in model_comparisons.items():
            if model_name != reference_model and comparison.get("is_correct", False):
                # 根据模型名称判断是否为小模型
                is_small_model = any(small in model_name.lower() for small in ["80b", "120b", "max", "next"])
                if is_small_model:
                    correct_small_models.append({
                        "model": model_name,
                        "response": comparison.get("model_response", ""),
                        "model_type": "小尺寸模型" if "80b" in model_name or "120b" in model_name else "快速模型"
                    })
        
        if correct_small_models:
            analysis_results.append({
                "user_query": user_query,
                "reference_model": reference_model,
                "replaceable_models": correct_small_models,
                "replaceable_count": len(correct_small_models),
                "record_id": entry.get("original_record_id"),
                "full_entry": entry
            })
    
    return analysis_results

def create_detailed_csv(analysis_results):
    """创建详细的CSV分析表格"""
    if not analysis_results:
        print("没有找到可替换的机会")
        return None
    
    # 创建CSV文件
    csv_file = f"complete_replacement_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    with open(csv_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = [
            '记录ID', '用户查询', '参考模型', '可替换模型', '模型类型', 
            '替换次数', '替换比例', '用户查询长度'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        
        # 统计信息
        model_stats = {}
        type_stats = {}
        
        for result in analysis_results:
            for model_info in result["replaceable_models"]:
                writer.writerow({
                    "记录ID": result["record_id"],
                    "用户查询": result["user_query"][:200] + "..." if len(result["user_query"]) > 200 else result["user_query"],
                    "参考模型": result["reference_model"],
                    "可替换模型": model_info["model"],
                    "模型类型": model_info["model_type"],
                    "替换次数": len(result["replaceable_models"]),
                    "替换比例": f"{len(result['replaceable_models'])}/3",
                    "用户查询长度": len(result["user_query"])
                })
                
                # 统计
                model_stats[model_info["model"]] = model_stats.get(model_info["model"], 0) + 1
                type_stats[model_info["model_type"]] = type_stats.get(model_info["model_type"], 0) + 1
    
    return csv_file, model_stats, type_stats, len(analysis_results)

async def main():
    """主函数"""
    try:
        # 运行完整评估
        dataset_entries, json_file = await run_complete_evaluation()
        
        if not dataset_entries:
            print("没有获取到任何评估数据")
            return
        
        # 分析替换机会
        analysis_results = analyze_replacement_opportunities(dataset_entries)
        
        # 创建CSV表格
        result = create_detailed_csv(analysis_results)
        
        if result:
            csv_file, model_stats, type_stats, total_scenarios = result
            
            print("\n" + "="*80)
            print("完整评估完成！")
            print("="*80)
            print(f"JSON数据文件: {json_file}")
            print(f"CSV表格文件: {csv_file}")
            print(f"\n总记录数: {len(dataset_entries)}")
            print(f"可替换场景数: {total_scenarios}")
            print(f"替换率: {total_scenarios/len(dataset_entries)*100:.2f}%")
            
            print("\n小模型替换统计:")
            for model, count in sorted(model_stats.items(), key=lambda x: x[1], reverse=True):
                print(f"  {model}: {count} 次")
            
            print("\n替换类型分布:")
            for replace_type, count in type_stats.items():
                print(f"  {replace_type}: {count} 次")
        
        else:
            print("没有找到可以替换235b的小模型机会")
    
    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
