#!/usr/bin/env python3
"""
运行完整的LLM路由评估并分析所有数据
"""

import asyncio
import json
import csv
from datetime import datetime
import sys
import os
import pathlib

# 添加路径
current_dir = pathlib.Path(__file__).parent
sys.path.append(str(current_dir))

from src.fc_module.offline_procedure.llm_router_evaluator import LLMRouterEvaluator

async def run_final_complete_evaluation():
    """运行完整的评估流程，处理所有数据"""
    print("开始运行完整的LLM路由评估...")
    
    # 创建评估器实例
    evaluator = LLMRouterEvaluator()
    
    # 运行评估（处理所有数据，使用足够大的天数）
    dataset_entries = await evaluator.run_evaluation(days_back=365, max_records=None)
    
    print(f"成功处理了 {len(dataset_entries)} 条记录")
    
    # 保存完整数据集
    output_file = f"final_complete_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    evaluator.save_dataset(dataset_entries, output_file)
    
    return dataset_entries, output_file

def analyze_replacement_opportunities(dataset_entries):
    """分析可以替换235b的小模型机会"""
    print("分析小尺寸模型替换235b的机会...")
    
    analysis_results = []
    total_records = len(dataset_entries)
    
    for i, entry in enumerate(dataset_entries):
        if i % 50 == 0:
            print(f"处理第 {i+1}/{total_records} 条记录...")
            
        user_query = entry.get("user_query", "")
        model_comparisons = entry.get("model_comparisons", {})
        reference_model = entry.get("reference_model", "")
        
        # 检查哪些小模型可以正确完成任务
        correct_small_models = []
        
        for model_name, comparison in model_comparisons.items():
            if model_name != reference_model and comparison.get("is_correct", False):
                # 根据模型名称判断是否为小模型
                is_small_model = any(small in model_name.lower() for small in ["80b", "120b", "max", "next"])
                if is_small_model:
                    correct_small_models.append({
                        "model": model_name,
                        "response": comparison.get("model_response", ""),
                        "model_type": "小尺寸模型" if "80b" in model_name or "120b" in model_name else "快速模型"
                    })
        
        if correct_small_models:
            analysis_results.append({
                "user_query": user_query,
                "reference_model": reference_model,
                "replaceable_models": correct_small_models,
                "replaceable_count": len(correct_small_models),
                "record_id": entry.get("original_record_id"),
                "full_entry": entry
            })
    
    return analysis_results

def create_final_csv(analysis_results):
    """创建最终的CSV分析表格"""
    if not analysis_results:
        print("没有找到可替换的机会")
        return None
    
    # 创建CSV文件
    csv_file = f"final_replacement_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    with open(csv_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = [
            '记录ID', '用户查询', '参考模型', '可替换模型', '模型类型', 
            '替换次数', '替换比例', '用户查询长度'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        
        # 统计信息
        model_stats = {}
        type_stats = {}
        
        for result in analysis_results:
            for model_info in result["replaceable_models"]:
                writer.writerow({
                    "记录ID": result["record_id"],
                    "用户查询": result["user_query"][:200] + "..." if len(result["user_query"]) > 200 else result["user_query"],
                    "参考模型": result["reference_model"],
                    "可替换模型": model_info["model"],
                    "模型类型": model_info["model_type"],
                    "替换次数": len(result["replaceable_models"]),
                    "替换比例": f"{len(result['replaceable_models'])}/3",
                    "用户查询长度": len(result["user_query"])
                })
                
                # 统计
                model_stats[model_info["model"]] = model_stats.get(model_info["model"], 0) + 1
                type_stats[model_info["model_type"]] = type_stats.get(model_info["model_type"], 0) + 1
    
    return csv_file, model_stats, type_stats, len(analysis_results)

async def main():
    """主函数"""
    try:
