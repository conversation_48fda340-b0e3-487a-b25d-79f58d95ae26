#!/usr/bin/env python3
"""
最终版本：遍历所有supabase数据并分析小模型替换机会
"""

import asyncio
import json
import csv
from datetime import datetime
import sys
import os
import pathlib

# 添加路径
current_dir = pathlib.Path(__file__).parent
sys.path.append(str(current_dir))

from src.fc_module.offline_procedure.llm_router_evaluator import LLMRouterEvaluator

async def main():
    """主函数：遍历所有数据并分析"""
    print("开始遍历所有supabase中tools不等于[]的数据...")
    
    # 创建评估器实例
    evaluator = LLMRouterEvaluator()
    await evaluator.initialize()
    
    # 获取所有数据
    try:
        response = (
            evaluator.supabase_service.sync_client.table("function_calling_llm_input_param")
            .select("*")
            .neq("tools", "[]")
            .execute()
        )
        records = response.data
        print(f"获取到 {len(records)} 条记录")
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return
    
    # 处理所有记录
    dataset_entries = []
    analysis_results = []
    
    for i, record in enumerate(records):
        if i % 10 == 0:
            print(f"处理第 {i+1}/{len(records)} 条记录...")
        
        try:
            entry = await evaluator.process_single_record(record)
            if entry:
                dataset_entries.append(entry)
                
                # 分析替换机会
                model_comparisons = entry.get("model_comparisons", {})
                reference_model = entry.get("reference_model", "")
                
                correct_small_models = []
                for model_name, comparison in model_comparisons.items():
                    if model_name != reference_model and comparison.get("is_correct", False):
                        is_small = any(s in model_name.lower() for s in ["80b", "120b", "max", "next"])
                        if is_small:
                            correct_small_models.append(model_name)
                
                if correct_small_models:
                    analysis_results.append({
                        "record_id": record.get("id"),
                        "user_query": entry.get("user_query", ""),
                        "replaceable_models": correct_small_models,
                        "replaceable_count": len(correct_small_models)
                    })
        
        except Exception as e:
            print(f"处理记录 {record.get('id')} 时出错: {e}")
            continue
    
    # 保存结果
    if dataset_entries:
        output_file = f"final_evaluation_all_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset_entries, f, ensure_ascii=False, indent=2)
        
        # 创建CSV分析
        if analysis_results:
            csv_file = f"final_replacement_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=['记录ID', '用户查询', '可替换模型', '替换次数'])
                writer.writeheader()
                
                model_stats = {}
                for result in analysis_results:
                    for model in result["replaceable_models"]:
                        writer.writerow({
                            "记录ID": result["record_id"],
                            "用户查询": result["user_query"][:100] + "..." if len(result["user_query"]) > 100 else result["user_query"],
                            "可替换模型": model,
                            "替换次数": result["replaceable_count"]
                        })
                        model_stats[model] = model_stats.get(model, 0) + 1
        
        # 输出统计
        print("\n" + "="*80)
        print("评估完成！")
        print("="*80)
        print(f"总记录数: {len(records)}")
        print(f"成功处理: {len(dataset_entries)}")
        print(f"可替换场景: {len(analysis_results)}")
        print(f"替换率: {len(analysis_results)/len(records)*100:.2f}%")
        
        if model_stats:
            print("\n小模型替换统计:")
            for model, count in sorted(model_stats.items(), key=lambda x: x[1], reverse=True):
                print(f"  {model}: {count} 次")
        
        print(f"\nJSON文件: {output_file}")
        if analysis_results:
            print(f"CSV文件: {csv_file}")
    else:
        print("没有成功处理的记录")

if __name__ == "__main__":
    asyncio.run(main())
