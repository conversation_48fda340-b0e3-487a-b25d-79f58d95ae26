#!/usr/bin/env python3
"""
运行完整的LLM路由评估并分析结果
"""

import asyncio
import json
import pandas as pd
from datetime import datetime
import sys
import os
import pathlib

# 添加路径
current_dir = pathlib.Path(__file__).parent
sys.path.append(str(current_dir))

from src.fc_module.offline_procedure.llm_router_evaluator import LLMRouterEvaluator

async def run_full_evaluation():
    """运行完整的评估流程"""
    print("开始运行完整的LLM路由评估...")
    
    # 创建评估器实例
    evaluator = LLMRouterEvaluator()
    
    # 运行评估（处理最近30天的所有数据）
    dataset_entries = await evaluator.run_evaluation(days_back=30, max_records=None)
    
    print(f"成功处理了 {len(dataset_entries)} 条记录")
    
    # 保存完整数据集
    output_file = f"llm_router_evaluation_full_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    evaluator.save_dataset(dataset_entries, output_file)
    
    return dataset_entries, output_file

def analyze_replacement_opportunities(dataset_entries):
    """分析可以替换235b的小模型机会"""
    print("分析可替换235b的小模型机会...")
    
    analysis_results = []
    
    for entry in dataset_entries:
        user_query = entry.get("user_query", "")
        model_comparisons = entry.get("model_comparisons", {})
        reference_model = entry.get("reference_model", "")
        
        # 检查哪些小模型可以正确完成任务
        correct_small_models = []
        
        for model_name, comparison in model_comparisons.items():
            if model_name != reference_model and comparison.get("is_correct", False):
                # 根据模型名称判断是否为小模型
                is_small_model = any(small in model_name.lower() for small in ["80b", "120b", "max", "next"])
                if is_small_model:
                    correct_small_models.append({
                        "model": model_name,
                        "response": comparison.get("model_response", "")
                    })
        
        if correct_small_models:
            analysis_results.append({
                "user_query": user_query,
                "reference_model": reference_model,
                "replaceable_models": [m["model"] for m in correct_small_models],
                "replaceable_count": len(correct_small_models),
                "full_entry": entry
            })
    
    return analysis_results

def create_replacement_table(analysis_results):
    """创建替换机会表格"""
    if not analysis_results:
        print("没有找到可替换的机会")
        return None
    
    # 创建DataFrame
    df_data = []
    for result in analysis_results:
        for model in result["replaceable_models"]:
            df_data.append({
                "用户查询": result["user_query"][:100] + "..." if len(result["user_query"]) > 100 else result["user_query"],
                "参考模型": result["reference_model"],
                "可替换模型": model,
                "替换类型": "小尺寸模型" if "80b" in model or "120b" in model else "快速模型"
            })
    
    df = pd.DataFrame(df_data)
    
    # 保存为CSV
    csv_file = f"model_replacement_opportunities_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    # 统计信息
    stats = {
        "总可替换场景数": len(analysis_results),
        "不同小模型的替换次数": df["可替换模型"].value_counts().to_dict(),
        "替换类型分布": df["替换类型"].value_counts().to_dict()
    }
    
    return df, csv_file, stats

async def main():
    """主函数"""
    try:
        # 运行完整评估
        dataset_entries, json_file = await run_full_evaluation()
        
        if not dataset_entries:
            print("没有获取到任何评估数据")
            return
        
        # 分析替换机会
        analysis_results = analyze_replacement_opportunities(dataset_entries)
        
        # 创建表格
        result = create_replacement_table(analysis_results)
        
        if result:
            df, csv_file, stats = result
            
            print("\n" + "="*60)
            print("评估完成！")
            print("="*60)
            print(f"JSON数据文件: {json_file}")
            print(f"CSV表格文件: {csv_file}")
            print("\n统计信息:")
            print(f"- 总可替换场景数: {stats['总可替换场景数']}")
            print("\n小模型替换次数:")
            for model, count in stats["不同小模型的替换次数"].items():
                print(f"  {model}: {count} 次")
            
            print("\n替换类型分布:")
            for replace_type, count in stats["替换类型分布"].items():
                print(f"  {replace_type}: {count} 次")
        
        else:
            print("没有找到可以替换235b的小模型机会")
    
    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
