#!/usr/bin/env python3
"""
简化版本的LLM路由评估，不依赖pandas
"""

import asyncio
import json
import sys
import os
import pathlib
from datetime import datetime

# 添加路径
current_dir = pathlib.Path(__file__).parent
sys.path.append(str(current_dir))

from src.fc_module.offline_procedure.llm_router_evaluator import LLMRouterEvaluator

async def run_simple_evaluation():
    """运行简化的评估流程"""
    print("开始运行简化的LLM路由评估...")
    
    # 创建评估器实例
    evaluator = LLMRouterEvaluator()
    
    # 运行评估（处理最近7天的数据，限制数量避免超时）
    dataset_entries = await evaluator.run_evaluation(days_back=7, max_records=10)
    
    print(f"成功处理了 {len(dataset_entries)} 条记录")
    
    # 分析可替换235b的小模型机会
    analysis_results = analyze_replacement_opportunities_simple(dataset_entries)
    
    # 创建简化报告
    create_simple_report(analysis_results, dataset_entries)
    
    return dataset_entries, analysis_results

def analyze_replacement_opportunities_simple(dataset_entries):
    """简化版本的分析可替换235b的小模型机会"""
    print("分析可替换235b的小模型机会...")
    
    analysis_results = []
    
    for entry in dataset_entries:
        user_query = entry.get("user_query", "")
        model_comparisons = entry.get("model_comparisons", {})
        reference_model = entry.get("reference_model", "")
        
        # 检查哪些小模型可以正确完成任务
        correct_small_models = []
        
        for model_name, comparison in model_comparisons.items():
            if model_name != reference_model and comparison.get("is_correct", False):
                # 根据模型名称判断是否为小模型
                is_small_model = any(small in model_name.lower() for small in ["80b", "120b", "max", "next"])
                if is_small_model:
                    correct_small_models.append(model_name)
        
        if correct_small_models:
            analysis_results.append({
                "user_query": user_query[:100] + "..." if len(user_query) > 100 else user_query,
                "reference_model": reference_model,
                "replaceable_models": correct_small_models,
                "replaceable_count": len(correct_small_models)
            })
    
    return analysis_results

def create_simple_report(analysis_results, dataset_entries):
    """创建简化报告"""
    print("\n" + "="*60)
    print("评估完成！")
    print("="*60)
    
    if not analysis_results:
        print("没有找到可以替换235b的小模型机会")
        return
    
    print(f"总可替换场景数: {len(analysis_results)}")
    
    # 统计不同小模型的替换次数
    model_counts = {}
    for result in analysis_results:
        for model in result["replaceable_models"]:
            model_counts[model] = model_counts.get(model, 0) + 1
    
    print("\n小模型替换次数:")
    for model, count in sorted(model_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {model}: {count} 次")
    
    # 保存结果到JSON文件
    output_file = f"simple_evaluation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            "total_records": len(dataset_entries),
            "replacement_opportunities": analysis_results,
            "model_statistics": model_counts
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到: {output_file}")

async def main():
    """主函数"""
    try:
        await run_simple_evaluation()
    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
