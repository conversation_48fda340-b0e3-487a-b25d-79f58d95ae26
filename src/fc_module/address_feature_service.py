import json
import requests
import os
from typing import Dict, List, Optional
import structlog

logger = structlog.get_logger(__name__)

# 默认的API基础URL
DEFAULT_BASE_URL = "http://feature-store-service-svc/feature_repo/api/v1/features/batch"

def get_feature_service_url() -> str:
    """
    获取特征服务的基础URL
    
    如果 local_config.yaml 存在且包含 local_home_url，则使用该URL
    否则使用默认的 http://feature-store-service-svc
    
    Returns:
        str: 特征服务的基础URL
    """
    config_path = os.path.join(os.path.dirname(__file__), "local_config.yaml")
    print("config_path:",config_path)
    if os.path.exists(config_path):
        return "http://verify-ai-api-internal.guangfan-ai.online/feature_repo/api/v1/features/batch"
        # try:
        #     with open(config_path, 'r', encoding='utf-8') as f:
        #         content = f.read().strip()
        #         # 解析简单的 key=value 格式
        #         if '=' in content:
        #             key, value = content.split('=', 1)
        #             if key.strip() == 'local_home_url':
        #                 return value.strip().strip('"\'')
        # except Exception as e:
        #     logger.warning(f"Failed to read local_config.yaml: {e}")
    
    # 如果文件不存在或解析失败，使用默认URL
    return DEFAULT_BASE_URL

def get_user_address_features(user_id: str) -> Dict:
    """
    获取用户的地址特征信息
    
    Args:
        user_id (str): 用户ID
        
    Returns:
        Dict: 包含家和公司地址信息的字典
    """
    api_url = get_feature_service_url()
    print("api_url:",api_url)
    payload = {
        "passenger_ids": [str(user_id)]
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    response1 = requests.post(api_url, json=payload, headers=headers, timeout=100)
    print(response1)
    try:
        response = requests.post(api_url, json=payload, headers=headers, timeout=100)
        print("response",response)

        response.raise_for_status()
        
        result = response.json()
        
        if result.get("success") and result.get("data"):
            user_data = result["data"][0]
            return parse_address_features(user_data)
        else:
            logger.warning(f"Feature service returned unsuccessful response: {result}")
            return get_unknown_address_features()
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed tocall feature service: {e}")
        return get_unknown_address_features()
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse feature service response: {e}")
        return get_unknown_address_features()

def parse_address_features(user_data: Dict) -> Dict:
    """
    解析地址特征数据
    
    Args:
        user_data (Dict): 原始的用户特征数据
        
    Returns:
        Dict: 解析后的地址信息
    """
    # 直接从API响应中获取地址信息
    home_address = user_data.get("home_address")
    work_address = user_data.get("work_address")
    home_city = user_data.get("home_city", "")
    
    # 处理地址信息缺失的情况
    if not home_address:
        home_address = "没有填写家的地址"
        # extract_home_address_from_city(home_city)
    
    if not work_address:
        work_address = "没有填写公司的地址"
    
    return {
        "home_address": home_address,
        "work_address": work_address,
        "home_city": home_city,
        "common_destinations": []
    }

def extract_home_address_from_city(home_city: str) -> str:
    """
    根据城市代码生成家庭地址描述
    
    Args:
        home_city (str): 家庭城市代码
        
    Returns:
        str: 家庭地址描述
    """
    if home_city:
        city_name = get_city_name(home_city)
        return f"{city_name}的居住地"
    
    return "未知居住区域"

def extract_home_address(destinations: List[str], home_city: str) -> str:
    """
    从常用目的地中提取家庭地址
    
    Args:
        destinations (List[str]): 常用目的地列表
        home_city (str): 家庭城市代码
        
    Returns:
        str: 家庭地址描述
    """
    # 这里可以根据业务逻辑实现更复杂的家庭地址识别
    # 例如：寻找包含"家"、"小区"、"住宅"等关键词的地址
    
    home_keywords = ["小区", "家园", "公寓", "住宅", "别墅", "苑", "里", "胡同"]
    
    for dest in destinations:
        if any(keyword in dest for keyword in home_keywords):
            return dest
    
    # 如果没有明确的家庭地址，使用城市信息
    if home_city:
        city_name = get_city_name(home_city)
        return f"{city_name}的居住地"
    
    return "未知居住区域"

def extract_work_address(destinations: List[str]) -> str:
    """
    从常用目的地中提取工作地址
    
    Args:
        destinations (List[str]): 常用目的地列表
        
    Returns:
        str: 工作地址描述
    """
    # 这里可以根据业务逻辑实现更复杂的工作地址识别
    # 例如：寻找包含"大厦"、"办公楼"、"科技园"、"园区"等关键词的地址
    
    work_keywords = ["大厦", "办公楼", "写字楼", "科技园", "园区", "中心", "广场", "公司"]
    
    for dest in destinations:
        if any(keyword in dest for keyword in work_keywords):
            return dest
    
    return "未知工作区域"

def get_city_name(city_code: str) -> str:
    """
    根据城市代码获取城市名称
    
    Args:
        city_code (str): 城市代码
        
    Returns:
        str: 城市名称
    """
    # 这里可以维护一个城市代码到名称的映射
    city_mapping = {
        "44": "北京",
        "31": "上海",
        "32": "天津",
        "33": "重庆",
        "34": "河北",
        "35": "山西",
        "36": "内蒙古",
        "37": "辽宁",
        "38": "吉林",
        "39": "黑龙江",
        "41": "江苏",
        "42": "浙江",
        "43": "安徽",
        "45": "福建",
        "46": "江西",
        "47": "山东",
        "48": "河南",
        "49": "湖北",
        "50": "湖南",
        "51": "广东",
        "52": "广西",
        "53": "海南",
        "54": "四川",
        "55": "贵州",
        "56": "云南",
        "57": "西藏",
        "58": "陕西",
        "59": "甘肃",
        "60": "青海",
        "61": "宁夏",
        "62": "新疆"
    }
    
    return city_mapping.get(city_code, f"城市{city_code}")

def get_unknown_address_features() -> Dict:
    """
    获取未知地址的特征信息
    
    Returns:
        Dict: 未知地址的默认信息
    """
    return {
        "home_address": "未知居住区域",
        "work_address": "未知工作区域",
        "home_city": "",
        "common_destinations": []
    }

# def mock_feature_service_response() -> Dict:
#     """
#     模拟特征服务的响应数据，用于测试
#     """
#     return {
#         "success": True,
#         "message": "特征查询成功",
#         "data": [{
#             "is_holiday": None,
#             "preferred_car_type": None,
#             "passenger_id": 1957790231121887232,
#             "last_airport_trip_destination": None,
#             "current_location_scene": None,
#             "current_time_period": None,
#             "work_location": "116.307189,40.040533",
#             "current_location_area": None,
#             "current_day_type": None,
#             "prediction_timestamp": None,
#             "home_city": None,
#             "predicted_destination": None,
#             "home_address": "北京市昌平区天通苑东苑一区",
#             "work_address": "北京市海淀区方正大厦(东南门)",
#             "last_updated_timestamp": [2025, 8, 21, 21, 15, 44],
#             "home_location": "116.432548,40.065876",
#             "common_destinations": None
#         }],
#         "total_count": 1
#     }

# def test_address_extraction():
#     """
#     测试地址提取功能
#     """
#     # 模拟API响应
#     mock_response = mock_feature_service_response()
#     user_data = mock_response["data"][0]
    
#     # 测试地址解析
#     address_info = parse_address_features(user_data)
    
#     print("测试地址提取功能:")
#     print(f"原始数据: {user_data}")
#     print(f"\n解析结果:")
#     print(f"家庭地址: {address_info['home_address']}")
#     print(f"工作地址: {address_info['work_address']}")
#     print(f"家庭城市: {address_info['home_city']}")
#     print(f"常用目的地: {address_info['common_destinations']}")

# 示例使用
if __name__ == "__main__":
    # 测试地址提取逻辑
    # test_address_extraction()
    aa=get_user_address_features("1957790231121887232")
    print(aa)
    # print("\n" + "="*50)
    
    # # 测试获取服务URL
    # service_url = get_feature_service_url()
    # print(f"使用的服务URL: {service_url}")
    
    # print("\n" + "="*50)
    
    # # 测试未知地址情况
    # unknown_info = get_unknown_address_features()
    # print("未知地址测试:")
    # print(f"家庭地址: {unknown_info['home_address']}")
    # print(f"工作地址: {unknown_info['work_address']}")
