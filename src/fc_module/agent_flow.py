import json
import structlog
import uuid
from datetime import datetime
from typing import List
from urllib.parse import urljoin

import requests

from pydantic import BaseModel
from redis import Redis


from src.llm_api import llm_wrap
from src.fc_module.prompts import BASE_TOOL_CALL_PROMPT
from src.fc_module.tool_call_transformer import transform_tool_calls
from src.fc_module.result_formatter import format_execution_result
from src import settings
from src.fc_module.context_info_template import template_input
from src.fc_module.address_feature_service import get_user_address_features
from src.fc_module.context_merger_simple import merge_lists_with_dict_elements_simple
from src.fc_module.iot_tools import iot_command_capture_pic
from src.fc_module.process_images import process_images_with_llm
from src.fc_module.transform_context import transform_tool_history_to_multimodal

logger = structlog.get_logger(__name__)

class AgentFlowResult(BaseModel):
    status : str = "ok"
    intent_status: str = "resolved"
    content: str = ""


class AgentFlow:
    def __init__(self, cache: Redis):
        self.cache = cache
        # 移除 self.messages，改为在方法内使用局部变量

        # self.param_model = "doubao-seed-1.6-flash-250615"
        self.param_model = "qwen-flash-2025-07-28"
        self.reply_model = "qwen-max"
        # self.reply_model = "doubao-seed-1.6-flash-250615"

    def set_session_hist(self, user_id: str, conversation_id: str, intent: str, messages: List):
        user_session_key = f"flow:session:{user_id}:{conversation_id}:{intent}"

        # msg_json = self.cache.get(user_session_key)
        # messages_prev = json.loads(msg_json) if msg_json else []

        # 1 hour to expire
        self.cache.set(user_session_key, json.dumps(messages), ex=60 * 60)

    def set_session_hist_mm(self, user_id: str, conversation_id: str, intent: str, messages: List,direct:bool=False):
        user_session_key = f"flow:session:mm:{user_id}:{conversation_id}:{intent}"
        if not direct:
            # print("messages:",messages)
            new_messages = transform_tool_history_to_multimodal(messages)
            # print("new_messages:",new_messages)
            self.cache.set(user_session_key, json.dumps(new_messages), ex=60 * 60)
        else:
            self.cache.set(user_session_key, json.dumps(messages), ex=60 * 60)

    def user_session_hist(self, user_id: str, conversation_id: str, intent: str):
        user_session_key = f"flow:session:{user_id}:{conversation_id}:{intent}"
        msg_json = self.cache.get(user_session_key)
        return json.loads(msg_json) if msg_json else []
    
    def user_session_hist_mm(self, user_id: str, conversation_id: str, intent: str):
        user_session_key = f"flow:session:mm:{user_id}:{conversation_id}:{intent}"
        msg_json = self.cache.get(user_session_key)
        return json.loads(msg_json) if msg_json else []

    def clear_session_hist(self, user_id: str, conversation_id: str, intent: str):
        user_session_key = f"flow:session:{user_id}:{conversation_id}:{intent}"
        self.cache.delete(user_session_key)
    
    def clear_session_hist_mm(self, user_id: str, conversation_id: str, intent: str):
        user_session_key = f"flow:session:mm:{user_id}:{conversation_id}:{intent}"
        self.cache.delete(user_session_key)

    def set_parameters_completeness_last_round(self, user_id: str, conversation_id: str, intent: str, status: bool):
        completeness_k = f"parameters:completeness:{user_id}:{conversation_id}:{intent}"
        self.cache.set(completeness_k, str(status), ex=60*60)

    def get_parameters_completeness_last_round(self, user_id: str, conversation_id: str, intent: str) -> bool:
        completeness_k = f"parameters:completeness:{user_id}:{conversation_id}:{intent}"
        r = self.cache.get(completeness_k)
        return bool(r) if r else False

    def set_wf_status_last_round(self, user_id: str, conversation_id: str, intent: str, status: dict):
        wf_status_k = f"wf:status:{user_id}:{conversation_id}:{intent}"
        self.cache.set(wf_status_k, json.dumps(status), ex=60*60)

    def get_wf_status_last_round(self, user_id: str, conversation_id: str, intent: str) -> dict:
        wf_status_k = f"wf:status:{user_id}:{conversation_id}:{intent}"
        r = self.cache.get(wf_status_k)
        return json.loads(r) if r else {}

    def set_interrupt_flag(self, user_id: str, conversation_id: str, intent: str):
        self.cache.set(f"fc:interrupt:{user_id}:{conversation_id}:{intent}", "1", ex= 5*60)

    def check_interrupt_flag(self, user_id: str, conversation_id: str, intent: str) -> bool:
        return self.cache.get(f"fc:interrupt:{user_id}:{conversation_id}:{intent}") == b"1"

    def clear_interrupt_flag(self, user_id: str, conversation_id: str, intent: str):
        self.cache.delete(f"fc:interrupt:{user_id}:{conversation_id}:{intent}")

    @staticmethod
    def _city_info_from_coords(longitude: str, latitude: str) -> str:
        url = urljoin(settings.api_svc_base_url, '/ride-hailing/api/searchSpotByCoordinate?context={"app_id":"shouqi"}')
        res = requests.post(url=url, json={"longitude": longitude, "latitude": latitude})
        if res.status_code != 200:
            logger.error(f"search POI failed, code: {res.status_code} err: {res.text}")
            return ""
        logger.debug(f"search POI result: {res.text}")
        data = res.json()
        if not data["success"] or not data["data"]["list"]:
            return ""
        return data["data"]["list"][0]["name"]

    def _location_info_from_coords(self, coords: str):
        longitude, latitude = coords.split(",") if coords else ("", "")
        # city_info = self._city_info_from_coords(longitude, latitude)
        return f"- 用户当前所在位置： \n- 坐标：经度{longitude}, 纬度{latitude}"

    def _cached_loc_from_coords(self, coords: str) -> str:
        coord_key = f"coords:location:{coords}"
        # if cached_loc := self.cache.get(coord_key):
        #     return cached_loc.decode()

        loc = self._location_info_from_coords(coords)
        # if loc:
        #     self.cache.set(coord_key, loc, ex=60 * 60)
        return loc

    @staticmethod
    def _time_info():
        now = datetime.now()
        current_time = now.strftime("%H:%M")

        # 根据时间判断时段
        if 6 <= now.hour < 12:
            time_period = "上午"
        elif 12 <= now.hour < 18:
            time_period = "下午"
        elif 18 <= now.hour < 22:
            time_period = "晚上"
        else:
            time_period = "深夜"
        return f"{time_period} {current_time}"

    def _generate_base_tool_call_prompt(self, coords: str):
        env_prompt = f"""当前环境信息：
        {self._cached_loc_from_coords(coords)}
        现在是{self._time_info()}

        在处理用户请求时，要充分利用当前位置和时间信息，提供更智能的服务。
        """
        return BASE_TOOL_CALL_PROMPT + "\n\n" + env_prompt

    def call_fix_params_model(self, inp: str, tools: List, coords: str, history_str: str, messages: List):
        # system_prompt = self._generate_base_tool_call_prompt(coords)
        # system_prompt += "\n\n以下是对话历史:\n" + history_str


        # if not self.messages:
        #     self.messages = [{"role": "system", "content": system_prompt}]
        # messages.append({"role": "user", "content": inp})
        new_message = messages + [{"role": "user", "content": "调用工具解决："+inp}]
        # logger.info(f"{new_message}")
        return llm_wrap.get_responses(model=self.param_model, messages=new_message, tools=tools)


    def get_tools(self, user_id: str, conversation_id: str, intent: str) -> List:
        if intent in {"10007", "10012", "10051"}: # 打车
            from src.fc_module.fc_tools import car_tools, important_task

            car_tools = car_tools.copy()
            completeness = self.get_parameters_completeness_last_round(user_id, conversation_id, intent)
            # print("type(completeness):",type(completeness))
            # print("completeness:",completeness)
            if completeness:
                car_tools.append(important_task)
            return car_tools
        return []

    def tools_name(self, user_id: str, conversation_id: str, intent: str):
        return [t['function']['name'] for t in self.get_tools(user_id, conversation_id, intent)]

    def validata_and_exec_tools(self, transformed_tool_dict: dict, token: str):
        url = urljoin(settings.api_svc_base_url, "/agent-api/param/commit")
        headers = {"Authorization": f"{token}"}
        res = requests.post(url=url, json=transformed_tool_dict, headers=headers)
        if res.status_code != 200:
            logger.error(f"call tools failed, code: {res.status_code} err: {res.text}")
        logger.debug(f"call tools result: {res.text}")
        return res.json()

    def transform_select_one(self,user_id: str, req_id: str, conversation_id: str, intent: str, coords: str, tool: dict):
        last_round_status = self.get_wf_status_last_round(user_id, conversation_id, intent)
        print("last_round_status:",last_round_status)
        operation = tool.get("function_name", "unknown_operation")
        param_list = []
        param_name = tool.get("param_name")
        param_value = tool.get("param_value")
        option_id = int(tool.get("option_id","0"))
        status = "TO_VALIDATE"

        if last_round_status:
            for p in last_round_status['param_list']:
                if p['name'] == param_name:
                    if len(last_round_status["select_one"]) > option_id and option_id != 0:
                        param_value = last_round_status["select_one"][option_id-1]

        if param_name and param_value is not None and (isinstance(param_value, str) and param_value.strip()):
            param_entry = {
                "name": param_name,
                "value": param_value,
                "status": status
            }
            param_list.append(param_entry)
        return param_list

    def process_tools(self, user_id: str, req_id: str, conversation_id: str, intent: str, coords: str, tools: List, token: str):
        # transform and re-organize -> send it to data
        # task_id = self.get_workflow_id(user_id, intent)
        # params = self.param_status_from_workflow_id(task_id)
        wf_last_round = self.get_wf_status_last_round(user_id, conversation_id, intent)
        wf_id = wf_last_round.get("wf_instance_id")
        logger.debug(f"wf_last_round:{wf_last_round}")
        # print("self.tools_name(user_id, conversation_id, intent):",self.tools_name(user_id, conversation_id, intent))
        # tool = tools[0]
        for tool in tools:
            if tool["function"]["name"] not in self.tools_name(user_id, conversation_id, intent):
                return {
                    'role': 'tool',
                    'tool_call_id': tool["id"],
                    'name': tool['function']['name'],
                    'content': "未知的工具,不在能力范围内"
                }
            elif tool["function"]["name"] in ["memory_reminder_assistant_agent","wechat_voice_assistant"]:
                return {
                    'role': 'tool',
                    'tool_call_id': tool["id"],
                    'name': tool['function']['name'],
                    'content': "直接回复用户：自己暂时无法完成用户的任务"
                    # """需要回复用户：可能没有听清楚用户的想法或者暂时无法完成用户的任务（不用根据用户问题延展或者追问），严禁许诺用户完成，提醒，记录，代办因为你无法一直在线，你暂时也都是无法完成"""
                    # ，比如记一下、微信消息、打车等等，不能帮用户执行和查询，说自己暂时还无法完成用户这个任务
                }
            elif tool["function"]["name"] == "call_bulltin_camera_picture":
                return {
                    'role': 'tool',
                    'tool_call_id': tool["id"],
                    'name': tool['function']['name'],
                    'content': iot_command_capture_pic(req_id, user_id)["msg"]
                }

        transformed = {
            "app_id": "shouqi",
            "request_id": req_id,
            "uid": user_id,
            "intent_id": intent,
            "conversation_id": conversation_id,
            "extra_data": {"coords": coords}
        }
        transformed |= transform_tool_calls(tools, wf_id, wf_last_round)

        # send_to_data
        logger.debug(f"Trans for tool req: {transformed}")

        # # TODO: check and correct
        # if wf_id := transformed["wf_instance_id"]:
        #     last_round_status = self.get_wf_status_last_round(user_id, conversation_id, intent, wf_id)
        #     if last_round_status:
        #         status_map = {p['name']: p for p in last_round_status['param_list']}
        #         for i, v in enumerate(transformed['param_list']):
        #             if v['name'] in status_map and v['status'] == "CONFIRMED":
        #                 if status_map[v['name']]['status'] not in {"TO_CONFIRM", "CONFIRMED"}:
        #                     logger.warning(f"param_correct: {v['name']} status is {status_map[v['name']]['status']}, should be TO_CONFIRM or CONFIRMED")
        #                     transformed['param_list'][i]['status'] = status_map[v['name']]['status']

        result = self.validata_and_exec_tools(transformed, token)
        logger.debug(f"Tool result: {result}")

        # set wf status last round
        if result['status'] and result['data']:
            self.set_wf_status_last_round(user_id, conversation_id, intent, result['data'])

        completeness = ((result.get('data') or {}).get("extra_data") or {}).get("parameters_completeness")
        if completeness:
            self.set_parameters_completeness_last_round(user_id, conversation_id, intent, True)

        # transfer to LLM readable.
        return format_execution_result(tools[0]["id"], result, operation=tools[0]['function']['name'], transformed=transformed)

    def main_agent_flow(self, user_id: str, req_id: str, conversation_id: str, intent: str, inp: str, history_str: str, hist_messages:list,coords: str, token: str, images: list = None) -> AgentFlowResult:
        logger.debug(f"inp: {inp}")
        messages_mm = self.user_session_hist_mm(user_id, conversation_id, intent)
        messages_mm_str = json.dumps(messages_mm[-3:])
        logger.debug(f"messages_mm: {messages_mm}")
        # print("messages_mm:",messages_mm)
        if "image_url" in messages_mm_str or images:
            content,messages = process_images_with_llm(images,messages_mm,inp,req_id, user_id)
            self.set_session_hist_mm(user_id, conversation_id, intent, messages,direct=True)
            logger.debug(f"messages_mm: {content}")
            return AgentFlowResult(content=content)

        # get tools by intent first
        tools = self.get_tools(user_id, conversation_id, intent)
        # print("tools:",tools)
        messages = self.user_session_hist(user_id, conversation_id, intent)
        if not messages:
            system_prompt = self._generate_base_tool_call_prompt(coords)
            # try:
            #     order_history = self.get_order_history(template_input, token, user_id, req_id, conversation_id)
            #     system_prompt += "\n\n以下是对话历史相关订单:\n" + order_history

            #     context_backgroud_info = self.get_context_backgroud_info(user_id)
            #     system_prompt += "\n\n以下是用户相关信息:\n" + context_backgroud_info
            #     logger.debug(f"context_backgroud_info: {context_backgroud_info}")
            # except:
            #     logger.warning(f"failed to get order history:{template_input}")
            messages = [{"role": "system", "content": system_prompt}]

        if self.check_interrupt_flag(user_id, conversation_id, intent):
            # 检查打断信号
            logger.info(f"interrupt flag detected, return!")
            self.clear_interrupt_flag(user_id, conversation_id, intent)
            return AgentFlowResult(status="interrupted")
        messages = merge_lists_with_dict_elements_simple(messages, hist_messages)

        logger.info(f"messages：{messages}")
        # call LLM1 for tool choice and params fix && <>regenerate intent<>
        res = self.call_fix_params_model(inp=inp, tools=tools, history_str=history_str, coords=coords, messages=messages)
        messages.append({"role": "user", "content": inp})

        if not res["tools"] and res["message"]:
            logger.warning(f"LLM1 call returns direct msg: {res['message']}")
            messages.append({"role": "assistant", "content": res["message"]})
            self.set_session_hist(user_id, conversation_id, intent, messages)
            self.set_session_hist_mm(user_id, conversation_id, intent, messages)
            

            return AgentFlowResult(content=res["message"])
        if not res["tools"] and not res["message"]:
            raise Exception("llm returned empty tools and message")

        if self.check_interrupt_flag(user_id, conversation_id, intent):
            logger.info(f"interrupt flag detected, skipped ctx: {res['tools']}")
            self.clear_interrupt_flag(user_id, conversation_id, intent)
            return AgentFlowResult(status="interrupted")

        logger.debug(f"LLM1 tools: {res['tools']}")
        # print('res["tools"][0]["function"]["name"] :',res["tools"][0]["function"]["name"])
        #### 时效性工具的策略：
        if res["tools"][0]["function"]["name"] == "search_taxi_order":
            messages_modified = messages.copy()

        messages.append({"role": "assistant", "content": "", "tool_calls": res['tools']})

        # call API for param detail
        tool_result = self.process_tools(
            user_id=user_id, req_id=req_id, conversation_id=conversation_id,
            coords=coords, intent=intent, tools=res["tools"], token=token
        )
        # for tool_result in tool_results:
        logger.debug(f"Tool result: {tool_result}")
        messages.append(tool_result)
        if tool_result["content"] == "直接回复用户：自己暂时无法完成用户的任务":
            messages.append({"role": "assistant", "content":"暂时无法完成你的任务，可能没有听清楚你的想法和问题。你能再说一遍吗？"})
            self.set_session_hist(user_id, conversation_id, intent, messages)
            self.set_session_hist_mm(user_id, conversation_id, intent, messages)
            return AgentFlowResult(
                intent_status="invalid",
                content="暂时无法完成你的任务，可能没有听清楚你的想法和问题。你能再说一遍吗？"
            )
        if tool_result["content"] == "拍照指令下发失败" or tool_result["content"] == "拍照指令下发成功":
            messages.append({"role": "assistant", "content":tool_result["content"]})
            self.set_session_hist(user_id, conversation_id, intent, messages)
            self.set_session_hist_mm(user_id, conversation_id, intent, messages)
            return AgentFlowResult(
                intent_status="invalid",
                content=tool_result["content"]
            )


        if self.check_interrupt_flag(user_id, conversation_id, intent):
            logger.info(f"interrupt flag detected, skipped ctx: {tool_result}")
            self.clear_interrupt_flag(user_id, conversation_id, intent)
            return AgentFlowResult(status="interrupted")

        # LLM2 generate reply from API output
        try:
            llm2_res = llm_wrap.get_responses(model=self.reply_model, messages=messages)
        except Exception as e:
            logger.error(f"LLM2 get response failed, msg: {messages}")
            raise e

        if self.check_interrupt_flag(user_id, conversation_id, intent):
            logger.info(f"interrupt flag detected, skipped ctx: {llm2_res}")
            self.clear_interrupt_flag(user_id, conversation_id, intent)
            return AgentFlowResult(status="interrupted")

        logger.debug(f"LLM2 result message: {llm2_res.get('message')}")
        messages.append({"role": "assistant", "content": llm2_res["message"]})
        if res["tools"][0]["function"]["name"] == "search_taxi_order":
            messages_modified.append({"role": "assistant", "content": llm2_res["message"]})
            self.set_session_hist(user_id, conversation_id, intent, messages_modified)
            self.set_session_hist_mm(user_id, conversation_id, intent, messages_modified)
        else:
            self.set_session_hist(user_id, conversation_id, intent, messages)
            self.set_session_hist_mm(user_id, conversation_id, intent, messages)
        return AgentFlowResult(content=llm2_res["message"])

    def get_context_backgroud_info(self, user_id: str) -> str:
        """
        获取用户上下文背景信息，包括地址特征
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 格式化的用户背景信息
        """
        try:
            # 获取用户地址信息
            address_info = get_user_address_features(user_id)
            
            # 格式化地址信息
            context_info = f"用户地址信息:\n"
            context_info += f"- 家庭地址（用户家的所在地）: {address_info['home_address']}\n"
            context_info += f"- 工作地址（用户公司所在地）: {address_info['work_address']}\n"
            
            if address_info.get('home_city'):
                context_info += f"- 家庭所在城市: {address_info['home_city']}\n"
            
            if address_info.get('common_destinations'):
                context_info += f"- 常用目的地: {', '.join(address_info['common_destinations'])}\n"
                
            return context_info
            
        except Exception as e:
            logger.error(f"获取用户上下文背景信息失败: {e}")
            return "用户地址信息: 暂时无法获取"
    
    def get_order_history(self, template_input: dict, token: str, user_id: str, req_id: str, conversation_id: str) -> str:
        """
        获取并格式化订单历史数据
        
        Args:
            template_input: 模板输入数据
            token: 认证token
            user_id: 用户ID
            req_id: 请求ID
            conversation_id: 会话ID
            
        Returns:
            格式化后的订单历史数据
        """
        from datetime import datetime, timedelta
        
        # 获取当前时间
        now = datetime.now()
        # 计算2小时前的时间
        two_hours_ago = now - timedelta(days=10)
        
        # 更新模板输入中的字段
        updated_template_input = template_input.copy()
        updated_template_input["uid"] = user_id
        updated_template_input["request_id"] = req_id
        updated_template_input["conversation_id"] = conversation_id
        
        # 更新时间范围
        for param in updated_template_input["param_list"]:
            if param["name"] == "from":
                param["value"] = two_hours_ago.strftime("%Y-%m-%d %H:%M:%S")
            elif param["name"] == "to":
                param["value"] = now.strftime("%Y-%m-%d %H:%M:%S")
        
        # 调用工具获取结果
        result = self.validata_and_exec_tools(updated_template_input, token)
        
        # 处理订单数据
        from src.fc_module.order_processor import process_order_data, format_for_llm
        processed_orders = process_order_data(result)
        
        # 格式化输出
        formatted_output = format_for_llm(processed_orders)
        
        return formatted_output


if __name__ == '__main__':
    _cache = Redis(host="127.0.0.1", port=6379, db=0)

    cid = str(uuid.uuid4())

    af = AgentFlow(_cache)
    af.clear_session_hist("1956340978696585216", cid,  "10007")

    # # test_info = {"status":true,"code":0,"message":"success","data":{"conversation_id":"898b94f5-21f4-4400-a329-fc8251f52e1e_1752409542202","request_id":"97af702d-4a73-4565-8c8e-392b14dc451c","wf_instance_id":"339","uid":"1915027853850705920","intent_id":"10007","app_id":"shouqi","operation":"call_taxi_service","param_list":[{"name":"start_place","value":"方正大厦","status":"TO_CONFIRM","select_one":["方正大厦(东南门)"],"extra":null},{"name":"end_place","value":"大兴机场航站楼","status":"TO_CONFIRM","select_one":["地铁草桥站(公交站)"],"extra":null}],"extra_data":{"coords":"116.306972,40.042679","parameters_completeness":false}},"context":null}
    token = "eyJhbGciOiJIUzM4NCJ9.eyJuaWNrTmFtZSI6IiIsInVzZXJJZCI6MTkxNTAyNzg1Mzg1MDcwNTkyMCwiZGV2aWNlSWQiOiJiZmJiZmVkZS05ZmI3LTYzMGQtYWU2Yi03ZDlkMmI3ZDYzZmYiLCJhdXRob3JpdGllcyI6W3siYXV0aG9yaXR5IjoiUk9MRV9HRUVLIn1dLCJ1c2VybmFtZSI6InVzZXJfMTAxMTNlMzVlNmI0NDI1MTk1ZGQwZGVhZDgzZjc0MzUiLCJleHAiOjE3NTU2MDA5MzF9.qcOvTL5Xjt0l1z1C5WSI2XL_gP8shc0CtQ6OKLz4fZgPxPGpUayj3nFmpGZsxkXx"

    inp = input("测试模式：")

    if inp=="0":
        hist_messages = []
        inp = "打车去北京第二十中学吧"
        while True:
            r = af.main_agent_flow("1956340978696585216", str(uuid.uuid4()), cid, "10007", inp, "",hist_messages, "116.480881,39.989410", token)
            print(r.content)
            hist_messages.append({"role": "user", "content": inp})
            hist_messages.append({"role": "assistant", "content": r.content})
            inp = input("输入对话：")
    elif inp=="1":
        hist_messages = [
            # {"role": "system", "content": "系统prompt"},
            {"role": "user", "content": "故宫附近有啥好吃的"},
            {"role": "assistant", "content": "四季民福（故宫店）"}
        ]
        inp = "打车去那里吧"
        while True:
            r = af.main_agent_flow("1956340978696585216", str(uuid.uuid4()), cid, "10007", inp, "",hist_messages, "116.480881,39.989410", token)
            print(r.content)
            hist_messages.append({"role": "user", "content": inp})
            hist_messages.append({"role": "assistant", "content": r.content})
            import random
            if random.random()<0.3:
                print("other info")
                hist_messages.append({"role": "user", "content": "特朗普和马斯克谁更不靠谱"})
                hist_messages.append({"role": "assistant", "content": "特朗普吧"})
            inp = input("输入对话：")
    else:
        hist_messages =[]
        import time
        start_time = time.time()
        r = af.main_agent_flow("1956340978696585216", str(uuid.uuid4()), cid, "10007", "看下这是什么？", "",hist_messages, "116.480881,39.989410", token)
        end_time = time.time()
        print("end_time-start_time:",end_time-start_time)
        print(r.content)
        images = [
                {
                    "image_url": "https://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg",
                    "position": "left",
                },
                {
                    "image_url": "https://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg",
                    "position": "right",
                }
            ]
        # hist_messages = []
        # inp = "使用京东，看下图片里面的东西多少钱"
        inp = "拍照拍照"
        while True:
            r = af.main_agent_flow("1956340978696585216", str(uuid.uuid4()), cid, "10007", inp, "",hist_messages, "116.480881,39.989410", token,images)
            print(r.content)
            hist_messages.append({"role": "user", "content": inp})
            hist_messages.append({"role": "assistant", "content": r.content})
            # import random
            # if random.random()<0.3:
            #     print("other info")
            #     hist_messages.append({"role": "user", "content": "特朗普和马斯克谁更不靠谱"})
            #     hist_messages.append({"role": "assistant", "content": "特朗普吧"})
            inp = input("输入对话：")
        
    # 使用新封装的函数获取订单历史
    # user_id = "1956342083107160064"
    # req_id = str(uuid.uuid4())
    # conversation_id = "d26faea7-6c7c-4c21-a7d8-b56fed460546_1754754473623"
    
    # order_history = af.get_order_history(template_input, token, user_id, req_id, conversation_id)
    # print("\n\n历史数据:")
    # print(order_history)


