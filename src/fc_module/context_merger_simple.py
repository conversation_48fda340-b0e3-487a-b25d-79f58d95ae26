
from typing import List, Dict, Any, <PERSON><PERSON>

def split_by_user_messages(messages: List[Dict[str, Any]]) -> List[Tuple[str, List[Dict[str, Any]]]]:
    """
    将消息列表按照user消息分割成多个片段
    """
    segments = []
    current_segment = []
    current_user_content = None
    
    # 处理所有消息
    for msg in messages:
        if msg.get('role') == 'user':
            # 遇到新的user消息，保存当前片段
            if current_user_content is not None and current_segment:
                segments.append((current_user_content, current_segment.copy()))
            
            # 开始新的片段
            current_user_content = msg.get('content', '')
            current_segment = [msg]
        else:
            # 继续当前片段
            current_segment.append(msg)
    
    # 添加最后一个片段
    if current_user_content is not None and current_segment:
        segments.append((current_user_content, current_segment.copy()))
    
    return segments

def merge_lists_with_dict_elements_simple(list_a, list_b):
    """
    简单实现：保持A的片段不变，按B的相对顺序插入B中独有的user片段
    """
    if not list_a:
        return list_b.copy()
    if not list_b:
        return list_a.copy()
    
    # A一定有系统prompt
    if not list_a or list_a[0].get('role') != 'system':
        raise ValueError("列表A必须包含系统prompt作为第一个元素")
    
    # 分割A和B的消息
    a_segments = split_by_user_messages(list_a)
    b_segments = split_by_user_messages(list_b)
    
    # 构建A的user content到片段的映射
    a_user_map = {}
    for user_content, segment in a_segments:
        a_user_map[user_content] = segment
    
    # 找出B中独有的片段（不在A中的user content）
    b_unique_segments = []
    for user_content, segment in b_segments:
        if user_content not in a_user_map:
            b_unique_segments.append((user_content, segment))
    
    # 如果没有B独有的片段，直接返回A
    if not b_unique_segments:
        return list_a.copy()
    
    # 构建结果列表
    result = []
    
    # 首先添加系统prompt
    system_prompts = [msg for msg in list_a if msg.get('role') == 'system']
    result.extend(system_prompts)
    
    # 按B的相对顺序处理
    for user_content, segment in b_segments:
        if user_content in a_user_map:
            # 使用A中的对应片段（保持A的内容）
            result.extend(a_user_map[user_content])
        else:
            # 添加B独有的片段（移除系统prompt，因为已经在最前面添加了）
            segment_without_system = [msg for msg in segment if msg.get('role') != 'system']
            result.extend(segment_without_system)
    
    return result

def test_simple_merge():
    """测试简单合并逻辑"""
    print("=== 测试简单合并逻辑 ===")
    
    # 测试用例
    A = [
        {'role': 'system', 'content': '系统prompt'},
        {'role': 'user', 'content': '打车回家吧'},
        {'role': 'assistant', 'content': '', 'tool_calls': [{'function': {'name': 'call_taxi', 'arguments': '{"destination": "机场"}'}}]},
        {'role': 'tool', 'name': 'call_taxi', 'content': '打车服务已调用，预计5分钟到达'},
        # {'role': 'assistant', 'content': '出发地是竹藤大厦(东北门)附近吗？目的地是橡林郡6号楼对吧？'}
    ]
    
    B = [
        
        
        {'role': 'user', 'content': '恭王府附近有啥好吃的'},
        {'role': 'assistant', 'content': '张治中路的南门涮肉'},
        {'role': 'user', 'content': '打车回家吧'},
        {'role': 'assistant', 'content': '出发地是竹藤大厦(东北门)附近吗？目的地是橡林郡6号楼对吧？'},
        {'role': 'user', 'content': '1+1 等于几？'},
        {'role': 'user', 'content': '用二进制'},
    ]
    
    print("B的顺序:")
    for i, item in enumerate(B):
        print(f"  {i}: {item['role']} - {item['content'][:20]}...")
    
    result = merge_lists_with_dict_elements_simple(A, B)
    
    print("\n合并结果:")
    for i, item in enumerate(result):
        role = item['role']
        content_preview = item['content'][:30] + '...' if item.get('content') else '[无内容]'
        if role == 'assistant' and item.get('tool_calls'):
            tool_names = [call['function']['name'] for call in item['tool_calls']]
            print(f"  {i}: {role} - tool_calls: {tool_names}")
        elif role == 'tool':
            print(f"  {i}: {role} - {item.get('name', '未知')}: {content_preview}")
        else:
            print(f"  {i}: {role} - {content_preview}")

if __name__ == "__main__":
    test_simple_merge()
