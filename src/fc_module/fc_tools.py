car_tools = [
    # 高德地图工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_geocode_address",
    #         "description": "将地点名称转换为经纬度坐标。支持地址、景点、建筑物等各种地点名称。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "address": {
    #                     "type": "string",
    #                     "description": "地点名称或地址，如'西湖'、'杭州东站'、'北京天安门'"
    #                 },
    #                 "city": {
    #                     "type": "string",
    #                     "description": "城市名称，可选，用于提高搜索精度，如'杭州'、'北京'"
    #                 }
    #             },
    #             "required": ["address"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_search_poi",
    #         "description": "搜索POI（兴趣点），如餐厅、酒店、景点等。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "keyword": {
    #                     "type": "string",
    #                     "description": "搜索关键词，如'星巴克'、'酒店'、'加油站'"
    #                 },
    #                 "city": {
    #                     "type": "string",
    #                     "description": "城市名称，可选，用于限定搜索范围"
    #                 },
    #                 "types": {
    #                     "type": "string",
    #                     "description": "POI类型，可选，如'餐饮服务'、'住宿服务'"
    #                 }
    #             },
    #             "required": ["keyword"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_reverse_geocode_poi",
    #         "description": "根据经纬度坐标查找附近的POI（兴趣点）。输入经纬度，输出附近的商店、餐厅、景点等。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "longitude": {
    #                     "type": "number",
    #                     "description": "经度，如116.397428"
    #                 },
    #                 "latitude": {
    #                     "type": "number",
    #                     "description": "纬度，如39.90923"
    #                 },
    #                 "radius": {
    #                     "type": "integer",
    #                     "description": "搜索半径（米），默认1000米，最大3000米"
    #                 }
    #             },
    #             "required": ["longitude", "latitude"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_recommend_similar_poi",
    #         "description": "根据POI名称推荐附近相似的POI。例如输入'北京上地地铁站附近有星巴克么？'，会推荐上地星巴克附近的咖啡店，而不是北京所有的星巴克。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "poi_name": {
    #                     "type": "string",
    #                     "description": "POI名称，可以是模糊名称，如'北京上地星巴克'、'杭州西湖银泰'，环境范围越具体越好，北京上地地铁站星巴克"
    #                 },
    #                 "city": {
    #                     "type": "string",
    #                     "description": "城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "radius": {
    #                     "type": "integer",
    #                     "description": "推荐范围半径（米），默认2000米"
    #                 }
    #             },
    #             "required": ["poi_name"]
    #         }
    #     }
    # },
    # 导航距离和时间计算工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_calculate_driving_route",
    #         "description": "计算两个经纬度坐标之间的驾车导航距离和预估时间。返回距离、时间、过路费、红绿灯数量等信息。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "origin_lng": {
    #                     "type": "number",
    #                     "description": "起点经度，如116.397428"
    #                 },
    #                 "origin_lat": {
    #                     "type": "number",
    #                     "description": "起点纬度，如39.90923"
    #                 },
    #                 "dest_lng": {
    #                     "type": "number",
    #                     "description": "终点经度，如116.465302"
    #                 },
    #                 "dest_lat": {
    #                     "type": "number",
    #                     "description": "终点纬度，如40.004717"
    #                 },
    #                 "strategy": {
    #                     "type": "integer",
    #                    "description": "路径规划策略，默认10（躲避拥堵，路程较短）。可选值：10-20为多策略，0-9为单策略"
    #                 }
    #             },
    #             "required": ["origin_lng", "origin_lat", "dest_lng", "dest_lat"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_calculate_poi_to_poi_route",
    #         "description": "计算两个POI名称之间的驾车导航距离和预估时间。先将POI名称转换为坐标，再计算路径。适用于'从北京天安门到北京西站'这类查询。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "origin_poi": {
    #                     "type": "string",
    #                     "description": "起点POI名称，如'北京天安门'、'杭州西湖'"
    #                 },
    #                 "dest_poi": {
    #                     "type": "string",
    #                     "description": "终点POI名称，如'北京西站'、'杭州东站'"
    #                 },
    #                 "origin_city": {
    #                     "type": "string",
    #                     "description": "起点城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "dest_city": {
    #                     "type": "string",
    #                     "description": "终点城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "strategy": {
    #                     "type": "integer",
    #                     "description": "路径规划策略，默认10（躲避拥堵，路程较短）"
    #                 }
    #             },
    #             "required": ["origin_poi", "dest_poi"]
    #         }
    #     }
    # },
    # 打车服务工具
    {
        "type": "function",
        "function": {
            "name": "call_taxi_service",
            "description": "调用打车服务，用户存在打车的意图（包含要去哪里），为用户安排车辆从起点到终点。比如用户询问打车、我要打车或者隐含打车的意图, 如果不存在出发地,可以使用当前所在位置进行补全；边缘情况：目前仅支持北京的同城打车，不支持跨城市打车，不支持更换车型，不支持领优惠券，不支持更换上车时间。",
            "parameters": {
                "type": "object",
                "properties": {
                    "start_place": {
                        "type": "string",
                        "description": "出发地点名称，必须提供, 如果不存在 出发地 则填 无"
                    },
                    "end_place": {
                        "type": "string",
                        "description": "目的地名称，没有时填 无，出发地和目的地一般需要不一样"
                    },
                    "car_prefer": {
                        "type": "string",
                        "description": "默认值为1（经济型）；与此同时，还存在2:舒适型；3:商务型；4:豪华型，如果需要同时叫多个车型的话，使用英文逗号分隔，格式类似1,2；从1～4 价格越来越高，舒适程度越来越好"
                    },
                    "wf_instance_id": {
                        "type": "string",
                        "description": "任务ID,当上文中的任务已完成或者被取消时，当前执行任务需要重置为空，如果存在明确相关的任务，继承上文的任务ID，只有两种可能：1）新任务初始是空字符串；2）继承上文中已经出现的wf_instance_id，严禁使用自增的方式生成新的ID"
                    },
                },
                "required": ["start_place", "end_place","wf_instance_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "cancel_taxi_order",
            "description": "用户完成打车服务后，用户取消历史的打车服务订单, 【何时不要调用】很近的上文已经询问过用户是否确认取消，并且用户当前表达明确取消意图，需要调用的是confirm_important_task",
            "parameters": {
                "type": "object",
                 "properties": {
                    "wf_instance_id": {
                        "type": "string",
                        "description": "任务ID，该参数一定不能为空，需要从上下文寻找同任务的上个任务ID或者wf_instance_id"
                    }
                },
                "required": ["wf_instance_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "call_driver_phone",
            "description": "用户完成打车服务后，并且叫到车之后，打电话给司机，进行联系",
            "parameters": {
                "type": "object",
                 "properties": {
                    "wf_instance_id": {
                        "type": "string",
                        "description": "任务ID，该参数一定不能为空，需要从上下文寻找同任务的上个任务ID或者wf_instance_id"
                    }
                },
                "required": ["wf_instance_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "search_taxi_order",
            # "description": "用户发出打车服务后，用于用户查询历史或者当前的打车服务订单的相关信息，比如车牌、颜色、电话、费用等打车订单的信息，",
            "description": """解析并回答用户对「当前或最近一次」打车/用车行程的信息查询，并将自然语言问法路由为结构化意图与槽位。仅用于已产生或正在进行的行程信息检索（含费用预估/ETA 等），不是下单或改派工具。

【何时调用】
- 用户询问与行程相关的：费用/是否贵、到达时间/是否赶得上某时、司机何时到/在哪、车牌/颜色/品牌/服务类型（快车/拼车）、载客能力（能否坐X人）、儿童座椅/后备箱信息、上/下车点（某门/南广场等）。
- 未指明行程时默认查询当前行程；若无当前行程则回退查询最近一单。

【何时不要调用】
- 发起/修改/取消订单，选择车型或价格方案（请交给下单类工具）。
- 路线规划、地图导航、公交/地铁/步行/骑行信息（请交给地图/导航工具）。
- 与车辆无关的泛问答（天气、餐饮推荐、旅游攻略等）。""",
            "parameters": {
                "type": "object",
                 "properties": {
                    "wf_instance_id": {
                        "type": "string",
                        "description": "任务ID，该参数一定不能为空，需要从上下文寻找同任务的上个任务ID或者wf_instance_id"
                    }
                },
                "required": ["wf_instance_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "flight_assistant_agent",
            "description": """用于两类场景：
                    (A) 航班/高铁动态实时查询（航班号/车次或城市/车站对 + 相对日期词，如今天/明天/后天；关心起飞/到达/延误/取消/停运/备降/登机口/检票口/站台/预计与实际时间/能否赶上等）；
                    (B) 乘机/乘车常见问题知识问答（机票/高铁的预订、退改签、延误与赔偿、选座、行李、值机、支付、机场/车站交通、特殊旅客、常旅客、环保、安全、投诉售后等政策与操作指引）。

                    触发规则（仅当满足其一即应路由）

                    出现航班/车次识别信息：如“MU5123 / CA1234 / G1234 / D1234”。

                    出现路线与时间：如“上海到广州 明早/几点能到/多久能到/能赶上11点会吗”。

                    出现状态/设施关键词：

                    航班：起飞/到达/延误/取消/改签/备降/登机/催促登机/截止登机/登机口/值机/行李/里程/常旅客/赔偿；

                    高铁：正点/晚点/停运/检票口/站台/中转/衔接/行李；

                    出现价格/时机策略类问法（属于 FAQ）：如“什么时候订最便宜/如何比较不同航司价格/节假日订票技巧/里程兑换怎么操作”等。

                    非触发（避免误路由）

                    只谈打车/酒店/签证/目的地攻略/旅游行程、具体支付代扣/下单执行、或实时行李定位（若无数据源）等，请交给其他工具或通用对话。""",
            "parameters": {
                "type": "object",
                 "properties": {
                    "question": {
                        "type": "string",
                        "description": "基于用户上下文改写的问题，如果没有依赖前文，直接输入用户的问题，再加上输出最好50字的要求"
                    }
                    ,
                    "session_id": {
                        "type": "string",
                        "description": "对话ID，默认值为空字符串"
                    }
                },
                "required": ["question"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "wechat_voice_assistant",
            "description": """你是一个专注于处理微信消息和联系人管理的语音助手。可以以下面
                     * **主动消息推送**: 这是你的一个特殊能力。当系统有新的重要消息时，你会主动语音通知用户，用户无需询问。
                    * **消息查阅**: 用户可以命令你“查未读消息”或“查已读消息”。
                    * **联系人管理**: 用户可以让你帮忙查找或管理联系人。
                    只谈打车/酒店/签证/目的地攻略/旅游行程、具体支付代扣/下单执行、或实时行李定位（若无数据源）等，请交给其他工具或通用对话。
                    但是**无法** 替用户发送任何消息；**无法** 执行任何与消息、联系人无关的功能（如播放音乐、设置提醒、查询天气等）""",
            "parameters": {
                "type": "object",
                 "properties": {
                    "question": {
                        "type": "string",
                        "description": "基于用户上下文改写的问题，如果没有依赖前文，直接输入用户的问题"
                    }
                    ,
                    "session_id": {
                        "type": "string",
                        "description": "对话ID，默认值为空字符串"
                    }
                },
                "required": ["question"]
            }
        }
    }
    ,
    {
        "type": "function",
        "function": {
            "name": "memory_reminder_assistant_agent",
            "description": """你是可以通过工具，完成确定性的用户关于日程/待办/备忘的增删改查的需求，比如帮我记一下，或者提醒用户做事、查询相关的代办日程事情时触发
""",
            "parameters": {
                "type": "object",
                 "properties": {
                    "question": {
                        "type": "string",
                        "description": "基于用户上下文改写的问题，如果没有依赖前文，直接输入用户的问题"
                    }
                    ,
                    "session_id": {
                        "type": "string",
                        "description": "对话ID，默认值为空字符串"
                    }
                },
                "required": ["question"]
            }
        }
    },


    # 上车点推荐工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_search_taxi_spots",
    #         "description": "搜索上车点推荐。根据指定位置搜索附近适合打车的上车点，如地铁站、酒店、商场等交通便利的地点。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "location": {
    #                     "type": "string",
    #                     "description": "位置名称或地址，如'北京大学'、'方正大厦'、'海淀医院'"
    #                 },
    #                 "city": {
    #                     "type": "string",
    #                     "description": "城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "radius": {
    #                     "type": "integer",
    #                     "description": "搜索半径（米），默认1000米，建议500-2000米"
    #                 }
    #             },
    #             "required": ["location"]
    #         }
    #     }
    # },
    # 打车价格估算工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_estimate_taxi_price",
    #         "description": "估算打车价格。基于起点终点距离和时间，估算不同车型的打车费用，包括起步价、里程费、时长费等。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "origin_poi": {
    #                     "type": "string",
    #                     "description": "起点POI名称，如'北京天安门'、'杭州西湖'"
    #                 },
    #                 "dest_poi": {
    #                     "type": "string",
    #                     "description": "终点POI名称，如'北京西站'、'杭州东站'"
    #                 },
    #                 "origin_city": {
    #                     "type": "string",
    #                     "description": "起点城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "dest_city": {
    #                     "type": "string",
    #                     "description": "终点城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "car_type": {
    #                     "type": "string",
    #                     "description": "车型类型，可选值：'经济型'、'舒适型'、'豪华型'，默认'经济型'"
    #                 }
    #             },
    #             "required": ["origin_poi", "dest_poi"]
    #         }
    #     }
    # },
    # 参数确认工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "confirm_parameter",
    #         "description": "确认低容错率函数的参数。用于用户确认已验证的参数值。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "function_name": {
    #                     "type": "string",
    #                     "description": "函数名称，如'call_taxi_service'"
    #                 },
    #                 "parameter_name": {
    #                     "type": "string",
    #                     "description": "参数名称，如'start_place'、'end_place'"
    #                 },
    #                 "parameter_value": {
    #                     "type": "string",
    #                     "description": "参数值，用于确认"
    #                 }
    #             },
    #             "required": ["function_name", "parameter_name", "parameter_value"]
    #         }
    #     }
    # },
    # # 参数取消工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "cancel_parameter",
    #         "description": "取消/重置低容错率函数的参数。用于用户取消或重新设置参数值。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "function_name": {
    #                     "type": "string",
    #                     "description": "函数名称，如'call_taxi_service'"
    #                 },
    #                 "parameter_name": {
    #                     "type": "string",
    #                     "description": "参数名称，如'start_place'、'end_place'"
    #                 }
    #             },
    #             "required": ["function_name", "parameter_name"]
    #         }
    #     }
    # },
    # 任务管理工具
    {
        "type": "function",
        "function": {
            "name": "confirm_parameter",
            "description": "确认或者修改、更新任务参数，将参数状态置为'已确认',用于用户确认已验证的参数值。信息未完成收集时一个或多个参数的确认",
            "parameters": {
                "type": "object",
                "properties": {
                    "wf_instance_id": {
                        "type": "string",
                        "description": "任务ID"
                    },
                    "task_function_name": {
                        "type": "string",
                        "description": "任务对应的函数名称，如'call_taxi_service'"
                    },
                    "param_name": {
                        "type": "string",
                        "description": "参数名称，如'start_place'、'end_place'"
                    },
                    "param_value": {
                        "type": "string",
                        "description": "参数值，对应start_place的出发地地址名称或者对应的目的地地址名称"
                    },
                    "status": {
                        "type": "string",
                        "description": "当用户表达确认上文中参数时，为CONFIRMED，当用户修改参数或者重新填写了一个参数为TO_VALIDATE，只能从CONFIRMED和TO_VALIDATE二选一"
                        # "只有上一轮状态为TO_CONFIRMED的变量在用户表达明确确认时可以变为CONFIRMED, 余下都是TO_VALIDATE"
                    },
                },
                "required": ["wf_instance_id","task_function_name","param_name","param_value","status"]
            }
        }
    },
	{
		"type": "function",
		"function": {
		    "name": "call_bulltin_camera_picture",
		    "description": "当用户需要使用摄像或者相机完成目标时，可以使用该函数，用于拉起摄像头。比如用户询问“这是什么”，“拍照”，“看下这是什么？”  “这个商品多少钱？” “帮我看下这个东西评价咋样” “拍照等等",
		    "parameters": {
		        "type": "object",
		            "properties": {
		            "wf_instance_id": {
		                "type": "string",
		                "description": "任务ID，默认是空字符串"
		            }
		        },
		        "required": []
		    }
		}
	},
    {
        "type": "function",
        "function": {
        "name": "user_select_one",
        "description": "当assistant给用户若干选项时，用户选择一个选项",
        "parameters": {
        "type": "object",
        "properties": {
        "function_name": {
        "type": "string",
        "description": "函数名称，如'call_taxi_service'"
        },
        "task_id": {
        "type": "string",
        "description": "任务ID"
        },
        "param_name": {
        "type": "string",
        "description": "参数名称，如'start_place'、'end_place'"
        },
        "param_value": {
        "type": "string",
        "description": "参数值，对应start_place的出发地地址名称或者对应的目的地地址名称"
        },
        "option_id": {
        "type": "string",
        "description": "选项ID，如'1'、'2'、'3'等，当用户的回答不在选项中，或者问题难以进行匹配时，输出'0'"
        },
        },
        "required": ["function_name","task_id","param_name","param_value","option_id"]
        }
        }
        },
    {
    "type": "function",
    "function": {
        "name": "after_task_complete_repeat",
        "description": "仿照历史上已经成功的订单，再来一单，比如订单被系统取消或者个人取消后，用户重新下一单类似的订单，核心点在于跟往常一样，并稍微做些调整的动作",
        "parameters": {
            "type": "object",
            "properties": {
                "function_name": {
                    "type": "string",
                    "description": "函数名称，如'call_taxi_service'"
                },
                "wf_instance_id": {
                    "type": "string",
                    "description": "历史任务ID，该参数一定不能为空，需要从上下文寻找需要重新下单1的上个任务ID或者wf_instance_id"
                },
                "need_user_confirm": {
                      "type": "String",
                      "description": "true or false(默认是true)"
                  },
                "replace_param":{
                    "description": " 默认是[],正常是需要修改的参数[\"{\"name\":\"start_place\",\"value\":\"方正大厦\"}\"]",
                    "type": "array",
                    "items": {
                    "type": "string"
                    },
                    "type": "object"   
                }
            ,
            "required": ["wf_instance_id","function_name","need_user_confirm"]
        }
    }
    }
    },
    {
    "type": "function",
    "function": {
        "name": "product_info_search_jd",
        "description": "基于用户问题，使用京东京言，进行回复，主要面向品意图的信息查询&评价查询",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "用户信息的文本提示信息"
                },
                "session_id": {
                    "type": "string",
                    "description": "上下文ID"
                }
            },
            "required": ["query"],
            "additionalProperties": False
        }}}

    # {
    #     "type": "function",
    #     "function": {
    #         "name": "cancel_parameter",
    #         "description": "取消任务参数，将参数状态置为'未填写',取消/重置低容错率函数的参数。用于用户取消或重新设置参数值。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "task_id": {
    #                     "type": "string",
    #                     "description": "任务ID"
    #                 },
    #                 "param_name": {
    #                     "type": "string",
    #                     "description": "参数名称"
    #                 },
    #                 "param_value": {
    #                     "type": "string",
    #                     "description": "参数值，具体出发地地址名称,无"
    #                 }
    #             },
    #             "required": ["task_id", "param_name"]
    #         }
    #     }
    # }
]


important_task = {
    "type": "function",
    "function": {
        "name": "confirm_important_task",
        "description": "用于用户确认可以执行低容错率函数的任务，或者当系统收集完信息用户确认执行某事，在**已完成参数收集**时使用，比如需要花钱或者较长时间的任务，订单的创建和取消；需要parameters_completeness状态为True, 当需要parameters_completeness状态不是True的时候，不能调用",
        "parameters": {
            "type": "object",
            "properties": {
                "function_name": {
                    "type": "string",
                    "description": "函数名称，如'call_taxi_service'"
                },
                "wf_instance_id": {
                    "type": "string",
                    "description": "任务ID，该参数一定不能为空，需要从上下文寻找同任务的上个任务ID或者wf_instance_id"
                },
            },
            "required": ["function_name","wf_instance_id"]
        }
    }
}


context_task_tool = {
    "type": "function",
    "function": {
        "name": "search_order",
        "description": "使用用户ID开始和结束时间确实相关的历史任务",
        "parameters": {
            "type": "object",
            "properties": {
                "user_id": {
                    "type": "string",
                    "description": "用户ID"
                },
                "from": {
                    "type": "string",
                    "description": "开始时间，格式类似：2025-08-19 00:00:00，默认取最近一个小时"
                },
                "to": {
                    "type": "string",
                    "description": "结束时间，格式类似：2025-08-20 00:00:00，默认取当前一个小时"
                }
            },
            "required": ["user_id"]
        }
    }
}
