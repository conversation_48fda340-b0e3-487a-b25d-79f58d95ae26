# -*- coding: utf-8 -*-
"""
大模型Function Calling工具定义
包含商品比价工具的function calling信息
"""

# 商品比价工具function calling定义
PRODUCT_PRICE_TOOL = {
    "type": "function",
    "function": {
        "name": "get_product_price_range",
        "description": "根据商品图片URL获取商品价格范围和详细信息。支持单张或双张图片输入，自动进行图片质量评估和交叉验证。",
        "parameters": {
            "type": "object",
            "properties": {
                "image_urls": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "商品图片URL列表，支持1-2张图片。图片应为清晰的产品照片，最好是白底或简单背景。"
                }
            },
            "required": ["image_urls"],
            "additionalProperties": False
        },
        # "returns": {
        #     "type": "object",
        #     "properties": {
        #         "success": {
        #             "type": "boolean",
        #             "description": "是否成功获取价格信息"
        #         },
        #         "price_range": {
        #             "type": "object",
        #             "description": "价格范围信息",
        #             "properties": {
        #                 "min_price": {
        #                     "type": "number",
        #                     "description": "最低价格（元）"
        #                 },
        #                 "max_price": {
        #                     "type": "number",
        #                     "description": "最高价格（元）"
        #                 },
        #                 "avg_price": {
        #                     "type": "number",
        #                     "description": "平均价格（元）"
        #                 },
        #                 "price_count": {
        #                     "type": "integer",
        #                     "description": "价格数据数量"
        #                 }
        #             }
        #         },
        #         "product_info": {
        #             "type": "object",
        #             "description": "商品详细信息",
        #             "properties": {
        #                 "items": {
        #                     "type": "array",
        #                     "description": "商品信息列表",
        #                     "items": {
        #                         "type": "object",
        #                         "properties": {
        #                             "title": {
        #                                 "type": "string",
        #                                 "description": "商品标题"
        #                             },
        #                             "brand": {
        #                                 "type": "string",
        #                                 "description": "品牌名称"
        #                             },
        #                             "category": {
        #                                 "type": "string",
        #                                 "description": "商品分类"
        #                             },
        #                             "score": {
        #                                 "type": "number",
        #                                 "description": "匹配分数（0-1）"
        #                             },
        #                             "item_id": {
        #                                 "type": "string",
        #                                 "description": "商品ID"
        #                             },
        #                             "price_info": {
        #                                 "type": "array",
        #                                 "description": "价格详细信息",
        #                                 "items": {
        #                                     "type": "object",
        #                                     "properties": {
        #                                         "shop_name": {
        #                                             "type": "string",
        #                                             "description": "店铺名称"
        #                                         },
        #                                         "item_title": {
        #                                             "type": "string",
        #                                             "description": "商品标题"
        #                                         },
        #                                         "origin_price": {
        #                                             "type": "string",
        #                                             "description": "原价"
        #                                         },
        #                                         "ds_price": {
        #                                             "type": "string",
        #                                             "description": "到手价"
        #                                         }
        #                                     }
        #                                 }
        #                             }
        #                         }
        #                     }
        #                 },
        #                 "total_items_found": {
        #                     "type": "integer",
        #                     "description": "找到的商品总数"
        #                 },
        #                 "image_quality": {
        #                     "type": "number",
        #                     "description": "图片质量评分（单张图片时）"
        #                 },
        #                 "image1_quality": {
        #                     "type": "number",
        #                     "description": "第一张图片质量评分（双张图片时）"
        #                 },
        #                 "image2_quality": {
        #                     "type": "number",
        #                     "description": "第二张图片质量评分（双张图片时）"
        #                 }
        #             }
        #         },
        #         "errors": {
        #             "type": "array",
        #             "description": "错误信息列表",
        #             "items": {
        #                 "type": "string"
        #             }
        #         },
        #         "warnings": {
        #             "type": "array",
        #             "description": "警告信息列表",
        #             "items": {
        #                 "type": "string"
        #             }
        #         }
        #     }
        # }
    }
}

# 关键词比价工具function calling定义
KEYWORD_PRICE_TOOL = {
    "type": "function",
    "function": {
        "name": "get_product_price_by_keyword",
        "description": "通过商品关键词搜索获取商品价格范围和详细信息。适用于用户直接提供商品名称或描述的情况。",
        "parameters": {
            "type": "object",
            "properties": {
                "keyword": {
                    "type": "string",
                    "description": "商品搜索关键词，如'苹果手机'、'笔记本电脑'等具体商品名称或描述"
                },
                "limit": {
                    "type": "integer",
                    "description": "最大返回结果数量，默认为5",
                    "default": 5
                }
            },
            "required": ["keyword"],
            "additionalProperties": False
        },
        # "returns": {
        #     "type": "object",
        #     "properties": {
        #         "success": {
        #             "type": "boolean",
        #             "description": "是否成功获取价格信息"
        #         },
        #         "price_range": {
        #             "type": "object",
        #             "description": "价格范围信息",
        #             "properties": {
        #                 "min_price": {
        #                     "type": "number",
        #                     "description": "最低价格（元）"
        #                 },
        #                 "max_price": {
        #                     "type": "number",
        #                     "description": "最高价格（元）"
        #                 },
        #                 "avg_price": {
        #                     "type": "number",
        #                     "description": "平均价格（元）"
        #                 },
        #                 "price_count": {
        #                     "type": "integer",
        #                     "description": "价格数据数量"
        #                 }
        #             }
        #         },
        #         "product_info": {
        #             "type": "object",
        #             "description": "商品详细信息",
        #             "properties": {
        #                 "items": {
        #                     "type": "array",
        #                     "description": "商品信息列表",
        #                     "items": {
        #                         "type": "object",
        #                         "properties": {
        #                             "title": {
        #                                 "type": "string",
        #                                 "description": "商品标题"
        #                             },
        #                             "brand": {
        #                                 "type": "string",
        #                                 "description": "品牌名称"
        #                             },
        #                             "category": {
        #                                 "type": "string",
        #                                 "description": "商品分类"
        #                             },
        #                             "score": {
        #                                 "type": "number",
        #                                 "description": "匹配分数（0-1）"
        #                             },
        #                             "item_id": {
        #                                 "type": "string",
        #                                 "description": "商品ID"
        #                             },
        #                             "shop_id": {
        #                                 "type": "string",
        #                                 "description": "店铺ID"
        #                             },
        #                             "platform": {
        #                                 "type": "string",
        #                                 "description": "电商平台"
        #                             },
        #                             "searched_keyword": {
        #                                 "type": "string",
        #                                 "description": "搜索关键词"
        #                             },
        #                             "price_info": {
        #                                 "type": "array",
        #                                 "description": "价格详细信息",
        #                                 "items": {
        #                                     "type": "object",
        #                                     "properties": {
        #                                         "shop_name": {
        #                                             "type": "string",
        #                                             "description": "店铺名称"
        #                                         },
        #                                         "item_title": {
        #                                             "type": "string",
        #                                             "description": "商品标题"
        #                                         },
        #                                         "origin_price": {
        #                                             "type": "string",
        #                                             "description": "原价"
        #                                         },
        #                                         "ds_price": {
        #                                             "type": "string",
        #                                             "description": "到手价"
        #                                         }
        #                                     }
        #                                 }
        #                             }
        #                         }
        #                     }
        #                 },
        #                 "total_items_found": {
        #                     "type": "integer",
        #                     "description": "找到的商品总数"
        #                 },
        #                 "search_keyword": {
        #                     "type": "string",
        #                     "description": "搜索关键词"
        #                 }
        #             }
        #         },
        #         "errors": {
        #             "type": "array",
        #             "description": "错误信息列表",
        #             "items": {
        #                 "type": "string"
        #             }
        #         },
        #         "warnings": {
        #             "type": "array",
        #             "description": "警告信息列表",
        #             "items": {
        #                 "type": "string"
        #             }
        #         }
        #     }
        # }
    }
}
call_bulltin_camera_picture = {
		"type": "function",
		"function": {
		    "name": "call_bulltin_camera_picture",
		    "description": "当用户需要使用摄像或者相机完成目标时，可以使用该函数，用于拉起摄像头。比如用户询问“这是什么”，“拍照”，“看下这是什么？”  “这个商品多少钱？” “帮我看下这个东西评价咋样” “拍照等等",
		    "parameters": {
		        "type": "object",
		            "properties": {
		            "wf_instance_id": {
		                "type": "string",
		                "description": "任务ID，默认是空字符串"
		            }
		        },
		        "required": []
		    }
		}
	}


jd_get_product_price_by_picture = {
		"type": "function",
		"function": {
		    "name": "jd_get_product_price_by_picture",
		    "description": "使用京东试图获取商品名和商品价格,说出，使用京东帮我看下这是什么的时候触发",
		    
            "parameters": {
            "type": "object",
            "properties": {
                "image_urls": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "商品图片URL列表，支持1-2张图片。图片应为清晰的产品照片，最好是白底或简单背景。"
                }
            },
            "required": ["image_urls"],
            "additionalProperties": False
        },
		}
	}


jd_get_product_url_by_picture = {
		"type": "function",
		"function": {
		    "name": "jd_get_product_price_by_picture",
		    "description": "使用京东试图获取商品下单链接，使用京东帮我看下下单链接的时候触发",
            "parameters": {
            "type": "object",
            "properties": {
                "image_urls": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "商品图片URL列表，支持1-2张图片。图片应为清晰的产品照片，最好是白底或简单背景。"
                }
            },
            "required": ["image_urls"],
            "additionalProperties": False
        },
		}
	}

# 所有可用工具的列表
ALL_TOOLS = [PRODUCT_PRICE_TOOL, 
             KEYWORD_PRICE_TOOL,
             call_bulltin_camera_picture,
             jd_get_product_price_by_picture,
             jd_get_product_url_by_picture]

def get_tools():
    """
    获取所有可用的function calling工具定义
    
    Returns:
        list: 工具定义列表
    """
    return ALL_TOOLS

def get_tool_by_name(tool_name):
    """
    根据工具名称获取工具定义
    
    Args:
        tool_name (str): 工具名称
        
    Returns:
        dict: 工具定义，如果未找到返回None
    """
    for tool in ALL_TOOLS:
        if tool["function"]["name"] == tool_name:
            return tool
    return None

# 工具使用示例
TOOL_USAGE_EXAMPLES = {
    "get_product_price_range": {
        "description": "获取商品价格范围",
        "example_call": {
            "name": "get_product_price_range",
            "arguments": {
                "image_urls": [
                    "https://example.com/image1.jpg",
                    "https://example.com/image2.jpg"
                ]
            }
        },
        "example_response": {
            "success": True,
            "price_range": {
                "min_price": 12.89,
                "max_price": 46.90,
                "avg_price": 27.59,
                "price_count": 13
            },
            "product_info": {
                "items": [
                    {
                        "title": "柯达（KODAK) USB3.0读卡器 多功能合一高速读卡器",
                        "brand": "Kodak/柯达",
                        "category": "读卡器",
                        "score": 0.88,
                        "item_id": "f5cf2e7783bfe626cd51e93812d64841",
                        "price_info": [
                            {
                                "shop_name": "京东",
                                "item_title": "DM多功能读卡器USB3.0",
                                "origin_price": "29.9",
                                "ds_price": "29.9"
                            }
                        ]
                    }
                ],
                "total_items_found": 3,
                "image1_quality": 0.88,
                "image2_quality": 0.75
            },
            "errors": [],
            "warnings": ["使用第一张图片的结果（质量更高）"]
        }
    },
    "get_product_price_by_keyword": {
        "description": "通过关键词获取商品价格",
        "example_call": {
            "name": "get_product_price_by_keyword",
            "arguments": {
                "keyword": "苹果手机 iPhone 15",
                "limit": 5
            }
        },
        "example_response": {
            "success": True,
            "price_range": {
                "min_price": 4999.0,
                "max_price": 8999.0,
                "avg_price": 6999.0,
                "price_count": 8
            },
            "product_info": {
                "items": [
                    {
                        "title": "Apple iPhone 15 5G手机",
                        "brand": "Apple",
                        "category": "手机",
                        "score": 1.0,
                        "item_id": "abc123def456",
                        "shop_id": "jd_shop_001",
                        "platform": "jd",
                        "searched_keyword": "苹果手机 iPhone 15",
                        "price_info": [
                            {
                                "shop_name": "京东",
                                "item_title": "Apple iPhone 15 128GB 黑色",
                                "origin_price": "5999",
                                "ds_price": "5999"
                            }
                        ]
                    }
                ],
                "total_items_found": 1,
                "search_keyword": "苹果手机 iPhone 15"
            },
            "errors": [],
            "warnings": []
        }
    }
}

if __name__ == "__main__":
    # 打印工具信息
    print("可用工具列表:")
    for tool in ALL_TOOLS:
        print(f"- {tool['function']['name']}: {tool['function']['description']}")
    
    print("\n工具定义JSON:")
    import json
    print(json.dumps(ALL_TOOLS, ensure_ascii=False, indent=2))
