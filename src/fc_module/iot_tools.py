from urllib.parse import urljoin

import requests
import structlog

from src import settings

logger = structlog.get_logger(__name__)

def iot_command_capture_pic(req_id: str, user_id: str):
    url = urljoin(settings.api_svc_base_url, f"/agent-api/iot/device/internal/command?userId={user_id}")

    body = {
        "action": "report_sensor",
        "action_app": [
            {
                "app": "shopping_helper",
                "params": f"camera=1&reqId={req_id}"
            }
        ]
    }
    res = requests.post(url, json=body)
    if res.status_code != 200:
        logger.error(f"iot_command_capture_pic failed, code: {res.status_code} err: {res.text}")
        return {"msg": "拍照指令下发失败"}
    return {"msg": "拍照指令下发成功"}
