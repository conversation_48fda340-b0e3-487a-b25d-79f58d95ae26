# 本地LLM服务配置
# 支持通过环境变量覆盖

# 服务配置
llm_service:
  # 本地服务地址
  base_url: "http://************:20259/v1"
  
  # 默认模型
  default_model: "qwen3_235b_a22b"
  
  # API密钥（本地服务通常为dummy）
  api_key: "dummy"
  
  # 超时时间（秒）
  timeout: 30.0

# 支持的模型列表
models:
  - name: "qwen3_235b_a22b"
    description: "Qwen3 235B模型，A22B配置"
    max_tokens: 8192
    temperature_range: [0.0, 2.0]
    
  - name: "qwen3_30b_a3b_fp8"
    description: "Qwen3 30B模型，A3B FP8配置"
    max_tokens: 4096
    temperature_range: [0.0, 2.0]

# 默认参数
defaults:
  temperature: 0.7
  max_tokens: 2048
  top_p: 0.9
  frequency_penalty: 0.0
  presence_penalty: 0.0
