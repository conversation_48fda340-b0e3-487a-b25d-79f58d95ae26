import json
from typing import Dict, List, Any, Optional
from src.fc_module.order_processor import process_order_data, format_for_llm

class OrderDataHandler:
    """处理订单数据的类，用于将JSON数据转换为大模型可理解的格式"""
    
    def __init__(self):
        """初始化订单数据处理器"""
        pass
    
    def handle_order_data(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理订单数据的主入口函数
        
        Args:
            json_data: 原始JSON数据
            
        Returns:
            包含处理结果的字典
        """
        try:
            # 处理订单数据
            processed_orders = process_order_data(json_data)
            
            # 格式化为大模型可理解的文本
            llm_formatted_text = format_for_llm(processed_orders)
            
            return {
                "success": True,
                "processed_orders": processed_orders,
                "llm_formatted_text": llm_formatted_text,
                "order_count": len(processed_orders)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "processed_orders": [],
                "llm_formatted_text": "",
                "order_count": 0
            }
    
    def get_order_summary(self, json_data: Dict[str, Any]) -> str:
        """
        获取订单摘要信息
        
        Args:
            json_data: 原始JSON数据
            
        Returns:
            订单摘要文本
        """
        result = self.handle_order_data(json_data)
        if result["success"]:
            return result["llm_formatted_text"]
        else:
            return f"处理订单数据时出错: {result['error']}"
    
    def get_order_by_id(self, json_data: Dict[str, Any], instance_id: str) -> Optional[Dict[str, Any]]:
        """
        根据实例ID获取特定订单
        
        Args:
            json_data: 原始JSON数据
            instance_id: 订单实例ID
            
        Returns:
            匹配的订单数据，如果未找到则返回None
        """
        result = self.handle_order_data(json_data)
        if result["success"]:
            for order in result["processed_orders"]:
                if order.get("instance_id") == instance_id:
                    return order
        return None


