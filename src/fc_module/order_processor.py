import json
from typing import Dict, List, Any
from datetime import datetime

def process_order_data(json_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    处理订单数据，将其转换为大模型可以理解的格式
    
    Args:
        json_data: 原始JSON数据
        
    Returns:
        转换后的订单列表，每个订单包含核心特征
    """
    # 提取param_list中的订单信息
    param_list = json_data.get("data", {}).get("param_list", [])
    
    processed_orders = []
    
    for item in param_list:
        if item.get("name") == "order":
            # 解析订单的value字段（这是一个JSON字符串）
            try:
                order_details = json.loads(item.get("value", "[]"))
            except json.JSONDecodeError:
                # 如果解析失败，跳过这个订单
                continue
                
            # 创建一个字典来存储处理后的订单信息
            processed_order = {}
            
            # 遍历订单详情，提取关键信息
            for detail in order_details:
                name = detail.get("name")
                value = detail.get("value")
                
                if name == "instance_id":
                    processed_order["instance_id"] = value
                elif name == "created_time":
                    processed_order["created_time"] = value
                elif name == "industry":
                    processed_order["industry"] = value
                elif name == "operation":
                    processed_order["operation"] = value
                elif name == "status":
                    processed_order["status"] = value
                elif name == "parameter":
                    # 解析parameter字段中的JSON字符串
                    try:
                        parameter_data = json.loads(value)
                        processed_order["parameter"] = parameter_data
                    except json.JSONDecodeError:
                        processed_order["parameter"] = {}
            
            # 如果成功提取了订单信息，则添加到结果列表中
            if processed_order:
                processed_orders.append(processed_order)
    
    return processed_orders

def format_for_llm(orders: List[Dict[str, Any]]) -> str:
    """
    将处理后的订单数据格式化为适合大模型理解的文本格式
    
    Args:
        orders: 处理后的订单列表
        
    Returns:
        格式化后的文本
    """
    result = "历史订单信息:\n"
    
    for i, order in enumerate(orders, 1):
        if order.get('status', 'N/A') not in [0, 1,"0","1"]:
            continue 
        result += f"{i}. 订单 #{order.get('instance_id', 'N/A')}\n"
        result += f"   创建时间: {order.get('created_time', 'N/A')}\n"
        result += f"   行业: {order.get('industry', 'N/A')}\n"
        result += f"   操作类型: {order.get('operation', 'N/A')}\n"
        result += f"   状态: {order.get('status', 'N/A') }\n订单状态说明 0=已创建 1=进行中 2=已完成 3=已取消 4=已失败 5=已过期"
        
        # 处理参数信息
        parameter = order.get('parameter', {})
        if parameter:
            result += "   参数详情:\n"
            
            # 出发地信息
            origin = parameter.get('originAddress', {})
            if origin:
                result += f"     出发地: {origin.get('name', 'N/A')} ({origin.get('label', 'N/A')})\n"
                
            # 目的地信息
            dest = parameter.get('destAddress', {})
            if dest:
                result += f"     目的地: {dest.get('name', 'N/A')} ({dest.get('label', 'N/A')})\n"
                
            # 车型信息
            car_type = parameter.get('carType', {})
            if car_type:
                result += f"     车型: {car_type.get('carTypeName', 'N/A')} ({car_type.get('label', 'N/A')})\n"
                
            # 价格信息
            price_type = parameter.get('priceType', {})
            if price_type:
                result += f"     预估价格: {price_type.get('desc', 'N/A')}\n"
        
        result += "\n"
    
    return result

# 示例用法
if __name__ == "__main__":
    # 示例JSON数据（简化版）
    sample_data = {
        "status": True,
        "code": 0,
        "message": "success",
        "data": {
            "param_list": [
                {
                    "name": "order",
                    "value": "[{\"name\":\"instance_id\",\"value\":\"511\"},{\"name\":\"created_time\",\"value\":\"8/19/25, 9:18 PM\"},{\"name\":\"industry\",\"value\":\"ride-hailing\"},{\"name\":\"operation\",\"value\":\"call_taxi_service\"},{\"name\":\"parameter\",\"value\":\"{\\\"originAddress\\\":{\\\"name\\\":\\\"北京大学(东门)\\\"},\\\"destAddress\\\":{\\\"name\\\":\\\"首都机场T3航站楼(公交站)\\\"},\\\"carType\\\":{\\\"carTypeName\\\":\\\"舒适型\\\"},\\\"priceType\\\":{\\\"desc\\\":\\\"舒适型133.97元\\\"}}\"},{\"name\":\"status\",\"value\":\"0\"}]"
                }
            ]
        }
    }
    
    # 处理数据
    processed_orders = process_order_data(sample_data)
    
    # 格式化输出
    formatted_output = format_for_llm(processed_orders)
    print(formatted_output)
