"""
参数验证和状态管理系统
实现四种参数状态：未填写、未校验、已校验待确认、已确认
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum
import json


class ParameterState(Enum):
    """参数状态枚举"""
    NOT_FILLED = "未填写"           # 参数未提供
    NOT_VALIDATED = "未校验"       # 参数已提供但未校验
    VALIDATED_PENDING = "已校验待确认"  # 参数已校验，等待用户确认
    CONFIRMED = "已确认"           # 参数已确认


class ParameterInfo:
    """参数信息类"""
    def __init__(self, name: str, value: Any = None, state: ParameterState = ParameterState.NOT_FILLED):
        self.name = name
        self.value = value
        self.state = state
        self.validation_result = None
        self.confusion_score = None
        self.validation_time = None
        self.confirmation_time = None
        self.validation_details = {}

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "name": self.name,
            "value": self.value,
            "state": self.state.value,
            "validation_result": self.validation_result,
            "confusion_score": self.confusion_score,
            "validation_time": self.validation_time,
            "confirmation_time": self.confirmation_time,
            "validation_details": self.validation_details
        }

    def from_dict(self, data: Dict):
        """从字典格式恢复"""
        self.name = data.get("name", "")
        self.value = data.get("value")
        self.state = ParameterState(data.get("state", ParameterState.NOT_FILLED.value))
        self.validation_result = data.get("validation_result")
        self.confusion_score = data.get("confusion_score")
        self.validation_time = data.get("validation_time")
        self.confirmation_time = data.get("confirmation_time")
        self.validation_details = data.get("validation_details", {})


class ParameterValidator:
    """参数验证器"""
    
    def __init__(self, amap_tools=None):
        self.amap_tools = amap_tools
        
        # 定义低容错率的函数
        self.low_fault_tolerance_functions = {
            "call_taxi_service": {
                "required_params": ["start_place", "end_place"],
                "optional_params": ["car_prefer"],
                "location_params": ["start_place", "end_place"]
            },
            "mcp_estimate_taxi_price": {
                "required_params": ["origin_poi", "dest_poi"],
                "optional_params": ["origin_city", "dest_city", "car_type"],
                "location_params": ["origin_poi", "dest_poi"]
            }
        }
        
        # 定义高容错率的函数
        self.high_fault_tolerance_functions = {
            "mcp_geocode_address": {
                "required_params": ["address"],
                "optional_params": ["city"]
            },
            "mcp_get_city_code": {
                "required_params": ["city_name"],
                "optional_params": []
            },
            "mcp_search_poi": {
                "required_params": ["keyword"],
                "optional_params": ["city", "types"]
            },
            "mcp_reverse_geocode_poi": {
                "required_params": ["longitude", "latitude"],
                "optional_params": ["radius"]
            },
            "mcp_recommend_similar_poi": {
                "required_params": ["poi_name"],
                "optional_params": ["city", "radius"]
            },
            "mcp_calculate_driving_route": {
                "required_params": ["origin_lng", "origin_lat", "dest_lng", "dest_lat"],
                "optional_params": ["strategy"]
            },
            "mcp_calculate_poi_to_poi_route": {
                "required_params": ["origin_poi", "dest_poi"],
                "optional_params": ["origin_city", "dest_city", "strategy"]
            },
            "mcp_search_taxi_spots": {
                "required_params": ["location"],
                "optional_params": ["city", "radius"]
            }
        }

    def get_function_fault_tolerance(self, function_name: str) -> str:
        """获取函数的容错率"""
        if function_name in self.low_fault_tolerance_functions:
            return "低"
        elif function_name in self.high_fault_tolerance_functions:
            return "高"
        else:
            return "未知"

    def is_low_fault_tolerance_function(self, function_name: str) -> bool:
        """判断是否为低容错率函数"""
        return function_name in self.low_fault_tolerance_functions

    def validate_parameter(self, function_name: str, param_name: str, param_value: Any) -> Dict:
        """
        验证单个参数
        
        Args:
            function_name: 函数名
            param_name: 参数名
            param_value: 参数值
            
        Returns:
            Dict: 验证结果
        """
        validation_result = {
            "is_valid": True,
            "confusion_score": 0.0,
            "validation_details": {},
            "suggestions": []
        }

        # 检查参数是否为空
        if param_value is None or str(param_value).strip() == "":
            validation_result["is_valid"] = False
            validation_result["validation_details"]["error"] = "参数值为空"
            return validation_result

        # 对地理位置相关参数进行特殊验证
        if self._is_location_parameter(function_name, param_name):
            location_validation = self._validate_location_parameter(param_value)
            validation_result.update(location_validation)

        return validation_result

    def _is_location_parameter(self, function_name: str, param_name: str) -> bool:
        """判断是否为地理位置参数"""
        function_config = self.low_fault_tolerance_functions.get(function_name, {})
        location_params = function_config.get("location_params", [])
        return param_name in location_params

    def _validate_location_parameter(self, location: str) -> Dict:
        """验证地理位置参数"""
        if not self.amap_tools:
            return {
                "is_valid": True,
                "confusion_score": 0.0,
                "validation_details": {"note": "无法验证地理位置，跳过验证"},
                "suggestions": []
            }

        try:
            # 使用POI搜索验证地理位置
            from vui_svr.fc_module.amap_mcp_tools import mcp_search_poi_with_segmentation

            search_result = mcp_search_poi_with_segmentation(location)

            if not search_result.get("status"):
                return {
                    "is_valid": False,
                    "confusion_score": 1.0,
                    "validation_details": {"error": f"地理位置搜索失败: {search_result.get('error', '未知错误')}"},
                    "suggestions": ["请检查地理位置名称是否正确"]
                }

            data = search_result.get("data", {})
            confusion_score = data.get("confusion_score", 0.0)
            pois = data.get("pois", [])

            # 根据困惑度判断验证结果
            print("confusion_score:",confusion_score)
            if confusion_score > 0.15:  # 困惑度大于0.2认为不通过
                # print("pois:",pois)
                # if location in [item["name"] for item in pois]:
                #     return {
                #         "is_valid": True,
                #         "confusion_score": 0,
                #         "validation_details": {
                #             "confusion_level": data.get("confusion_level", "低"),
                #             "found_pois": len(pois),
                #             "segments": data.get("segments", [])
                #         },
                #         "suggestions": []
                #     }

                # else:
                return {
                    "is_valid": False,
                    "confusion_score": confusion_score,
                    "validation_details": {
                        "confusion_level": data.get("confusion_level", "高"),
                        "found_pois": len(pois),
                        "segments": data.get("segments", [])
                    },
                    "suggestions": [
                        "地理位置存在歧义，请提供更具体的地址",
                        f"找到{len(pois)}个相关地点，建议明确具体位置"
                    ]
                }
            else:
                return {
                    "is_valid": True,
                    "confusion_score": confusion_score,
                    "validation_details": {
                        "confusion_level": data.get("confusion_level", "很低"),
                        "found_pois": len(pois),
                        "best_match": pois[0] if pois else None
                    },
                    "suggestions": []
                }

        except Exception as e:
            return {
                "is_valid": False,
                "confusion_score": 1.0,
                "validation_details": {"error": f"验证过程出错: {str(e)}"},
                "suggestions": ["请重新输入地理位置"]
            }


class FunctionParameterManager:
    """函数参数管理器"""
    
    def __init__(self, amap_tools=None):
        self.validator = ParameterValidator(amap_tools)
        self.function_parameters = {}  # {function_name: {param_name: ParameterInfo}}
        
    def initialize_function_parameters(self, function_name: str, provided_params: Dict) -> Dict:
        """
        初始化函数参数
        
        Args:
            function_name: 函数名
            provided_params: 用户提供的参数
            
        Returns:
            Dict: 初始化结果
        """
        if function_name not in self.function_parameters:
            self.function_parameters[function_name] = {}
            
        # 获取函数配置
        function_config = self._get_function_config(function_name)
        if not function_config:
            return {"status": False, "error": f"未知函数: {function_name}"}
            
        required_params = function_config.get("required_params", [])
        optional_params = function_config.get("optional_params", [])
        all_params = required_params + optional_params
        
        # 初始化所有参数
        for param_name in all_params:
            if param_name in provided_params:
                # 参数已提供，设置为未校验状态
                param_info = ParameterInfo(
                    name=param_name,
                    value=provided_params[param_name],
                    state=ParameterState.NOT_VALIDATED
                )
            else:
                # 参数未提供
                param_info = ParameterInfo(
                    name=param_name,
                    state=ParameterState.NOT_FILLED
                )
            
            self.function_parameters[function_name][param_name] = param_info
            
        return {
            "status": True,
            "required_params": required_params,
            "optional_params": optional_params,
            "provided_params": list(provided_params.keys()),
            "missing_required": [p for p in required_params if p not in provided_params]
        }
    
    def _get_function_config(self, function_name: str) -> Optional[Dict]:
        """获取函数配置"""
        if function_name in self.validator.low_fault_tolerance_functions:
            return self.validator.low_fault_tolerance_functions[function_name]
        elif function_name in self.validator.high_fault_tolerance_functions:
            return self.validator.high_fault_tolerance_functions[function_name]
        else:
            return None

    def validate_all_parameters(self, function_name: str) -> Dict:
        """
        验证函数的所有参数

        Args:
            function_name: 函数名

        Returns:
            Dict: 验证结果
        """
        if function_name not in self.function_parameters:
            return {"status": False, "error": "函数参数未初始化"}

        function_params = self.function_parameters[function_name]
        validation_results = {}
        all_valid = True

        for param_name, param_info in function_params.items():
            if param_info.state == ParameterState.NOT_FILLED:
                continue  # 跳过未填写的参数

            if param_info.state == ParameterState.NOT_VALIDATED:
                # 验证参数
                validation_result = self.validator.validate_parameter(
                    function_name, param_name, param_info.value
                )

                param_info.validation_result = validation_result
                param_info.confusion_score = validation_result.get("confusion_score", 0.0)
                param_info.validation_time = datetime.now().isoformat()
                param_info.validation_details = validation_result.get("validation_details", {})

                if validation_result["is_valid"]:
                    param_info.state = ParameterState.VALIDATED_PENDING
                else:
                    all_valid = False

                validation_results[param_name] = validation_result

        return {
            "status": True,
            "all_valid": all_valid,
            "validation_results": validation_results,
            "parameter_states": {name: info.state.value for name, info in function_params.items()}
        }

    def can_execute_function(self, function_name: str) -> Dict:
        """
        检查函数是否可以执行

        Args:
            function_name: 函数名

        Returns:
            Dict: 检查结果
        """
        if function_name not in self.function_parameters:
            return {"can_execute": False, "reason": "函数参数未初始化"}

        function_config = self._get_function_config(function_name)
        if not function_config:
            return {"can_execute": False, "reason": "未知函数"}

        required_params = function_config.get("required_params", [])
        function_params = self.function_parameters[function_name]

        # 检查必填参数
        missing_required = []
        unvalidated_params = []
        unconfirmed_params = []

        for param_name in required_params:
            param_info = function_params.get(param_name)
            if not param_info or param_info.state == ParameterState.NOT_FILLED:
                missing_required.append(param_name)
            elif param_info.state == ParameterState.NOT_VALIDATED:
                unvalidated_params.append(param_name)
            elif param_info.state == ParameterState.VALIDATED_PENDING:
                # 对于低容错率函数，需要用户确认
                if self.validator.is_low_fault_tolerance_function(function_name):
                    unconfirmed_params.append(param_name)

        # 检查可选参数（如果用户提供了）
        optional_params = function_config.get("optional_params", [])
        for param_name in optional_params:
            param_info = function_params.get(param_name)
            if param_info and param_info.value is not None:
                if param_info.state == ParameterState.NOT_VALIDATED:
                    unvalidated_params.append(param_name)
                elif param_info.state == ParameterState.VALIDATED_PENDING:
                    if self.validator.is_low_fault_tolerance_function(function_name):
                        unconfirmed_params.append(param_name)

        # 判断是否可以执行
        can_execute = (
            len(missing_required) == 0 and
            len(unvalidated_params) == 0 and
            len(unconfirmed_params) == 0
        )

        return {
            "can_execute": can_execute,
            "missing_required": missing_required,
            "unvalidated_params": unvalidated_params,
            "unconfirmed_params": unconfirmed_params,
            "fault_tolerance": self.validator.get_function_fault_tolerance(function_name)
        }

    def confirm_parameter(self, function_name: str, param_name: str) -> Dict:
        """
        确认参数

        Args:
            function_name: 函数名
            param_name: 参数名

        Returns:
            Dict: 确认结果
        """
        if function_name not in self.function_parameters:
            return {"status": False, "error": "函数参数未初始化"}

        param_info = self.function_parameters[function_name].get(param_name)
        if not param_info:
            return {"status": False, "error": f"参数 {param_name} 不存在"}

        if param_info.state != ParameterState.VALIDATED_PENDING:
            return {"status": False, "error": f"参数 {param_name} 状态不是待确认状态"}

        param_info.state = ParameterState.CONFIRMED
        param_info.confirmation_time = datetime.now().isoformat()

        return {
            "status": True,
            "message": f"参数 {param_name} 已确认",
            "parameter_info": param_info.to_dict()
        }

    def cancel_parameter(self, function_name: str, param_name: str) -> Dict:
        """
        取消/重置参数

        Args:
            function_name: 函数名
            param_name: 参数名

        Returns:
            Dict: 取消结果
        """
        if function_name not in self.function_parameters:
            return {"status": False, "error": "函数参数未初始化"}

        param_info = self.function_parameters[function_name].get(param_name)
        if not param_info:
            return {"status": False, "error": f"参数 {param_name} 不存在"}

        # 重置参数状态
        param_info.value = None
        param_info.state = ParameterState.NOT_FILLED
        param_info.validation_result = None
        param_info.confusion_score = None
        param_info.validation_time = None
        param_info.confirmation_time = None
        param_info.validation_details = {}

        return {
            "status": True,
            "message": f"参数 {param_name} 已重置",
            "parameter_info": param_info.to_dict()
        }

    def update_parameter(self, function_name: str, param_name: str, param_value: Any) -> Dict:
        """
        更新参数值

        Args:
            function_name: 函数名
            param_name: 参数名
            param_value: 新的参数值

        Returns:
            Dict: 更新结果
        """
        if function_name not in self.function_parameters:
            return {"status": False, "error": "函数参数未初始化"}

        param_info = self.function_parameters[function_name].get(param_name)
        if not param_info:
            return {"status": False, "error": f"参数 {param_name} 不存在"}

        # 更新参数值并重置状态
        param_info.value = param_value
        param_info.state = ParameterState.NOT_VALIDATED
        param_info.validation_result = None
        param_info.confusion_score = None
        param_info.validation_time = None
        param_info.confirmation_time = None
        param_info.validation_details = {}

        return {
            "status": True,
            "message": f"参数 {param_name} 已更新",
            "parameter_info": param_info.to_dict()
        }

    def get_function_status(self, function_name: str) -> Dict:
        """
        获取函数的完整状态

        Args:
            function_name: 函数名

        Returns:
            Dict: 函数状态
        """
        if function_name not in self.function_parameters:
            return {"status": False, "error": "函数参数未初始化"}

        function_params = self.function_parameters[function_name]
        execution_check = self.can_execute_function(function_name)

        return {
            "status": True,
            "function_name": function_name,
            "fault_tolerance": self.validator.get_function_fault_tolerance(function_name),
            "parameters": {name: info.to_dict() for name, info in function_params.items()},
            "execution_check": execution_check
        }
