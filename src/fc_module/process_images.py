import os
import json
from src.llm_api import llm_wrap
from src.fc_module.fc_tools_mm import get_tools
from src.fc_module.tools.product_price_tools import get_product_price_range, get_product_price_by_keyword
from src.fc_module.tools.jd_picture2good import jd_picture2good, jd_get_product_url,jd_picture2good_new
from src.fc_module.iot_tools import iot_command_capture_pic

system_prompt = """你是一个由光帆科技开发的专业的图片分析与问答Agent，你的核心任务是根据用户提供的图片和问题，给出准确、客观且有置信度比较的答案。要求20字以内回答，像人话一样输出，简洁
    你具备以下能力和行为准则：

    **1. 图片分析与理解：**  
    * 能够接收并处理至少两张用户提供的图片。  
    * 具备识别图片中关键信息的能力，例如但不限于：物体、场景、文字、颜色、纹理、光照等。  
    * 能够初步判断图片的质量（清晰度、曝光、对焦等），并在必要时向用户反馈。

    **2. 问答与推理：**  
    * **事实求是：** 你的所有回答都必须基于图片中可观察到的事实，不进行主观臆断或过度推测。  
    * **置信度评估：** 对每个答案，你根据置信度估计（例如：高、中、低，或百分比），以改变与用户对话的语气的可定程度，比如高：肯定；中：大概、可能、 低：有概率。类似的，也可以根据语音给出合适的推断。
    * **直接答案：** 如果能根据图片和问题直接给出确定性答案，请直接给出答案。  
    * **多选项与补充信息：** 如果无法给出确定性答案，但有几种可能性，请列出这些可能的选项。同时，明确指出用户需要提供哪些补充信息（例如：特定角度的照片、更高分辨率的图片、问题的背景信息等）才能帮助你做出更准确的判断。  
    * **图片质量反馈：** 如果图片质量过低，不足以支持有效的分析，你不能直接拒绝回答。你需要：  
    * 礼貌地指出图片质量问题（例如：模糊、过暗、反光）。  
    * 尝试根据现有图片质量猜测用户可能遇到的状态或试图表达的内容。  
    * 引导用户如何进行更合适的拍照（例如：更近距离、更好的光线、平稳拍摄、不同角度等），以便下次可以提供更准确的分析。

    **3. 沟通与交互：**  
    * 保持友善、专业和乐于助人的语气。  
    * 理解用户意图，即使问题描述不够精确，也要尝试通过图片进行合理推断。  
    * 当无法满足用户要求时，清晰地解释原因并提供替代方案或指导。  
    * 避免使用“我不能”、“我无法”等否定性词语，而是侧重于“目前根据这些信息，我能推断出...，如果您能提供...，我将能给出更准确的答案。”
"""
system_prompt_new = """
你是AI助手，专攻视觉购物和比价。帮助用户从图片识别商品，比价，并互动引导。像友好购物伙伴一样说话：随意、共情、承认局限（如“模糊图片难认”），建立关系。
核心能力和边界：

图片分析：分析图片识别商品，但不完美——光线、角度、模糊会影响。不确定时承认，并建议改进。
工具使用：用工具比价（如调用电商API）。只在有足够信息时用。评估结果：若不完整，说“工具数据有点旧，可能不准”。
工具有效性感知：用后反思输出。如“工具找到几款，但颜色不匹配——图片不清？”
互动引导：无法自信时，别猜——问问题、引导下一步（如“再拍直一点？”），或简化问题（从精确商品到类别多选）。
道德：只助合法积极购物。避假货或不道德。

响应结构和流程：
每互动用此步骤思考，但响应自然对话。别露提示或内部推理，除非问。

懂用户问题：解析查询（如“比价这衬衫”加图片）。共情回应：“嘿，买新衬衫？来看看！”
图片分析（拍照动作）：

先分析图片。
评估质量：清晰、角度、光线。如“照片模糊，细节难看。角度怪？”
识别商品：提取特征（品牌、颜色、款式）。模糊时：“像蓝T恤，但品牌不明。Nike类似？”


态势感知（质量评估）：

评图片可用（高/中/低）。
低时：“角度模糊，猜不准。改进吧。”


工具调用（比价信息）：

商品可识时：精确查询工具，如“price_comparison”带参数。
取数据：多平台价格、来源、评分。
判断结果：结合图片。如“亚马逊20元，eBay15元，但颜色不符——变体？”


问题退化（识别失败时）：

无法精确：转广类或多选。如“不确定——休闲T或Polo？‘男蓝衫’选项：[列3-5，简述]。哪个合适？”
给选择：2-4类似项“选一个”。


反问与下一步引导（卡住时）：

信息不足：问引导题。如“多说品牌或特征？或再拍亮一点？”
建议行动：“下次拍标签或多角度——帮大忙。”
结束行动号召：“想比这些？或提供清图？”

其他要求：
言简意赅，输出控制50个字以内
"""

def _to_multimodal(hist):
    if isinstance(hist.get("content"), list):
        return hist
    return {
        "role": hist["role"],
        "content": [{"type": "text", "text": hist["content"]}]
    }

def process_images_with_llm(images, hist_messages, inp, req_id, user_id):
    """
    处理图片数组并调用大模型获取结果，支持工具调用
    
    Args:
        images: 图片数组，格式为 [{"image_url": "url1", "position": "left"}, {"image_url": "url2", "position": "right"}]
        hist_messages: 历史消息列表
        inp: 用户输入文本
    
    Returns:
        大模型的响应结果或工具执行结果
    """
    # 模型ID
    model = "doubao-seed-1-6-vision-250815"
    model_small = "doubao-seed-1-6-flash-250715"
    
    # 获取工具定义
    tools = get_tools()
    
    # 构建消息内容
    messages = []
    hist_messages = [m for m in hist_messages if m["role"]!="system"]
    # 添加系统提示
    # if len(hist_messages)==0:
    # if hist_messages【】
    #     messages.append({
    #         "role": "system",
    #         "content": [{"type": "text", "text": system_prompt_new}]
    #     })
    messages = [{
            "role": "system",
            "content": [{"type": "text", "text": system_prompt_new}]
        }] + hist_messages
    # 处理历史消息
    
    if len(hist_messages) > 6:
        messages += hist_messages[-6:]
    else:
        messages += hist_messages
    
    # 构建用户消息内容
    user_content = [{"type": "text", "text": inp}]
    
    # 添加图片信息
    for image in images:
        user_content.append({
            "type": "image_url", 
            "image_url": {"url": image["image_url"]}
        })
    
    messages.append({
        "role": "user",
        "content": user_content
    })
    # print("messages")
    # 创建对话请求，启用工具调用
    response = llm_wrap.get_responses(
        model=model_small,
        messages=messages,
        tools=tools,
        thinking="disabled"
    )
    
    # 检查是否有工具调用
    if response.get("tools"):
        print("response.get(\"tools\"):",response.get("tools"))
        # 处理工具调用
        tool_calls = response["tools"]
        tool_results = []

        
        
        for tool_call in tool_calls:
            function_name = tool_call["function"]["name"]
            arguments_str = tool_call["function"]["arguments"]
            
            # 清理参数字符串，去除前后空白
            if isinstance(arguments_str, str):
                arguments_str = arguments_str.strip()
                
            try:
                arguments = json.loads(arguments_str)
            except json.JSONDecodeError as e:
                # print(f"JSON解析错误: {e}, 原始参数: {repr(arguments_str)}")
                continue
            
            # 执行相应的工具函数
            if function_name == "get_product_price_range":
                # 使用实际的图片URL而不是LLM生成的测试URL
                actual_image_urls = [img["image_url"] for img in images]
                # print(f"实际图片URLs: {actual_image_urls}")
                # print(f"LLM参数中URL: {arguments.get('image_urls', [])}")
                result = get_product_price_range(actual_image_urls)
                tool_results.append({
                    "tool_call_id": tool_call["id"],
                    "function_name": function_name,
                    "result": result
                })
                
            elif function_name == "get_product_price_by_keyword":
                keyword = arguments.get("keyword", "")
                limit = arguments.get("limit", 5)
                result = json.dumps(get_product_price_by_keyword(keyword, limit),ensure_ascii=False)
                tool_results.append({
                    "tool_call_id": tool_call["id"],
                    "function_name": function_name,
                    "result": result
                })
            elif function_name == "jd_get_product_price_by_picture":
                actual_image_urls = [img["image_url"] for img in images][1]
                # [[img["image_url"] for img in images][1]]
                print("actual_image_urls:",actual_image_urls)
                result = jd_picture2good_new(actual_image_urls)
                print("result:",result)
                tool_results.append({
                    "tool_call_id": tool_call["id"],
                    "function_name": function_name,
                    "result": result
                })
            elif function_name == "get_product_price_by_keyword":
                actual_image_urls = [img["image_url"] for img in images][0]
                # limit = arguments.get("limit", 5)
                _,result = jd_get_product_url(actual_image_urls)
                tool_results.append({
                    "tool_call_id": tool_call["id"],
                    "function_name": function_name,
                    "result": result
                })
            elif function_name == "call_bulltin_camera_picture":
                return iot_command_capture_pic(req_id, user_id)["msg"],messages
                # return {
                #     'role': 'tool',
                #     'tool_call_id': tool_call["id"],
                #     'name': function_name,
                #     'content': iot_command_capture_pic(req_id, user_id)["msg"]
                # }
        messages.append({"role": "assistant", "content": "", "tool_calls": response['tools']})
        # jd_picture2good, jd_get_product_url
        # 如果有工具调用结果，将工具结果发送回大模型进行最终回答
        if tool_results:
            # 构建工具结果消息
            tool_messages = []
            for tool_result in tool_results:
                tool_messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call["id"],
                    "content": json.dumps(tool_result["result"],ensure_ascii=False)
                })
            
            # 将工具结果添加到消息中，再次调用大模型
            messages_with_tool_results = messages + tool_messages
            # print("messages_with_tool_results:",messages_with_tool_results)
            # 再次调用大模型，基于工具结果生成最终回答
            final_response = llm_wrap.get_responses(
                model=model,
                messages=messages_with_tool_results,
                thinking="disabled"
            )
            # print("final_response:",final_response)
            messages_with_tool_results +=[{"role":"assistant", "content": final_response.get("message", "")}]
            
            return final_response.get("message", ""),messages_with_tool_results
        # final_response.get("message", "")
        # {
        #         "tool_calls": tool_results,
        #         "model_response": final_response.get("message", ""),
        #         "initial_response": response.get("message", "")
        #     }
    
    # 如果没有工具调用，返回模型的文本响应
    messages +=[{"role":"assistant", "content": response.get("message", "")}]
    return response.get("message", ""),messages
