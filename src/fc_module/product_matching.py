import json
from src.llm_api import llm_wrap

def evaluate_product_match(user_images, product_name, product_image_url, product_title):
    """
    评估用户图片与商品的匹配程度
    
    Args:
        user_images: 用户图片列表 [{"image_url": "左耳图片URL"}, {"image_url": "右耳图片URL"}]
        product_name: 用户商品名称
        product_image_url: 商品图片URL
        product_title: 商品标题
    
    Returns:
        dict: 包含匹配评估结果的字典
        {
            "has_product": bool,  # 图片中是否有商品
            "is_same_product": bool,  # 是否同一商品
            "match_score": int,  # 匹配分数1-10
            "formatted_output": str  # 格式化输出
        }
    """
    # 系统prompt专门用于商品匹配评估
    system_prompt = """你是一个专业的商品匹配评估专家。你的任务是：
    1. 判断用户图片中是否有商品（是/否）
    2. 判断用户图片/描述的商品与提供的商品是否匹配（是/否）
    3. 给出1-10分的匹配度评分（10分为完全匹配）
    
    评估标准：
    - 商品关键特征（品牌、型号、颜色等）
    - 视觉相似度
    - 描述一致性
    
    输出格式要求：
    <withGood>是/否</withGood>
    <GoodScore>1-10</GoodScore>
    """
    
    # 构建用户消息
    user_message = f"""
    请评估以下商品匹配度：
    - 用户商品名称: {product_name}
    - 商品图片URL: {product_image_url}
    - 商品标题: {product_title}
    
    用户图片:
    - 图片: {user_images[0]['image_url']}
    """
    
    # 构建消息内容
    messages = [
        {
            "role": "system",
            "content": [{"type": "text", "text": system_prompt}]
        },
        {
            "role": "user",
            "content": [{"type": "text", "text": user_message}]
        }
    ]
    
    # 添加图片信息
    for image in user_images:
        messages[1]["content"].append({
            "type": "image_url",
            "image_url": {"url": image["image_url"]}
        })
    
    # 调用大模型
    response = llm_wrap.get_responses(
        model="doubao-seed-1-6-vision-250815",
        messages=messages,
        thinking="disabled"
    )
    
    # 解析响应
    response_text = response.get("message", "")
    result = {
        "has_product": False,
        "is_same_product": False,
        "match_score": 5,  # 默认分数
        "formatted_output": response_text
    }
    
    # 从格式化输出中提取结果
    try:
        # 提取<withGood>标签内容
        with_good = response_text.split("<withGood>")[1].split("</withGood>")[0]
        result["has_product"] = with_good == "是"
        result["is_same_product"] = with_good == "是"
        
        # 提取<GoodScore>分数
        score_part = response_text.split("<GoodScore>")[1].split("</GoodScore>")[0]
        result["match_score"] = int(score_part)
    except (IndexError, ValueError):
        pass
    
    return result
