import json
import structlog
from typing import Dict, Any, List
from datetime import datetime

logger = structlog.get_logger(__name__)

def format_execution_result(
    tool_call_id: str,
    execution_result: Dict[str, Any],
    operation: str = '',
    wf_instance_id: str = '',
    transformed: Dict[str, Any]={}

) -> Dict[str, Any]:
    """
    Transforms a detailed execution result into a human-readable summary
    formatted for an OpenAI tool call response.

    Args:
        tool_call_id: The ID of the original tool call.
        execution_result: The detailed result from the function execution.
        wf_instance_id: The ID of the workflow instance.

    Returns:
        A dictionary formatted as an OpenAI tool call result.
    """
    # logger.debug(f"transformed: {transformed}")
    function_names= [item["value"] for item in transformed["param_list"] if item["name"]=="function_name"]

    status = execution_result["status"]
    summary_parts = [f"任务 {operation} 的执行情况如下："]
    if not status:
        summary_parts.append(f'\n任务执行失败，错误信息：{execution_result}')
        return {
            "role": "tool",
            "tool_call_id": tool_call_id,
            "name": operation,
            "content": " ".join(summary_parts)
        }

    execution_data = execution_result.get("data", {})
    old_operation= operation
    operation = execution_data.get("operation", "unknown_operation")
    param_list = execution_data.get("param_list", [])
    wf_instance_id = wf_instance_id or execution_data.get("wf_instance_id", "")
    if operation =="call_driver_phone":
        summary_parts.append(f'\n任务执行成功，可以回复用户：正在联系司机，稍等一下。')
        return {
            "role": "tool",
            "tool_call_id": tool_call_id,
            "name": operation,
            "content": " ".join(summary_parts)
        }


    # 1. Aggregate parameters by status
    params_by_status = {
        "TO_INPUT": [],
        "TO_VALIDATE": [],
        "TO_CONFIRM": [],
    }
    extra_data = execution_data.get("extra_data", {})
    parameters_completeness = extra_data.get("parameters_completeness", False)
    print(f"**params_by_status**:{params_by_status}")
    summary_parts.append(f"\n当前的参数收集确认完毕标识: parameters_completeness:{parameters_completeness}\n")

    if "error" in extra_data:
        summary_parts.append(f"任务 {operation} 存在错误，，错误信息如下:{extra_data["error"]}。\n")
    else:
        if  parameters_completeness or old_operation=="confirm_important_task":
            summary_parts.append(f"任务 {operation} 已完成参数收集，可以跟用户确认完成任务信息。\n")
        else:
            summary_parts.append(f"任务 {operation} 还有需要用户确认或者填写的信息。\n")

    for param_dict in param_list:
        status = param_dict.get("status")
        if status in params_by_status:
            params_by_status[status].append(param_dict)

    for k, params in params_by_status.items():
        if not params:
            continue
        if k == "TO_VALIDATE":
            summary_parts.append(f"\n待细化变量为: {','.join(p['name'] for p in params)}\n")
        if k == "TO_INPUT":
            summary_parts.append(f"\n待输入变量为: {','.join(p['name'] for p in params)}\n")
        if k == "TO_COMPLETE":
            summary_parts.append(f"\n待确认变量为: {','.join(p['name'] for p in params)}\n")

    # 2. Generate a human-readable text summary
    # Handle parameters needing to be filled
    if params_by_status["TO_INPUT"]:
        summary_parts.append("\n请填写以下缺失的信息（TO_INPUT）：")
        for p in params_by_status["TO_INPUT"]:
            reason = (p.get('extra') or {}).get('reason')
            if reason:
                if reason == "SEARCH_RESULT_LIST_EMPTY":
                    param_value = [item['value'] for item in transformed["param_list"] if item['name']==p['name']]
                    summary_parts.append(f"- {p['name']} 输入参数'{param_value}'不支持或者查不到这个参数值，需要用户重新填写有效或者支持的参数值")
                else:
                    summary_parts.append(f"- {p['name']}的缺失的信息可能因为工具调用错误，以下补充说明: {reason}")
            else:
                summary_parts.append(f"- {p['name']}: 需要用户提供具体内容。如果上文已经有相关参数，代表上文的相关参数无效，需要进行更改参数，或者使用其他工具")

    # Handle parameters needing validation
    if params_by_status["TO_VALIDATE"]:
        summary_parts.append("\n其中存在需要用户校验选择以下信息（TO_VALIDATE）：")
        for p in params_by_status["TO_VALIDATE"]:
            param_name = p['name']
            param_value = p.get('value', '未提供')
            summary_parts.append(f"- 参数 '{param_name}' (用户提供的值: '{param_value}') 需要验证。")
            
            suggestions = p.get('select_one')
            if suggestions:
                suggestion_str = ", ".join([f"'{s}'" for s in suggestions])
                summary_parts.append(f"\n存在多个相似的候选： {suggestion_str}，需要用户确认其中的哪一个？")
            else:
                # FIXME : This is a placeholder, you should replace it with more elastic and user-friendly suggestions.
                summary_parts.append(f"参数 '{param_name}的查询接口可能有点问题，可以稍后再确认\n")
            if p.get('extra'):
                extra_info = p.get('extra').get('reason')
                if extra_info:
                    summary_parts.append(f"  补充说明: {extra_info}")

    # Handle parameters needing confirmation
    if params_by_status["TO_CONFIRM"]:
        summary_parts.append("\n,请**确认**以下信息（**TO_CONFIRM**）：")
        for p in params_by_status["TO_CONFIRM"]:
            param_name = p['name']
            if "select_one" in p:
                param_value = p.get('select_one', '未提供')
                # if param_name == 'price':
                #     param_value = [x.replace("舒适型", "") for x in param_value]
                user_input_value = p.get('value', '未提供')
                if user_input_value != param_value:
                    summary_parts.append(f"- {param_name}: 用户输入的信息：{user_input_value} 它对应的\n 有效的参数是{param_value}，需要用户确认有效参数")              
                else:
                    summary_parts.append(f"- {param_name}: {param_value}")
            else:
                param_value = p.get('value', '未提供')
                if param_name == 'price':
                    param_value = param_value.replace("舒适型", "")
                summary_parts.append(f"- {param_name}: {param_value}")

    if wf_instance_id:
        summary_parts.append(f"\n当前任务id: {wf_instance_id}")

    if param_list:
        param_list_remove_null = remove_null_values(param_list)
        param_str = format_dict_list_to_text(param_list_remove_null)
        ### 如果存在 描述和参数列表 就不用values
        print("param_list_remove_null:",param_str)
        summary_parts.append(f"\n任务相关信息如下：{param_str}")
        now = datetime.now()
        current = now.strftime("%Y-%m-%d %H:%M:%S")
        summary_parts.append(f"\n当前时间如下：{current}")


    
    # if status and not param_list:
    #     #FIXME: 根据TaskName 存在不同的任务名称，需要根据不同的任务名称来确定回复内容
    #     summary_parts.append(f"\n当前任务已完成，请回复用户如下内容：好嘞，正在帮你叫车，叫到了通知你")

    logger.debug(f"operation: {operation}")
    if operation=="confirm_important_task":
        print("function_names:",function_names)
        for function_name in  function_names:
            if  function_name == "call_taxi_service":
                summary_parts.append(f"\n当前任务已完成，请回复用户如下内容：好嘞，正在帮你叫车，叫到了通知你")
            # elif function_name == "call_taxi_service":
            #     summary_parts.append(f"\n当前任务已完成，请回复用户如下内容：好嘞，正在帮你叫车，叫到了通知你")

    content_summary = " ".join(summary_parts)

    # 3. Format the final output
    formatted_response = {
        "role": "tool",
        "tool_call_id": tool_call_id,
        "name": operation,
        "content": content_summary
    }

    return formatted_response

def remove_null_values(obj):
    ## 删除无效信息
    if isinstance(obj, dict):
        return {k: remove_null_values(v) for k, v in obj.items() if v is not None}
    elif isinstance(obj, list):
        return [remove_null_values(item) for item in obj]
    else:
        return obj


def format_dict_list_to_text(data_list: List[Dict[str, str]]) -> str:
    """
    将List[dict]转换为纯文本格式
    
    Args:
        data_list: 包含字典的列表，每个字典包含name, value, desc字段
        
    Returns:
        格式化后的纯文本字符串
    """
    result_parts = []
    
    for item in data_list:
        name = item.get('name', '')
        value = item.get('value', '')
        desc = item.get('desc', '')
        
        # 处理缺失value的情况
        if not value:
            result_parts.append(f"{name}: 无值")
            continue
            
        # 检查name是否包含status或cancel
        if 'status' in name.lower() or 'cancel' in name.lower():
            # 当name包含status或cancel时，name/values/desc都有含义
            result_parts.append(f"{name}: {value} ({desc})")
        else:
            # 当name不包含status或cancel时，使用desc + values
            result_parts.append(f"{desc}: {value}")
    
    return "\n".join(result_parts)

# --- Example Usage ---
if __name__ == "__main__":
    # Test the new format_dict_list_to_text function
    print("--- Testing format_dict_list_to_text Function ---")
    
    test_data = [
        {'name': 'order_status', 'value': '99', 'desc': '服务取消'},
        {'name': 'cancel_reason', 'value': '99', 'desc': '系统取消'},
        {'name': 'payment_status', 'value': '0', 'desc': '无需支付'},
        {'name': 'refund_status', 'value': '0', 'desc': '无需退款'},
        {'name': 'evaluation_status', 'value': '0', 'desc': '无需评价'},
        {'name': 'estimated_actual_price', 'value': '102.14', 'desc': '预估实际价格，单位元'},
        {'name': 'estimated_travel_time', 'value': '34', 'desc': '预估全程行驶时间，单位分钟'},
        {'name': 'estimated_travel_distance', 'value': '13519', 'desc': '预估全程行驶距离，单位米'},
        {'name': 'start_location_name', 'value': '北京南站-东停车场M层(夹层)-A2通道-网约车上车点', 'desc': '起点名称'},
        {'name': 'end_location_name', 'value': '北京西站', 'desc': '终点名称'},
        {'name': 'driver2_end_loc_duration', 'value': '70', 'desc': '司机/乘客到达目的地剩余时间，单位分钟'},
        {'name': 'driver_eta_to_end_loc', 'value': '15:19', 'desc': '司机/乘客到达目的地预计时间，格式为HH:mm'},
        {'name': 'driver2_end_loc_distance', 'value': '26100', 'desc': '司机/乘客到目的地剩余距离，单位米'}
    ]
    
    formatted_text = format_dict_list_to_text(test_data)
    print("Input data:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    print("\nFormatted text output:")
    print(formatted_text)
    
    # Test with missing value
    print("\n--- Testing with missing value ---")
    test_data_missing = [
        {'name': 'test_field', 'value': '', 'desc': '测试字段'},
        {'name': 'normal_field', 'value': 'normal_value', 'desc': '正常字段'}
    ]
    
    formatted_text_missing = format_dict_list_to_text(test_data_missing)
    print("Input data with missing value:")
    print(json.dumps(test_data_missing, indent=2, ensure_ascii=False))
    print("\nFormatted text output:")
    print(formatted_text_missing)
