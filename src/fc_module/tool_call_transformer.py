
import json
import structlog
from typing import Dict, Any, Optional, List

logger = structlog.get_logger(__name__)

PARAM_STATUS_INDEX = ["TO_INPUT", "TO_VALIDATE", "TO_CONFIRM", "CONFIRMED"]

def transform_tool_call(
    tool_call: Dict[str, Any],
    task_id: Optional[str] = None,
    wf_last_round: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Transforms a function call result into the specified dictionary format.

    Args:
        tool_call: The tool call object from the LLM, containing function name and arguments.
        task_id: The existing task ID, if available.
        wf_last_round: A dictionary mapping parameter names to their status in the last round of the workflow, if available.

    Returns:
        A dictionary with the transformed data.
    """
    function_name = tool_call.get('function', {}).get('name')
    try:
        function_args = json.loads(tool_call.get('function', {}).get('arguments', '{}'))
    except json.JSONDecodeError:
        logger.error(f"Failed to decode JSON arguments: {tool_call}")
        function_args = {}

    task_parameters_status = dict()
    if wf_last_round:
        for p in wf_last_round.get('param_list', []):
            print("p:",p)
            if p['status'] == "TO_CONFIRM":
                print("change to CONFIRM")
                # 当函数调用这些对应工具，往往代表对于参数的确认
                task_parameters_status[p['name']] = "CONFIRMED"
            else:
                task_parameters_status[p['name']] = p['status']

    # General handling for other functions
    # 1) Handle wf_instance_id
    # If a task_id from a previous context exists, use it. Otherwise, default to "".
    if function_name=="call_taxi_service":
        wf_instance_id =  function_args.get("wf_instance_id", "") or function_args.get("task_id", "")

    else:
        wf_instance_id = task_id or function_args.get("wf_instance_id", "") or function_args.get("task_id", "")
    
    if wf_instance_id == "无":
        wf_instance_id = ""
    # Special handling for 'confirm_parameter'
    if function_name == "user_select_one":
        operation = function_args.get("function_name", "unknown_operation")
        param_list = []
        param_name = function_args.get("param_name")
        param_value = function_args.get("param_value")

        option_id_str = function_args.get("option_id", "0")
        option_id = int(option_id_str) if option_id_str.isdigit() else 0

        status = "TO_VALIDATE"

        if wf_last_round:
            for p in wf_last_round['param_list']:
                if p['name'] == param_name and "select_one" in wf_last_round:
                    if len(wf_last_round["select_one"]) > option_id and option_id != 0:
                        param_value = wf_last_round["select_one"][option_id - 1]

        if param_name and param_value is not None and (isinstance(param_value, str) and param_value.strip()):
            param_entry = {
                "name": param_name,
                "value": param_value,
                "status": status
            }

            param_list.append(param_entry)

        return {
            "wf_instance_id": wf_instance_id,
            "operation": operation,
            "param_list": param_list
        }


    if function_name == "confirm_parameter":
        operation = function_args.get("task_function_name", "unknown_operation")
        
        param_list = []
        param_name = function_args.get("param_name")
        param_value = function_args.get("param_value")
        status = function_args.get("status", "TO_VALIDATE")

        if param_name and param_value is not None and (isinstance(param_value, str) and param_value.strip()):
            param_entry = {
                "name": param_name,
                "value": param_value,
                "status": status
            }
            param_list.append(param_entry)
            
        return {
            "wf_instance_id": wf_instance_id,
            "operation": operation,
            "param_list": param_list
        }

    # 2) Handle operation
    # The operation is the function name. The user example mentioned 'exec_taxi_main_flow'
    # but the comment indicated it should be the function name.
    operation = function_name

    # 3) Handle param_list
    param_list = []
    if function_args:
        for param_name, value in function_args.items():
            # If the parameter value is None or an empty string, it's not included.
            if value is None or (isinstance(value, str) and not value.strip()):
                continue
            if not isinstance(value,str):
                value = json.dumps(value)

            # Determine the status of the parameter
            if param_name in task_parameters_status.keys():
                status = task_parameters_status[param_name]
            else:
                # Default status for new parameters
                status = "TO_VALIDATE" if value == "无" else "CONFIRMED"
                value = "" if value == "无" else value

            param_entry = {
                "name": param_name,
                "value": value,
                "status": status
            }
            param_list.append(param_entry)

    # Assemble the final dictionary
    if operation=="after_task_complete_repeat":
        wf_instance_id = ""
    transformed_data = {
        "wf_instance_id": wf_instance_id,
        "operation": operation,
        "param_list": param_list
    }

    return transformed_data

def transform_tool_calls(
    tools: List[Dict[str, Any]],
    task_id: Optional[str] = None,
    wf_last_round: Optional[Dict[str, Any]] = None
):
    transformed = dict()
    for tool in tools:
        _t = transform_tool_call(tool, task_id, wf_last_round)
        if not transformed:
            transformed = _t
        else:
            transformed["param_list"] += _t["param_list"]

    param_dict = dict()
    for param in transformed["param_list"]:
        if param["name"] not in param_dict:
            param_dict[param["name"]] = param
        else:
            # deduplicate.
            logger.info(f"param {param['name']} already exists, old: {param_dict[param['name']]}, new: {param}")
            old_status = param_dict[param["name"]]["status"]
            if PARAM_STATUS_INDEX.index(param["status"]) < PARAM_STATUS_INDEX.index(old_status):
                logger.info(f"new status {param['status']} is lower than old status {old_status}, use new status")
                param_dict[param["name"]] = param
    transformed["param_list"] = list(param_dict.values())
    return transformed

# --- Example Usage ---
if __name__ == "__main__":
    print("--- Running Transformation Examples ---")

    # --- Scenario 1: User's first-time request to create a task ---
    print("\n--- Scenario 1: New Task Creation ---")
    first_time_tool_call = {
        "function": {
            "name": "call_taxi_service",
            "arguments": '{"start_place": "北京首都国际机场", "end_place": "北京环球影城"}'
        }
    }
    # No existing task_id, so it will be set to ""
    # No existing parameter statuses, so they will default to "to_validate"
    transformed_output_1 = transform_tool_call(first_time_tool_call)
    print("Input tool_call:")
    print(json.dumps(first_time_tool_call, indent=2, ensure_ascii=False))
    print("\nTransformed output:")
    print(json.dumps(transformed_output_1, indent=2, ensure_ascii=False))
    # Expected: wf_instance_id="", all params "to_validate"

    print("\n" + "="*40 + "\n")

    # --- Scenario 2: User continues a conversation with an existing task_id ---
    print("--- Scenario 2: Continuing an Existing Task ---")
    existing_task_tool_call = {
        "function": {
            "name": "call_taxi_service",
            "arguments": '{"start_place": "东方明珠", "end_place": "上海迪士尼度假区", "car_prefer": "舒适型"}'
        }
    }
    existing_task_id = "task_12345"
    # Simulate that 'start_place' was confirmed in a previous turn
    param_statuses = {
        "start_place": "confirmed",
        "end_place": "TO_VALIDATE",
        # 'car_prefer' is new, so it will default to "to_validate"
    }
    transformed_output_2 = transform_tool_call(existing_task_tool_call, existing_task_id, param_statuses)
    print("Input tool_call:")
    print(json.dumps(existing_task_tool_call, indent=2, ensure_ascii=False))
    print(f"\nInput context: task_id='{existing_task_id}', statuses={param_statuses}")
    print("\nTransformed output:")
    print(json.dumps(transformed_output_2, indent=2, ensure_ascii=False))
    # Expected: wf_instance_id="task_12345", start_place is "confirmed", others "to_validate"

    print("\n" + "="*40 + "\n")

    # --- Scenario 3: Function call with a missing required parameter (value is None or empty) ---
    print("--- Scenario 3: Parameter with None/Empty Value ---")
    missing_param_tool_call = {
        "function": {
            "name": "call_taxi_service",
            "arguments": '{"start_place": "广州塔", "end_place": "", "car_prefer": null}'
        }
    }
    # 'end_place' and 'car_prefer' should not be in the final param_list
    transformed_output_3 = transform_tool_call(missing_param_tool_call)
    print("Input tool_call:")
    print(json.dumps(missing_param_tool_call, indent=2, ensure_ascii=False))
    print("\nTransformed output:")
    print(json.dumps(transformed_output_3, indent=2, ensure_ascii=False))
    # Expected: param_list only contains 'start_place'

    print("\n" + "="*40 + "\n")

    # --- Scenario 4: Handling 'confirm_parameter' function call ---
    print("--- Scenario 4: 'confirm_parameter' call ---")
    confirm_parameter_tool_call = {
        "function": {
            "name": "confirm_parameter",
            "arguments": '{"task_id": "task_12345", "task_function_name": "call_taxi_service", "param_name": "end_place", "param_value": "北京环球影城", "status": "CONFIRMED"}'
        }
    }
    transformed_output_4 = transform_tool_call(confirm_parameter_tool_call)
    print("Input tool_call:")
    print(json.dumps(confirm_parameter_tool_call, indent=2, ensure_ascii=False))
    print("\nTransformed output:")
    print(json.dumps(transformed_output_4, indent=2, ensure_ascii=False))
    # Expected: wf_instance_id="task_12345", operation="call_taxi_service", param_list contains 'end_place' with status "confirmed"

    test_data = [{'id': 'call_7eee4c9d5a79418286bbf9', 'type': 'function', 'function': {'name': 'call_taxi_service', 'arguments': '{"start_place": "方正大厦", "end_place": "朝阳公园", "wf_instance_id": ""}'}}]
    test_data_function = test_data[0]
    print("test_data_function:",test_data_function)
    transformed_output_5 = transform_tool_call(test_data_function,"584",{"param_list": [{"name": "start_place", "value": "方正大厦", "status": "TO_VALIDATE"}, {"name": "end_place", "value": "朝阳公园", "status": "TO_VALIDATE"}, {"name": "wf_instance_id", "value": "584", "status": "TO_VALIDATE"}]})

    print("\nTransformed output:")
    print(json.dumps(transformed_output_5, indent=2, ensure_ascii=False))

    test_data = [{'id': 'call_9227cca5e95145119bb517', 'type': 'function', 'function': {'name': 'after_task_complete_repeat', 'arguments': '{"wf_instance_id": "1625", "function_name": "call_taxi_service", "need_user_confirm": "true", "replace_param": []}'}}]

    transformed_output_6 = transform_tool_call(test_data[0],"584",{})
    print("\nTransformed output:")
    print(json.dumps(transformed_output_6, indent=2, ensure_ascii=False))


