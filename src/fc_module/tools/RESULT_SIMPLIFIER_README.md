# 结果精简工具使用说明

## 概述

这个工具用于将 `get_product_price_by_keyword` 和 `get_product_price_range` 函数的复杂JSON结果转换为简洁易读的纯文本格式，保留关键有效信息。

## 功能特点

- ✅ 提取价格范围信息（最低价、最高价、平均价）
- ✅ 显示商品基本信息（标题、品牌、分类）
- ✅ 保留搜索关键词信息
- ✅ 显示警告和错误信息
- ✅ 使用表情符号增强可读性
- ✅ 完全兼容现有API接口

## 安装和使用

### 基本使用

```python
from src.fc_module.tools.product_price_tools import get_product_price_by_keyword, get_product_price_range
from src.fc_module.tools.result_simplifier import simplify_keyword_search_result, simplify_image_search_result

# 关键词搜索精简
keyword_result = get_product_price_by_keyword("苹果手机")
simplified_text = simplify_keyword_search_result(keyword_result)
print(simplified_text)

# 图片搜索精简
image_urls = ["http://example.com/image.jpg"]
image_result = get_product_price_range(image_urls)
simplified_text = simplify_image_search_result(image_result)
print(simplified_text)
```

### 通用函数

也可以使用通用函数处理两种类型的结果：

```python
from src.fc_module.tools.result_simplifier import simplify_product_price_result

# 适用于两种类型的搜索结果
simplified_text = simplify_product_price_result(any_search_result)
```

## 输出示例

### 成功结果示例

```
💰 价格范围: 4399.00 - 9799.00 元
📊 平均价格: 6287.33 元
🔢 价格样本数: 30 个

🛒 商品信息:
1. 【国家补贴15%/顺丰速发】Apple/苹果 iPhone 16 官方旗舰 国行正品 全新5G手机 国家补贴 | 品牌: Apple | 分类: 手机

🔍 搜索关键词: 苹果手机
```

### 失败结果示例

```
❌ 搜索失败: 未找到相关商品信息
```

## 函数说明

### `simplify_product_price_result(result)`
通用精简函数，适用于两种搜索类型的结果。

**参数:**
- `result`: `get_product_price_by_keyword` 或 `get_product_price_range` 的返回结果

**返回:** 精简后的纯文本字符串

### `simplify_keyword_search_result(result)`
专门用于关键词搜索结果的精简函数。

### `simplify_image_search_result(result)`
专门用于图片搜索结果的精简函数。

## 集成建议

### 1. 更新现有测试代码

将现有的测试代码中的详细输出替换为精简输出：

```python
# 之前
if result['success']:
    print(f"价格范围: {result['price_range']}")
    # 详细打印每个商品信息...
    
# 之后
if result['success']:
    simplified = simplify_keyword_search_result(result)
    print(simplified)
```

### 2. 在API响应中使用

在返回给前端或用户的API响应中，可以使用精简格式：

```python
def api_search_keyword(keyword):
    result = get_product_price_by_keyword(keyword)
    if result['success']:
        return {
            'success': True,
            'simplified_text': simplify_keyword_search_result(result),
            'raw_data': result  # 可选：保留原始数据
        }
    else:
        return {
            'success': False,
            'error': simplify_keyword_search_result(result)
        }
```

### 3. 日志记录

在日志记录中使用精简格式，减少日志体积：

```python
import logging
logger = logging.getLogger(__name__)

result = get_product_price_by_keyword(keyword)
logger.info(f"搜索结果: {simplify_keyword_search_result(result)}")
```

## 优势

1. **简洁性**: 从复杂的JSON结构中提取关键信息
2. **可读性**: 使用表情符号和清晰格式增强用户体验
3. **兼容性**: 完全兼容现有代码，无需修改原有函数
4. **灵活性**: 提供通用和专用函数，满足不同场景需求
5. **错误处理**: 妥善处理各种错误情况，提供友好的错误信息

## 测试

运行测试脚本验证功能：

```bash
# 测试精简功能
python src/fc_module/tools/result_simplifier.py

# 演示使用示例
python src/fc_module/tools/demo_simplify_results.py

# 测试关键词搜索（使用精简输出）
python src/fc_module/tools/test_keyword_tool.py

# 测试图片搜索（使用精简输出）
python src/fc_module/tools/test_product_price_tool.py
```

## 注意事项

- 精简工具不会修改原始数据，只是提供格式转换
- 表情符号在终端和大多数现代应用中都能正常显示
- 如果需要进一步自定义输出格式，可以修改 `simplify_product_price_result` 函数
