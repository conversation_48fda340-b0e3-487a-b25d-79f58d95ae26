# -*- coding: utf-8 -*-
"""
条形码处理工具模块
处理商品条形码链接，提取条形码code
"""

import re
import requests
from typing import Optional, Dict, Any, Union
from urllib.parse import urlparse, parse_qs
import json


def extract_barcode_from_url(barcode_url: str) -> Optional[str]:
    """
    从条形码链接中提取条形码code
    
    Args:
        barcode_url: 商品条形码的链接
        
    Returns:
        条形码code字符串，如果提取失败返回None
        
    Examples:
        >>> extract_barcode_from_url("https://example.com/barcode?code=1234567890123")
        "1234567890123"
        >>> extract_barcode_from_url("https://barcode.com/product/9876543210987")
        "9876543210987"
    """
    if not barcode_url or not isinstance(barcode_url, str):
        return None
    
    try:
        # 方法1: 从URL参数中提取
        parsed_url = urlparse(barcode_url)
        query_params = parse_qs(parsed_url.query)
        
        # 常见的条形码参数名
        barcode_param_names = ['code', 'barcode', 'ean', 'upc', 'gtin', 'isbn', 'product_code']
        
        for param_name in barcode_param_names:
            if param_name in query_params and query_params[param_name]:
                code = query_params[param_name][0]
                if _is_valid_barcode(code):
                    return code
        
        # 方法2: 从URL路径中提取数字序列
        path = parsed_url.path
        
        # 匹配常见的条形码格式（8-14位数字）
        barcode_patterns = [
            r'/(\d{13})(?:/|$)',  # EAN-13 (13位)
            r'/(\d{12})(?:/|$)',  # UPC-A (12位)
            r'/(\d{14})(?:/|$)',  # GTIN-14 (14位)
            r'/(\d{8})(?:/|$)',   # EAN-8 (8位)
            r'/(\d{10})(?:/|$)',  # ISBN-10 (10位)
            r'barcode[/_-](\d{8,14})(?:/|$)',  # 包含barcode关键词
            r'product[/_-](\d{8,14})(?:/|$)',  # 包含product关键词
            r'item[/_-](\d{8,14})(?:/|$)',     # 包含item关键词
        ]
        
        for pattern in barcode_patterns:
            match = re.search(pattern, path, re.IGNORECASE)
            if match:
                code = match.group(1)
                if _is_valid_barcode(code):
                    return code
        
        # 方法3: 从完整URL中提取任何8-14位数字序列
        all_numbers = re.findall(r'\d{8,14}', barcode_url)
        for number in all_numbers:
            if _is_valid_barcode(number):
                return number
                
        return None
        
    except Exception as e:
        print(f"提取条形码时出错: {e}")
        return None


def _is_valid_barcode(code: str) -> bool:
    """
    验证条形码是否有效
    
    Args:
        code: 条形码字符串
        
    Returns:
        是否为有效的条形码
    """
    if not code or not code.isdigit():
        return False
    
    length = len(code)
    # 常见条形码长度: EAN-8(8), UPC-A(12), EAN-13(13), GTIN-14(14), ISBN-10(10)
    valid_lengths = [8, 10, 12, 13, 14]
    
    return length in valid_lengths


def get_barcode_info(barcode_code: str) -> Dict[str, Any]:
    """
    根据条形码获取商品信息
    
    Args:
        barcode_code: 条形码code
        
    Returns:
        包含商品信息的字典
    """
    if not barcode_code or not _is_valid_barcode(barcode_code):
        return {
            'success': False,
            'error': '无效的条形码',
            'barcode': barcode_code
        }
    
    # 判断条形码类型
    barcode_type = _get_barcode_type(barcode_code)
    
    return {
        'success': True,
        'barcode': barcode_code,
        'type': barcode_type,
        'length': len(barcode_code),
        'country_code': _get_country_code(barcode_code) if barcode_type in ['EAN-13', 'EAN-8'] else None,
        'check_digit': barcode_code[-1] if len(barcode_code) > 1 else None,
        'is_valid': _validate_check_digit(barcode_code)
    }


def _get_barcode_type(code: str) -> str:
    """
    根据长度判断条形码类型
    
    Args:
        code: 条形码字符串
        
    Returns:
        条形码类型
    """
    length = len(code)
    type_mapping = {
        8: 'EAN-8',
        10: 'ISBN-10',
        12: 'UPC-A',
        13: 'EAN-13',
        14: 'GTIN-14'
    }
    return type_mapping.get(length, f'Unknown-{length}')


def _get_country_code(code: str) -> Optional[str]:
    """
    从EAN码中提取国家/地区代码
    
    Args:
        code: EAN条形码
        
    Returns:
        国家/地区代码
    """
    if len(code) == 13:
        prefix = code[:3]
        # 中国大陆: 690-699
        if '690' <= prefix <= '699':
            return 'CN'
        # 美国/加拿大: 000-139
        elif '000' <= prefix <= '139':
            return 'US/CA'
        # 其他常见国家代码可以继续添加
    elif len(code) == 8:
        prefix = code[:2]
        # 简化的EAN-8国家代码判断
        if prefix in ['69']:
            return 'CN'
    
    return None


def _validate_check_digit(code: str) -> bool:
    """
    验证条形码校验位
    
    Args:
        code: 条形码字符串
        
    Returns:
        校验位是否正确
    """
    if not code or len(code) < 2:
        return False
    
    try:
        if len(code) == 13:  # EAN-13
            return _validate_ean13(code)
        elif len(code) == 8:  # EAN-8
            return _validate_ean8(code)
        elif len(code) == 12:  # UPC-A
            return _validate_upc_a(code)
        else:
            # 对于其他类型，暂时返回True
            return True
    except:
        return False


def _validate_ean13(code: str) -> bool:
    """验证EAN-13校验位"""
    if len(code) != 13:
        return False
    
    digits = [int(d) for d in code[:-1]]
    check_digit = int(code[-1])
    
    # EAN-13校验算法
    odd_sum = sum(digits[i] for i in range(0, 12, 2))
    even_sum = sum(digits[i] for i in range(1, 12, 2))
    total = odd_sum + even_sum * 3
    calculated_check = (10 - (total % 10)) % 10
    
    return calculated_check == check_digit


def _validate_ean8(code: str) -> bool:
    """验证EAN-8校验位"""
    if len(code) != 8:
        return False
    
    digits = [int(d) for d in code[:-1]]
    check_digit = int(code[-1])
    
    # EAN-8校验算法
    odd_sum = sum(digits[i] for i in range(0, 7, 2))
    even_sum = sum(digits[i] for i in range(1, 7, 2))
    total = odd_sum * 3 + even_sum
    calculated_check = (10 - (total % 10)) % 10
    
    return calculated_check == check_digit


def _validate_upc_a(code: str) -> bool:
    """验证UPC-A校验位"""
    if len(code) != 12:
        return False
    
    digits = [int(d) for d in code[:-1]]
    check_digit = int(code[-1])
    
    # UPC-A校验算法
    odd_sum = sum(digits[i] for i in range(0, 11, 2))
    even_sum = sum(digits[i] for i in range(1, 11, 2))
    total = odd_sum * 3 + even_sum
    calculated_check = (10 - (total % 10)) % 10
    
    return calculated_check == check_digit


def process_barcode_url(barcode_url: str) -> Dict[str, Any]:
    """
    处理条形码链接的主函数
    
    Args:
        barcode_url: 商品条形码的链接
        
    Returns:
        包含条形码code和相关信息的字典
        
    Examples:
        >>> result = process_barcode_url("https://example.com/barcode?code=1234567890123")
        >>> print(result['barcode_code'])
        "1234567890123"
    """
    result = {
        'success': False,
        'barcode_code': None,
        'barcode_info': {},
        'original_url': barcode_url,
        'error': None
    }
    
    try:
        # 提取条形码
        barcode_code = extract_barcode_from_url(barcode_url)
        
        if not barcode_code:
            result['error'] = '无法从URL中提取有效的条形码'
            return result
        
        # 获取条形码信息
        barcode_info = get_barcode_info(barcode_code)
        
        result.update({
            'success': True,
            'barcode_code': barcode_code,
            'barcode_info': barcode_info
        })
        
        return result
        
    except Exception as e:
        result['error'] = f'处理条形码URL时出错: {str(e)}'
        return result


def get_barcode_code_from_url(barcode_url: str) -> str:
    """
    简化版函数：从商品条形码链接中提取条形码code

    Args:
        barcode_url: 商品条形码的链接

    Returns:
        条形码code字符串，如果提取失败返回空字符串

    Examples:
        >>> get_barcode_code_from_url("https://example.com/barcode?code=1234567890123")
        "1234567890123"
        >>> get_barcode_code_from_url("https://barcode.com/product/9876543210987")
        "9876543210987"
        >>> get_barcode_code_from_url("https://invalid-url.com/no-barcode")
        ""
    """
    try:
        result = process_barcode_url(barcode_url)
        return result.get('barcode_code', '') or ''
    except Exception:
        return ''


# 示例和测试代码
if __name__ == "__main__":
    # 测试用例
    test_urls = [
        "https://example.com/barcode?code=1234567890123",
        "https://barcode.com/product/9876543210987",
        "https://shop.com/item/6901234567890/details",
        "https://api.barcode.com/lookup/barcode_6901234567890",
        "https://invalid-url.com/no-barcode"
    ]

    print("=== 条形码处理测试 ===\n")

    for url in test_urls:
        print(f"测试URL: {url}")

        # 测试简化版函数
        code = get_barcode_code_from_url(url)
        print(f"条形码code: {code}")

        # 测试完整版函数
        result = process_barcode_url(url)
        print(f"完整结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        print("-" * 50)
