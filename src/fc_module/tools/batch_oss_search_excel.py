# -*- coding: utf-8 -*-
"""
批量遍历 /Users/<USER>/Downloads/测试照片 下的图片：
1) 上传到阿里云 OSS，生成可访问链接
2) 调用图片检索接口（is_auto_first_cate_name=0），拿到结果
3) 将每张图的“真实标签(测试照片下一级目录名) + 上传链接 + 检索结果”等写入 Excel

注意：
- 不打开其他文件；脚本仅访问图片文件、并通过参数/环境变量读取必要配置。
- 图片检索接口通过 --api-url 传入，鉴权可用 --api-key 或环境变量 X-Api-Key。

运行示例：
  python3 batch_oss_search_excel.py \
    --api-url "https://your-search.example.com/api/search" \
    --api-key "$X_Api_Key" \
    --base-dir "/Users/<USER>/Downloads/测试照片" \
    --output "results.xlsx"

必要依赖：oss2, requests, openpyxl
  pip install oss2 requests openpyxl
"""
from __future__ import annotations
import os
import sys
import json
import time
import argparse
import traceback
from typing import Dict, Any, List, Tuple

import requests



IMAGE_EXTS = {".jpg", ".jpeg", ".png", ".bmp", ".webp", ".gif"}
X_Api_Key ="25767dd78548ff5e590f4f02fa62ca00"

def is_image_file(path: str) -> bool:
    ext = os.path.splitext(path)[1].lower()
    return ext in IMAGE_EXTS


def iter_images_with_label(base_dir: str) -> List[Tuple[str, str, str]]:
    """
    遍历 base_dir 下所有图片。真实标签为相对 base_dir 的第一层目录名。
    返回 (abs_path, rel_path, label) 列表
    其中：
      - abs_path: 图片绝对路径
      - rel_path: 图片相对 base_dir 的路径（用于在 OSS 上保持层级）
      - label: 第一层目录名
    """
    results: List[Tuple[str, str, str]] = []
    base_dir = os.path.abspath(base_dir)
    for root, _, files in os.walk(base_dir):
        for fname in files:
            abs_path = os.path.join(root, fname)
            if not is_image_file(abs_path):
                continue
            rel_path = os.path.relpath(abs_path, base_dir)
            parts = rel_path.split(os.sep)
            if len(parts) < 2:
                # 直接放在 base_dir 根下的文件，无法确定“下一级目录=真实标签”，跳过
                continue
            label = parts[0]
            results.append((abs_path, rel_path, label))
    return results


# def build_bucket(endpoint: str, access_key_id: str, access_key_secret: str, bucket_name: str) -> oss2.Bucket:
#     auth = oss2.Auth(access_key_id, access_key_secret)
#     bucket = oss2.Bucket(auth, endpoint, bucket_name)
#     return bucket


# def upload_and_sign(bucket: oss2.Bucket, object_name: str, local_file: str, sign_expires: int = 7200, retry: int = 3, backoff: float = 1.5) -> Tuple[bool, str, str]:
#     """
#     上传文件并生成签名 URL。
#     返回 (ok, url, err)
#     """
#     last_err = ""
#     for i in range(retry):
#         try:
#             bucket.put_object_from_file(object_name, local_file)
#             url = bucket.sign_url('GET', object_name, sign_expires)
#             return True, url, ""
#         except Exception as e:
#             last_err = f"upload_sign_error[{i+1}/{retry}]: {e}"
#             time.sleep(backoff ** i)
#     return False, "", last_err


def call_search_api(api_url: str, api_key: str | None, img_url: str, is_auto_first_cate_name: int = 0, timeout: float = 15.0) -> Tuple[bool, Dict[str, Any] | None, str, str]:
    """
    调用图片检索接口，返回 (ok, json_obj, raw_text, err)
    请求体包含：link/url 与 is_auto_first_cate_name=0
    """
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["X-Api-Key"] = api_key

    # payload = {
    #     # 同时提供常见字段名，服务端取其一即可
    #     "link": img_url,
    #     "url": img_url,
    #     "image_url": img_url,
    #     "is_auto_first_cate_name": int(is_auto_first_cate_name),
    # }
    payload = {
    "img_url": img_url,
    "img_num": 10,
    "is_auto_first_cate_name": int(is_auto_first_cate_name)
    }

    try:
        resp = requests.post(api_url, headers=headers, json=payload, timeout=timeout)
        raw = resp.text
        if resp.status_code != 200:
            return False, None, raw, f"HTTP {resp.status_code}"
        try:
            data = resp.json()
            return True, data, raw, ""
        except Exception as je:
            return False, None, raw, f"JSON parse error: {je}"
    except Exception as e:
        return False, None, "", f"request error: {e}"




if __name__ == "__main__":
    test_url = "http://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg"
    api_url  = "https://gw-openapi.zhidemai.com/img-search-service/v1/img_search_by_pic"

    result = call_search_api(api_url=api_url,api_key=X_Api_Key,img_url=test_url)
    print("result:",result)

