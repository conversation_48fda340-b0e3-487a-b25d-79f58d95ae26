import time
from typing import Dict
import hashlib
import urllib.parse
import urllib.request
import urllib.error
import json



def get_signature(params: Dict[str, str], app_key: str) -> str:
    # 1-2: sort params by key and build query string
    sorted_items = sorted(params.items(), key=lambda kv: kv[0])
    parts = [f"{k}={v}" for k, v in sorted_items]
    query_string = "&".join(parts)

    # 3-4: append app_key
    string_to_sign = query_string + app_key
    # print the string to be signed (mimic System.out.println)
    print(string_to_sign)

    # 5: compute MD5
    md5 = hashlib.md5()
    md5.update(string_to_sign.encode("utf-8"))
    sign = md5.hexdigest()

    # 6: return signature
    return sign


base = "http://m.51huisheng.cn"
bijia = "/vip/oem/pro/bijia"
sousuo="/vip/oem/pro/get_item_v2"
channel = "guangfan"
appkey = "yBwCwQjE3idfWEDQDWk32dfWvDHLbRI"


def fanli_get(store,good_id: str):
    """Send GET request to base+bijia with params as query parameters.

    Prints the signed string, request URL, status and response body.
    Returns the response body as text on success, or None on failure.
    """
    timestamp = int(time.time())
    params = {
        "store": store,
        "channel": channel,
        "item_id": good_id,
        "t": str(timestamp),
    }
    sign = get_signature(params, appkey)
    params["sign"] = sign

    url = base + bijia
    query = urllib.parse.urlencode(params)
    full_url = url + "?" + query

    print("Request URL:", full_url)

    try:
        req = urllib.request.Request(full_url, method="GET")
        with urllib.request.urlopen(req, timeout=10) as resp:
            status = getattr(resp, "status", None)
            body = resp.read().decode("utf-8", errors="replace")
            print("Status code:", status)
            # print("Response body:", body)
            return json.loads(body)
    except urllib.error.HTTPError as e:
        print("HTTP error:", e.code, e.reason)
        try:
            err_body = e.read().decode("utf-8", errors="replace")
            print("Error body:", err_body)
        except Exception:
            pass
        return None
    except urllib.error.URLError as e:
        print("Request failed:", e.reason)
        return None

def sousuo_get(keyword: str):
    timestamp = int(time.time())
    params = {
        "channel": channel,
        "keyword": keyword,
        "t": str(timestamp),
    }
    sign = get_signature(params, appkey)
    params["sign"] = sign

    url = base + sousuo
    query = urllib.parse.urlencode(params)
    full_url = url + "?" + query

    print("Request URL:", full_url)
    try:
        req = urllib.request.Request(full_url, method="GET")
        with urllib.request.urlopen(req, timeout=10) as resp:
            status = getattr(resp, "status", None)
            body = resp.read().decode("utf-8", errors="replace")
            print("Status code:", status)
            # print("Response body:", body)
            return json.loads(body)
    except urllib.error.HTTPError as e:
        print("HTTP error:", e.code, e.reason)
        try:
            err_body = e.read().decode("utf-8", errors="replace")
            print("Error body:", err_body)
        except Exception:
            pass
        return None
    except urllib.error.URLError as e:
        print("Request failed:", e.reason)
        return None


if __name__ == "__main__":
    import time
    start_time = time.time()
    sousuo_get("笔记本电脑")
    end_time = time.time()
    print("elca time 1:",end_time-start_time)

    start_time = time.time()
    result = fanli_get("jd","6vVtwT0MNfQBvpCeFERtsceg_sweEvrt2exzoXBKCA5")
    end_time = time.time()
    print("elca time 2:",end_time-start_time)
    print("result:",result)

