# -*- coding: utf-8 -*-
"""
调试比价API返回结果
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from src.fc_module.tools.bijia import fanli_get

def debug_bijia_api():
    """调试比价API返回结果"""
    print("=== 调试比价API返回结果 ===\n")
    
    # 测试几个商品ID
    test_item_ids = [
        "zRaPADdceta3OB2557UvXxhdt4-9OKVxvFOwewXakXcY",  # 苹果手机
        "88noMnAoSnt9vODa0kPIgAKfbt8-nMNPz4Txe5MB3ZwSB8",  # 笔记本电脑
        "eRa2MRaCDtdWQRnk40S8K2uzt0-DoDRJmIPpakXm6PU6y",  # 华为平板
        "DDPgNRaiDt50qNoreZUQzRhjtn-bWa3z4sGVX3Y76yHWj"   # 小米手环
    ]
    
    for item_id in test_item_ids:
        print(f"调试商品ID: {item_id}")
        print("-" * 50)
        
        try:
            result = fanli_get('jd', item_id)
            
            if result:
                print(f"API响应状态: {result.get('status', 'N/A')}")
                print(f"API响应信息: {result.get('info', 'N/A')}")
                
                if 'data' in result:
                    data = result['data']
                    print(f"data字段包含的键: {list(data.keys())}")
                    
                    # 检查所有可能包含价格信息的字段
                    for key in data:
                        if isinstance(data[key], list):
                            print(f"字段 {key}: 类型={type(data[key])}, 长度={len(data[key])}")
                            
                            if data[key]:
                                print(f"  前3个元素示例:")
                                for i, item in enumerate(data[key][:3]):
                                    if isinstance(item, dict):
                                        print(f"    元素 {i+1}:")
                                        for item_key, item_value in list(item.items())[:5]:  # 只显示前5个键值对
                                            print(f"      {item_key}: {item_value}")
                                    else:
                                        print(f"    元素 {i+1}: {item}")
                                print()
                        else:
                            print(f"字段 {key}: {data[key]}")
                            
                else:
                    print("  响应中没有data字段")
            else:
                print("  API调用返回None")
                
        except Exception as e:
            print(f"❌ 调试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    debug_bijia_api()
