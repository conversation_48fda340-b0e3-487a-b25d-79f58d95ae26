# -*- coding: utf-8 -*-
"""
演示图片搜索和比价核心逻辑
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from src.fc_module.tools.product_price_tools import (
    call_search_api, analyze_image_quality, get_top_items,
    extract_item_info, search_product_by_keyword, get_price_info
)

# API配置
SEARCH_API_URL = "https://gw-openapi.zhidemai.com/img-search-service/v1/img_search_by_pic"
SEARCH_API_KEY = "25767dd78548ff5e590f4f02fa62ca00"

def demo_core_logic():
    """演示核心逻辑实现"""
    print("=== 图片搜索和比价核心逻辑演示 ===\n")
    
    # 测试图片URL
    test_image_url = "http://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg"
    
    print("1. 图片搜索API调用")
    print("=" * 50)
    success, search_result, raw_text, err = call_search_api(
        api_url=SEARCH_API_URL,
        api_key=SEARCH_API_KEY,
        img_url=test_image_url,
        is_auto_first_cate_name=0
    )
    
    if not success:
        print(f"图片搜索失败: {err}")
        return
    
    print("✅ 图片搜索成功")
    print(f"搜索结果数量: {len(search_result['data']) if isinstance(search_result['data'], list) else 'N/A'}")
    
    # 分析图片质量
    print("\n2. 图片质量评估")
    print("=" * 50)
    quality = analyze_image_quality(search_result)
    print(f"图片质量评分: {quality:.2f}/1.0")
    
    # 获取TOP商品
    print("\n3. 获取TOP商品")
    print("=" * 50)
    top_items = get_top_items(search_result, 3)
    print(f"找到 {len(top_items)} 个TOP商品")
    
    for i, item in enumerate(top_items):
        item_info = extract_item_info(item)
        print(f"\n商品 {i+1}:")
        print(f"  标题: {item_info['title']}")
        print(f"  品牌: {item_info['brand']}")
        print(f"  分类: {item_info['category']}")
        print(f"  分数: {item_info['score']}")
        print(f"  商品ID: {item_info['item_id']}")
    
    # 商品搜索和比价
    print("\n4. 商品搜索和比价")
    print("=" * 50)
    if top_items:
        first_item = extract_item_info(top_items[0])
        
        # 通过标题搜索获取真实商品ID
        print(f"搜索商品: {first_item['title']}")
        search_result = search_product_by_keyword(first_item['title'])
        
        if search_result:
            print("✅ 商品搜索成功")
            real_item_id = search_result.get('item_id')
            shop_id = search_result.get('shop_id')
            platform = search_result.get('platform', 'jd')
            
            print(f"真实商品ID: {real_item_id}")
            print(f"店铺ID: {shop_id}")
            print(f"平台: {platform}")
            
            # 获取价格信息
            print(f"\n查询价格信息...")
            price_list = get_price_info(platform, real_item_id)
            
            if price_list:
                print("✅ 价格查询成功")
                print(f"找到 {len(price_list)} 个价格信息")
                
                # 显示前3个价格
                for i, price_item in enumerate(price_list[:3]):
                    print(f"  价格 {i+1}:")
                    print(f"    店铺: {price_item.get('shop_name', 'N/A')}")
                    print(f"    标题: {price_item.get('item_title', 'N/A')}")
                    print(f"    原价: {price_item.get('origin_price', 'N/A')}")
                    print(f"    到手价: {price_item.get('ds_price', 'N/A')}")
                
                # 计算价格范围
                prices = []
                for price_item in price_list:
                    if 'ds_price' in price_item and price_item['ds_price']:
                        try:
                            prices.append(float(price_item['ds_price']))
                        except (ValueError, TypeError):
                            pass
                
                if prices:
                    print(f"\n📊 价格统计:")
                    print(f"  最低价: {min(prices):.2f}元")
                    print(f"  最高价: {max(prices):.2f}元")
                    print(f"  平均价: {sum(prices) / len(prices):.2f}元")
                    print(f"  价格数量: {len(prices)}个")
            else:
                print("❌ 无法获取价格信息")
        else:
            print("❌ 商品搜索失败")
    else:
        print("❌ 未找到相关商品")

if __name__ == "__main__":
    demo_core_logic()
