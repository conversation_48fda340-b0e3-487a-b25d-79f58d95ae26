# -*- coding: utf-8 -*-
"""
演示如何使用结果精简工具
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from src.fc_module.tools.product_price_tools import get_product_price_by_keyword, get_product_price_range
from src.fc_module.tools.result_simplifier import simplify_keyword_search_result, simplify_image_search_result

def demo_keyword_search_simplify():
    """演示关键词搜索结果的精简"""
    print("=== 关键词搜索精简演示 ===\n")
    
    # 测试关键词
    test_keywords = ["苹果手机", "笔记本电脑", "华为平板"]
    
    for keyword in test_keywords:
        print(f"搜索关键词: {keyword}")
        print("-" * 50)
        
        try:
            # 获取原始结果
            raw_result = get_product_price_by_keyword(keyword)
            
            # 显示原始结果（简要）
            print(f"原始结果 - 成功: {raw_result['success']}")
            if raw_result['success']:
                print(f"价格范围: {raw_result['price_range']}")
                print(f"商品数量: {raw_result['product_info']['total_items_found']}")
            
            # 显示精简结果
            print("\n精简结果:")
            simplified = simplify_keyword_search_result(raw_result)
            print(simplified)
            
        except Exception as e:
            print(f"❌ 处理出错: {e}")
        
        print("\n" + "="*50 + "\n")

def demo_image_search_simplify():
    """演示图片搜索结果的精简"""
    print("=== 图片搜索精简演示 ===\n")
    
    # 测试图片URL（需要替换为实际的测试图片URL）
    test_image_urls = [
        ["http://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg"]
    ]
    
    for image_urls in test_image_urls:
        print(f"处理图片: {image_urls[0]}")
        print("-" * 50)
        
        try:
            # 获取原始结果
            raw_result = get_product_price_range(image_urls)
            
            # 显示原始结果（简要）
            print(f"原始结果 - 成功: {raw_result['success']}")
            if raw_result['success']:
                print(f"价格范围: {raw_result['price_range']}")
                print(f"商品数量: {raw_result['product_info']['total_items_found']}")
            
            # 显示精简结果
            print("\n精简结果:")
            simplified = simplify_image_search_result(raw_result)
            print(simplified)
            
        except Exception as e:
            print(f"❌ 处理出错: {e}")
        
        print("\n" + "="*50 + "\n")

def show_usage_examples():
    """显示使用示例"""
    print("=== 使用示例 ===")
    print("\n1. 关键词搜索精简:")
    print("""
from src.fc_module.tools.product_price_tools import get_product_price_by_keyword
from src.fc_module.tools.result_simplifier import simplify_keyword_search_result

result = get_product_price_by_keyword("苹果手机")
simplified_text = simplify_keyword_search_result(result)
print(simplified_text)
""")
    
    print("\n2. 图片搜索精简:")
    print("""
from src.fc_module.tools.product_price_tools import get_product_price_range
from src.fc_module.tools.result_simplifier import simplify_image_search_result

image_urls = ["http://example.com/image.jpg"]
result = get_product_price_range(image_urls)
simplified_text = simplify_image_search_result(result)
print(simplified_text)
""")

if __name__ == "__main__":
    # 演示使用示例
    show_usage_examples()
    
    print("\n" + "="*80 + "\n")
    
    # 演示关键词搜索精简（实际API调用可能会失败，取决于网络和API状态）
    try:
        demo_keyword_search_simplify()
    except Exception as e:
        print(f"关键词搜索演示失败（可能是API问题）: {e}")
    
    print("\n" + "="*80 + "\n")
    
    # 演示图片搜索精简（实际API调用可能会失败，取决于网络和API状态）
    try:
        demo_image_search_simplify()
    except Exception as e:
        print(f"图片搜索演示失败（可能是API问题）: {e}")
