# -*- coding: utf-8 -*-
"""
图片条形码识别工具模块
从图片中识别和提取条形码
"""

import requests
import io
import base64
import time
from typing import Optional, Dict, Any, List
import json
import logging


def estimate_processing_time(image_url: str) -> Dict[str, Any]:
    """
    预估图片条形码识别的处理时间

    Args:
        image_url: 图片URL

    Returns:
        包含时间预估信息的字典
    """
    estimation = {
        'estimated_total_time': 0.0,
        'breakdown': {
            'download_time': 0.0,
            'image_processing_time': 0.0,
            'barcode_detection_time': 0.0
        },
        'factors': [],
        'confidence': 'medium'
    }

    try:
        # 基础时间估算（秒）
        base_download_time = 2.0  # 网络下载基础时间
        base_processing_time = 1.0  # 图像处理基础时间
        base_detection_time = 0.5  # 条形码检测基础时间

        # 根据URL特征调整预估
        if 'oss' in image_url.lower():
            estimation['factors'].append('OSS存储，网络较快')
            base_download_time *= 0.8
        elif 'localhost' in image_url or '127.0.0.1' in image_url:
            estimation['factors'].append('本地文件，下载极快')
            base_download_time = 0.1
        elif 'https' in image_url:
            estimation['factors'].append('HTTPS连接，略慢')
            base_download_time *= 1.1

        # 根据文件扩展名调整
        if '.png' in image_url.lower():
            estimation['factors'].append('PNG格式，处理稍慢')
            base_processing_time *= 1.2
        elif '.jpg' in image_url.lower() or '.jpeg' in image_url.lower():
            estimation['factors'].append('JPEG格式，处理较快')
            base_processing_time *= 0.9

        # 设置预估时间
        estimation['breakdown']['download_time'] = base_download_time
        estimation['breakdown']['image_processing_time'] = base_processing_time
        estimation['breakdown']['barcode_detection_time'] = base_detection_time

        estimation['estimated_total_time'] = sum(estimation['breakdown'].values())

        # 设置置信度
        if estimation['estimated_total_time'] < 2.0:
            estimation['confidence'] = 'high'
        elif estimation['estimated_total_time'] > 5.0:
            estimation['confidence'] = 'low'

        return estimation

    except Exception as e:
        estimation['error'] = f'预估时间计算失败: {str(e)}'
        estimation['estimated_total_time'] = 3.0  # 默认预估时间
        return estimation


def download_image_as_pil(image_url: str):
    """
    从URL下载图片并返回PIL Image对象

    Args:
        image_url: 图片URL

    Returns:
        PIL Image对象，失败时返回None
    """
    try:
        from PIL import Image

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(image_url, headers=headers, timeout=30)
        response.raise_for_status()

        # 将响应内容转换为PIL Image
        image = Image.open(io.BytesIO(response.content))
        return image

    except Exception as e:
        print(f"下载图片失败: {e}")
        return None


def download_image_as_base64(image_url: str) -> Optional[str]:
    """
    从URL下载图片并转换为base64

    Args:
        image_url: 图片URL

    Returns:
        base64编码的图片数据，失败时返回None
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(image_url, headers=headers, timeout=30)
        response.raise_for_status()

        # 将响应内容转换为base64
        image_base64 = base64.b64encode(response.content).decode('utf-8')
        return image_base64

    except Exception as e:
        print(f"下载图片失败: {e}")
        return None


def get_image_info(image_url: str) -> Dict[str, Any]:
    """
    获取图片基本信息

    Args:
        image_url: 图片URL

    Returns:
        图片信息字典
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.head(image_url, headers=headers, timeout=10)
        response.raise_for_status()

        return {
            'content_type': response.headers.get('content-type', ''),
            'content_length': response.headers.get('content-length', ''),
            'url': image_url,
            'accessible': True
        }

    except Exception as e:
        return {
            'url': image_url,
            'accessible': False,
            'error': str(e)
        }


def extract_barcode_with_llm(image_url: str) -> Dict[str, Any]:
    """
    使用OpenCV和pyzbar从图片中提取条形码
    
    Args:
        image_url: 图片URL
        
    Returns:
        包含条形码信息的字典
    """
    result = {
        'success': False,
        'barcodes': [],
        'image_url': image_url,
        'error': None,
        'method': 'opencv_pyzbar'
    }
    
    try:
        # 尝试导入必要的库
        try:
            import cv2
            import numpy as np
            from pyzbar import pyzbar
        except ImportError as e:
            result['error'] = f'缺少必要的库: {str(e)}. 请安装: pip install opencv-python pyzbar pillow'
            return result
        
        # 下载图片
        pil_image = download_image_as_pil(image_url)
        if pil_image is None:
            result['error'] = '无法下载图片'
            return result
        
        # 转换为OpenCV格式
        opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        # 转换为灰度图像
        gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
        
        # 使用pyzbar检测条形码
        barcodes = pyzbar.decode(gray)
        
        if not barcodes:
            result['error'] = '图片中未检测到条形码'
            return result
        
        # 处理检测到的条形码
        detected_barcodes = []
        for barcode in barcodes:
            barcode_data = barcode.data.decode('utf-8')
            barcode_type = barcode.type
            
            # 获取条形码位置信息
            points = barcode.polygon
            if len(points) == 4:
                # 矩形条形码
                x = min([p.x for p in points])
                y = min([p.y for p in points])
                w = max([p.x for p in points]) - x
                h = max([p.y for p in points]) - y
                bbox = {'x': x, 'y': y, 'width': w, 'height': h}
            else:
                bbox = None
            
            detected_barcodes.append({
                'data': barcode_data,
                'type': barcode_type,
                'bbox': bbox,
                'raw_points': [(p.x, p.y) for p in points]
            })
        
        result.update({
            'success': True,
            'barcodes': detected_barcodes,
            'total_found': len(detected_barcodes)
        })
        
        return result
        
    except Exception as e:
        result['error'] = f'处理图片时出错: {str(e)}'
        return result


def extract_barcode_opencv_only(image_url: str) -> Dict[str, Any]:
    """
    仅使用OpenCV进行条形码检测（不依赖pyzbar）
    通过图像处理技术检测条形码区域

    Args:
        image_url: 图片URL

    Returns:
        包含条形码信息的字典
    """
    result = {
        'success': False,
        'barcodes': [],
        'image_url': image_url,
        'error': None,
        'method': 'opencv_only'
    }

    try:
        # 尝试导入OpenCV
        try:
            import cv2
            import numpy as np
        except ImportError as e:
            result['error'] = f'缺少OpenCV库: {str(e)}'
            return result

        # 下载图片
        pil_image = download_image_as_pil(image_url)
        if pil_image is None:
            result['error'] = '无法下载图片'
            return result

        # 转换为OpenCV格式
        opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

        # 转换为灰度图像
        gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)

        # 使用形态学操作检测条形码区域
        # 创建矩形内核
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (21, 7))

        # 应用形态学梯度
        gradient = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, kernel)

        # 二值化
        _, thresh = cv2.threshold(gradient, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)

        # 闭运算连接条形码区域
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (21, 7))
        closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # 腐蚀和膨胀
        closed = cv2.erode(closed, None, iterations=4)
        closed = cv2.dilate(closed, None, iterations=4)

        # 查找轮廓
        contours, _ = cv2.findContours(closed.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 筛选可能的条形码区域
        potential_barcodes = []
        for contour in contours:
            # 计算轮廓的边界矩形
            x, y, w, h = cv2.boundingRect(contour)

            # 条形码通常是宽度大于高度的矩形
            aspect_ratio = w / float(h)

            # 筛选条件：宽高比合适，面积足够大
            if aspect_ratio > 1.5 and cv2.contourArea(contour) > 1000:
                potential_barcodes.append({
                    'bbox': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)},
                    'area': int(cv2.contourArea(contour)),
                    'aspect_ratio': round(aspect_ratio, 2)
                })

        if potential_barcodes:
            result.update({
                'success': True,
                'barcodes': [{
                    'data': 'DETECTED_BARCODE_REGION',  # 无法读取具体数据，只能检测区域
                    'type': 'UNKNOWN',
                    'detection_method': 'opencv_morphology',
                    'regions': potential_barcodes
                }],
                'total_found': len(potential_barcodes),
                'note': '仅检测到条形码区域，无法读取具体数据。需要pyzbar库来解码条形码内容。'
            })
        else:
            result['error'] = '未检测到可能的条形码区域'

        return result

    except Exception as e:
        result['error'] = f'处理图片时出错: {str(e)}'
        return result


def extract_barcode_from_image_simple(image_url: str) -> Dict[str, Any]:
    """
    简化版图片条形码提取（不依赖OpenCV）
    使用基础的图片处理方法

    Args:
        image_url: 图片URL

    Returns:
        包含条形码信息的字典
    """
    result = {
        'success': False,
        'barcodes': [],
        'image_url': image_url,
        'error': None,
        'method': 'simple_pyzbar'
    }

    try:
        # 尝试导入pyzbar
        try:
            from pyzbar import pyzbar
        except ImportError:
            result['error'] = '缺少pyzbar库，请安装: pip install pyzbar'
            return result

        # 下载图片
        pil_image = download_image_as_pil(image_url)
        if pil_image is None:
            result['error'] = '无法下载图片'
            return result

        # 转换为RGB模式（如果需要）
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')

        # 使用pyzbar直接处理PIL图像
        barcodes = pyzbar.decode(pil_image)

        if not barcodes:
            result['error'] = '图片中未检测到条形码'
            return result

        # 处理检测到的条形码
        detected_barcodes = []
        for barcode in barcodes:
            barcode_data = barcode.data.decode('utf-8')
            barcode_type = barcode.type

            detected_barcodes.append({
                'data': barcode_data,
                'type': barcode_type,
                'rect': barcode.rect._asdict() if barcode.rect else None
            })

        result.update({
            'success': True,
            'barcodes': detected_barcodes,
            'total_found': len(detected_barcodes)
        })

        return result

    except Exception as e:
        result['error'] = f'处理图片时出错: {str(e)}'
        return result


def get_barcode_from_image(image_url: str) -> str:
    """
    从图片中提取条形码的简化函数

    Args:
        image_url: 图片URL

    Returns:
        条形码字符串，失败时返回空字符串
    """
    try:
        # 首先尝试简化版方法
        result = extract_barcode_from_image_simple(image_url)

        if result['success'] and result['barcodes']:
            return result['barcodes'][0]['data']

        # 如果简化版失败，尝试OpenCV版本
        result = extract_barcode_with_llm(image_url)

        if result['success'] and result['barcodes']:
            return result['barcodes'][0]['data']

        # 最后尝试仅OpenCV检测（不解码）
        result = extract_barcode_opencv_only(image_url)

        if result['success'] and result['barcodes']:
            # 这种方法只能检测区域，不能读取数据
            return result['barcodes'][0]['data']

        return ''

    except Exception:
        return ''


def get_barcode_from_image_with_timing(image_url: str) -> Dict[str, Any]:
    """
    从图片中提取条形码，包含时间预估和实际耗时统计

    Args:
        image_url: 图片URL

    Returns:
        包含条形码数据和时间统计的字典
    """
    result = {
        'barcode_code': '',
        'success': False,
        'image_url': image_url,
        'timing': {
            'estimated': {},
            'actual': {},
            'comparison': {}
        },
        'error': None
    }

    try:
        # 1. 预估处理时间
        print("⏱️  正在预估处理时间...")
        estimation = estimate_processing_time(image_url)
        result['timing']['estimated'] = estimation

        print(f"📊 预估总时间: {estimation['estimated_total_time']:.1f}秒")
        print(f"   - 下载时间: {estimation['breakdown']['download_time']:.1f}秒")
        print(f"   - 处理时间: {estimation['breakdown']['image_processing_time']:.1f}秒")
        print(f"   - 检测时间: {estimation['breakdown']['barcode_detection_time']:.1f}秒")
        if estimation['factors']:
            print(f"🔍 影响因素: {', '.join(estimation['factors'])}")
        print()

        # 2. 开始实际处理
        start_time = time.time()
        print("🚀 开始处理图片...")

        # 下载阶段
        download_start = time.time()
        print("📥 正在下载图片...")

        # 首先尝试简化版方法
        simple_result = extract_barcode_from_image_simple(image_url)
        download_time = time.time() - download_start

        if simple_result['success'] and simple_result['barcodes']:
            total_time = time.time() - start_time
            result.update({
                'barcode_code': simple_result['barcodes'][0]['data'],
                'success': True,
                'method_used': 'simple_pyzbar'
            })
            print(f"✅ 成功识别条形码: {result['barcode_code']}")
        else:
            # 尝试OpenCV版本
            print("🔄 尝试OpenCV方法...")
            opencv_start = time.time()

            opencv_result = extract_barcode_with_llm(image_url)

            if opencv_result['success'] and opencv_result['barcodes']:
                total_time = time.time() - start_time
                result.update({
                    'barcode_code': opencv_result['barcodes'][0]['data'],
                    'success': True,
                    'method_used': 'opencv_pyzbar'
                })
                print(f"✅ 成功识别条形码: {result['barcode_code']}")
            else:
                # 最后尝试仅OpenCV检测
                print("🔄 尝试OpenCV区域检测...")
                region_result = extract_barcode_opencv_only(image_url)

                total_time = time.time() - start_time

                if region_result['success'] and region_result['barcodes']:
                    result.update({
                        'barcode_code': region_result['barcodes'][0]['data'],
                        'success': True,
                        'method_used': 'opencv_only',
                        'note': '仅检测到条形码区域，无法读取具体数据'
                    })
                    print(f"⚠️  检测到条形码区域，但无法解码具体数据")
                else:
                    result['error'] = '所有方法都未能识别出条形码'
                    print("❌ 未能识别出条形码")

        # 3. 记录实际时间
        total_time = time.time() - start_time
        result['timing']['actual'] = {
            'total_time': total_time,
            'download_time': download_time,
            'processing_time': total_time - download_time
        }

        # 4. 时间对比分析
        estimated_time = estimation['estimated_total_time']
        time_diff = total_time - estimated_time
        accuracy = (1 - abs(time_diff) / estimated_time) * 100

        result['timing']['comparison'] = {
            'time_difference': time_diff,
            'accuracy_percentage': accuracy,
            'faster_than_expected': time_diff < 0
        }

        # 输出时间统计
        print()
        print("⏱️  时间统计:")
        print(f"   📊 预估时间: {estimated_time:.2f}秒")
        print(f"   ⚡ 实际时间: {total_time:.2f}秒")
        print(f"   📈 时间差异: {time_diff:+.2f}秒")
        print(f"   🎯 预估准确度: {accuracy:.1f}%")

        if time_diff < 0:
            print(f"   🚀 比预期快了 {abs(time_diff):.2f}秒!")
        else:
            print(f"   🐌 比预期慢了 {time_diff:.2f}秒")

        return result

    except Exception as e:
        result['error'] = f'处理过程出错: {str(e)}'
        result['timing']['actual']['total_time'] = time.time() - start_time if 'start_time' in locals() else 0
        return result


def analyze_image_for_barcode(image_url: str) -> Dict[str, Any]:
    """
    分析图片中的条形码信息

    Args:
        image_url: 图片URL

    Returns:
        详细的分析结果
    """
    result = {
        'success': False,
        'image_url': image_url,
        'image_info': {},
        'barcode_results': [],
        'summary': {},
        'timing': {
            'total_time': 0.0,
            'method_times': {}
        },
        'error': None
    }
    
    try:
        start_time = time.time()

        # 获取图片基本信息
        pil_image = download_image_as_pil(image_url)
        if pil_image is None:
            result['error'] = '无法下载或打开图片'
            return result
        
        result['image_info'] = {
            'size': pil_image.size,
            'mode': pil_image.mode,
            'format': pil_image.format
        }
        
        # 尝试不同的条形码识别方法
        methods = [
            ('simple_pyzbar', extract_barcode_from_image_simple),
            ('opencv_pyzbar', extract_barcode_with_llm),
            ('opencv_only', extract_barcode_opencv_only)
        ]
        
        all_barcodes = []
        successful_methods = []
        
        for method_name, method_func in methods:
            try:
                method_start = time.time()
                method_result = method_func(image_url)
                method_time = time.time() - method_start

                result['barcode_results'].append(method_result)
                result['timing']['method_times'][method_name] = method_time

                if method_result['success']:
                    successful_methods.append(method_name)
                    all_barcodes.extend(method_result['barcodes'])
            except Exception as e:
                method_time = time.time() - method_start if 'method_start' in locals() else 0
                result['barcode_results'].append({
                    'method': method_name,
                    'success': False,
                    'error': str(e)
                })
                result['timing']['method_times'][method_name] = method_time
        
        # 汇总结果
        unique_barcodes = []
        seen_data = set()
        
        for barcode in all_barcodes:
            if barcode['data'] not in seen_data:
                unique_barcodes.append(barcode)
                seen_data.add(barcode['data'])
        
        result['summary'] = {
            'total_methods_tried': len(methods),
            'successful_methods': successful_methods,
            'total_barcodes_found': len(unique_barcodes),
            'unique_barcodes': [b['data'] for b in unique_barcodes]
        }
        
        if unique_barcodes:
            result['success'] = True
            result['primary_barcode'] = unique_barcodes[0]['data']
        else:
            result['error'] = '所有方法都未能检测到条形码'

        # 记录总时间
        result['timing']['total_time'] = time.time() - start_time

        return result
        
    except Exception as e:
        result['error'] = f'分析图片时出错: {str(e)}'
        return result


# ==================== 条码查询MCP服务接口 ====================

class BarcodeQueryConfig:
    """条码查询配置类"""

    def __init__(self):
        self.mcp_server_url = "http://mcpservergateway.market.alicloudapi.com/mcpnacos/cmapi011806/eyJhcHBDb2RlIjoiY2FhYThmMDE3ZDgxNGI2YzliM2Y2MTI5ZTFhMmE1NWYiLCJzIjoiQ2xvdWRfTWFya2V0In0="  # 已配置的URL
        self.tool_name = "条码查询接口"  # MCP工具名称
        self.timeout = 30
        self.max_retries = 3

    def set_server_url(self, url: str):
        """设置MCP服务器URL"""
        self.mcp_server_url = "http://mcpservergateway.market.alicloudapi.com/mcpnacos/cmapi011806/eyJhcHBDb2RlIjoiY2FhYThmMDE3ZDgxNGI2YzliM2Y2MTI5ZTFhMmE1NWYiLCJzIjoiQ2xvdWRfTWFya2V0In0="

    def is_configured(self) -> bool:
        """检查是否已配置"""
        return self.mcp_server_url is not None


# 全局配置实例
barcode_query_config = BarcodeQueryConfig()


def configure_barcode_query_service(server_url: str):
    """
    配置条码查询服务

    Args:
        server_url: MCP服务器URL

    Example:
        configure_barcode_query_service("http://your-mcp-server.com/api")
    """
    barcode_query_config.set_server_url(server_url)
    print(f"✅ 条码查询服务已配置: {server_url}")


def query_barcode_info(barcode: str) -> Dict[str, Any]:
    """
    查询条码信息（通过MCP服务）

    Args:
        barcode: 条形码字符串

    Returns:
        包含商品信息的字典
    """
    result = {
        'success': False,
        'barcode': barcode,
        'product_info': {},
        'raw_response': {},
        'error': None,
        'service': 'mcp_cmapi01'
    }

    try:
        # 检查配置
        if not barcode_query_config.is_configured():
            result['error'] = '条码查询服务未配置，请先调用 configure_barcode_query_service(url)'
            return result

        if not barcode or not barcode.strip():
            result['error'] = '条形码不能为空'
            return result

        # 构建请求参数（MCP tools/call格式）
        request_data = {
            "jsonrpc": "2.0",
            "method": "tools/call",
            "params": {
                "name": barcode_query_config.tool_name,
                "arguments": {
                    "barcode": barcode.strip()
                }
            },
            "id": 1
        }

        # 发送请求（MCP streamableHttp协议）
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/event-stream',
            'MCP-Protocol-Version': '2025-06-18',
            'User-Agent': 'BarcodeProcessor/1.0'
        }

        response = requests.post(
            barcode_query_config.mcp_server_url,
            json=request_data,
            headers=headers,
            timeout=barcode_query_config.timeout
        )

        response.raise_for_status()

        # 解析MCP streamableHttp响应
        try:
            response_data = response.json()
            result['raw_response'] = response_data

            # 检查JSON-RPC错误
            if 'error' in response_data:
                error_info = response_data['error']
                result['error'] = f"JSON-RPC错误 {error_info.get('code', 'N/A')}: {error_info.get('message', '未知错误')}"
                return result

            # 检查是否有result字段
            if 'result' not in response_data:
                result['error'] = 'MCP服务响应缺少result字段'
                return result

            # 获取MCP工具调用结果
            mcp_result = response_data['result']

            # 检查MCP工具调用是否出错
            if mcp_result.get('isError', False):
                result['error'] = f"MCP工具调用错误: {mcp_result.get('error', '未知错误')}"
                return result

            # 获取内容
            content = mcp_result.get('content', [])
            if not content or not isinstance(content, list):
                result['error'] = 'MCP工具响应内容为空或格式错误'
                return result

            # 获取第一个内容项的文本
            first_content = content[0]
            if first_content.get('type') != 'text':
                result['error'] = 'MCP工具响应内容类型不是文本'
                return result

            # 解析JSON文本
            api_response_text = first_content.get('text', '')
            if not api_response_text:
                result['error'] = 'MCP工具响应文本为空'
                return result

            # 提取JSON部分（去掉参数说明）
            json_end = api_response_text.find('  - 以下是返回参数说明')
            if json_end != -1:
                api_response_text = api_response_text[:json_end].strip()

            try:
                api_data = json.loads(api_response_text)
            except json.JSONDecodeError as e:
                result['error'] = f'解析API响应JSON失败: {e}'
                return result

        except json.JSONDecodeError:
            # 非JSON响应，可能是SSE流
            result['raw_response'] = {'text': response.text, 'content_type': response.headers.get('content-type')}
            result['error'] = f'MCP服务返回非JSON响应，Content-Type: {response.headers.get("content-type")}'
            return result

        # 检查API响应状态
        if api_data.get('status') != 0:
            result['error'] = f"API返回错误: {api_data.get('msg', '未知错误')}"
            return result

        # 提取商品信息
        product_data = api_data.get('result', {})
        if not product_data:
            result['error'] = 'API返回的商品信息为空'
            return result

        # 格式化商品信息
        result['product_info'] = {
            'barcode': product_data.get('barcode', barcode),
            'name': product_data.get('name', ''),
            'english_name': product_data.get('ename', ''),
            'brand': product_data.get('brand', ''),
            'type': product_data.get('type', ''),
            'origin_country': product_data.get('origincountry', ''),
            'origin_place': product_data.get('originplace', ''),
            'assembly_country': product_data.get('assemblycountry', ''),
            'company': product_data.get('company', ''),
            'net_content': product_data.get('netcontent', ''),
            'net_weight': product_data.get('netweight', ''),
            'gross_weight': product_data.get('grossweight', ''),
            'dimensions': {
                'width': product_data.get('width', ''),
                'height': product_data.get('height', ''),
                'depth': product_data.get('depth', '')
            },
            'package_type': product_data.get('packagetype', ''),
            'keyword': product_data.get('keyword', ''),
            'description': product_data.get('description', ''),
            'picture_url': product_data.get('pic', ''),
            'price': product_data.get('price', ''),
            'license_num': product_data.get('licensenum', ''),
            'health_permit_num': product_data.get('healthpermitnum', ''),
            'expiration_date': product_data.get('expirationdate', ''),
            'unspsc': product_data.get('unspsc', ''),
            'barcode_type': product_data.get('barcodetype', ''),
            'is_basic_unit': product_data.get('isbasicunit', ''),
            'catena': product_data.get('catena', '')
        }

        result['success'] = True
        return result

    except requests.exceptions.Timeout:
        result['error'] = f'请求超时（{barcode_query_config.timeout}秒）'
        return result
    except requests.exceptions.ConnectionError:
        result['error'] = '无法连接到MCP服务器'
        return result
    except requests.exceptions.HTTPError as e:
        result['error'] = f'HTTP错误: {e}'
        return result
    except json.JSONDecodeError as e:
        result['error'] = f'JSON解析错误: {e}'
        return result
    except Exception as e:
        result['error'] = f'查询条码信息时出错: {str(e)}'
        return result


def get_barcode_from_image_and_query_info(image_url: str) -> Dict[str, Any]:
    """
    从图片中识别条形码并查询商品信息的一体化函数

    Args:
        image_url: 图片URL

    Returns:
        包含条形码和商品信息的完整结果
    """
    result = {
        'success': False,
        'image_url': image_url,
        'barcode_recognition': {},
        'product_query': {},
        'final_result': {
            'barcode': '',
            'product_info': {}
        },
        'error': None
    }

    try:
        print("🔍 步骤1: 从图片识别条形码...")

        # 1. 识别条形码
        barcode_code = get_barcode_from_image(image_url)

        result['barcode_recognition'] = {
            'success': bool(barcode_code),
            'barcode': barcode_code,
            'method': 'image_recognition'
        }

        if not barcode_code:
            result['error'] = '未能从图片中识别出条形码'
            return result

        print(f"✅ 识别到条形码: {barcode_code}")

        # 2. 查询商品信息
        print("🔍 步骤2: 查询商品信息...")

        query_result = query_barcode_info(barcode_code)
        result['product_query'] = query_result

        if not query_result['success']:
            result['error'] = f"条形码识别成功，但商品信息查询失败: {query_result.get('error', '未知错误')}"
            result['final_result']['barcode'] = barcode_code  # 至少返回条形码
            return result

        print(f"✅ 查询到商品: {query_result['product_info'].get('name', '未知商品')}")

        # 3. 合并结果
        result.update({
            'success': True,
            'final_result': {
                'barcode': barcode_code,
                'product_info': query_result['product_info']
            }
        })

        return result

    except Exception as e:
        result['error'] = f'一体化处理过程出错: {str(e)}'
        return result


def get_product_description_from_image(image_url: str, format_style: str = "detailed") -> str:
    """
    从图片URL生成完整的产品文本描述

    Args:
        image_url: 图片URL
        format_style: 格式样式 ("detailed", "simple", "structured")

    Returns:
        产品文本描述字符串
    """
    try:
        print(f"🔍 正在处理图片: {image_url}")

        # 1. 从图片识别条形码并查询商品信息
        result = get_barcode_from_image_and_query_info(image_url)

        if not result['success']:
            return f"❌ 处理失败: {result.get('error', '未知错误')}"

        barcode = result['final_result']['barcode']
        product = result['final_result']['product_info']

        print(f"✅ 识别成功: {barcode}")

        # 2. 根据格式样式生成描述
        if format_style == "simple":
            return _format_simple_description(barcode, product)
        elif format_style == "structured":
            return _format_structured_description(barcode, product)
        else:  # detailed
            return _format_detailed_description(barcode, product)

    except Exception as e:
        return f"❌ 生成产品描述时出错: {str(e)}"


def _format_detailed_description(barcode: str, product: Dict[str, Any]) -> str:
    """格式化详细描述"""

    description_parts = []

    # 标题
    description_parts.append("📦 商品详细信息")
    description_parts.append("=" * 40)

    # 基本信息
    if product.get('name'):
        description_parts.append(f"🏷️  商品名称：{product['name']}")

    if product.get('brand'):
        description_parts.append(f"🏢 品牌：{product['brand']}")

    if product.get('type'):
        description_parts.append(f"📏 规格：{product['type']}")

    # 生产信息
    if product.get('company'):
        description_parts.append(f"🏭 生产企业：{product['company']}")

    if product.get('origin_country'):
        description_parts.append(f"🌍 原产国：{product['origin_country']}")

    if product.get('origin_place'):
        description_parts.append(f"📍 产地：{product['origin_place']}")

    # 产品规格
    if product.get('net_content'):
        description_parts.append(f"⚖️  净含量：{product['net_content']}")

    if product.get('net_weight'):
        description_parts.append(f"🏋️  净重：{product['net_weight']}")

    # 包装信息
    dimensions = product.get('dimensions', {})
    if any(dimensions.values()):
        size_info = []
        if dimensions.get('width'): size_info.append(f"宽{dimensions['width']}")
        if dimensions.get('height'): size_info.append(f"高{dimensions['height']}")
        if dimensions.get('depth'): size_info.append(f"深{dimensions['depth']}")
        if size_info:
            description_parts.append(f"📐 尺寸：{' × '.join(size_info)}")

    # 其他信息
    if product.get('keyword'):
        description_parts.append(f"🔍 关键词：{product['keyword']}")

    if product.get('description'):
        description_parts.append(f"📝 描述：{product['description']}")

    # 条形码信息
    description_parts.append("")
    description_parts.append("📊 条形码信息")
    description_parts.append("-" * 20)
    description_parts.append(f"🔢 条形码：{barcode}")

    if product.get('barcode_type'):
        description_parts.append(f"📋 类型：{product['barcode_type']}")

    # 图片链接
    if product.get('picture_url'):
        description_parts.append("")
        description_parts.append(f"🖼️  商品图片：{product['picture_url']}")

    # 证书信息
    certificates = []
    if product.get('license_num'):
        certificates.append(f"生产许可证：{product['license_num']}")
    if product.get('health_permit_num'):
        certificates.append(f"卫生许可证：{product['health_permit_num']}")

    if certificates:
        description_parts.append("")
        description_parts.append("📜 证书信息")
        description_parts.append("-" * 20)
        description_parts.extend(certificates)

    return "\n".join(description_parts)


def _format_simple_description(barcode: str, product: Dict[str, Any]) -> str:
    """格式化简单描述"""

    parts = []

    if product.get('name'):
        parts.append(f"商品：{product['name']}")

    if product.get('brand'):
        parts.append(f"品牌：{product['brand']}")

    if product.get('type'):
        parts.append(f"规格：{product['type']}")

    if product.get('company'):
        parts.append(f"厂商：{product['company']}")

    parts.append(f"条形码：{barcode}")

    return " | ".join(parts)


def _format_structured_description(barcode: str, product: Dict[str, Any]) -> str:
    """格式化结构化描述"""

    # 按照您提供的示例格式
    description_parts = []

    description_parts.append(f"根据查询结果，条形码 {barcode} 对应的商品信息和图片链接；")
    description_parts.append("")
    description_parts.append("商品信息如下：")
    description_parts.append("")

    if product.get('name'):
        description_parts.append(f"商品名称：{product['name']}")

    if product.get('brand'):
        description_parts.append(f"品牌：{product['brand']}")

    if product.get('type'):
        description_parts.append(f"净含量：{product['type']}")

    if product.get('origin_country'):
        description_parts.append(f"原产国：{product['origin_country']}")

    if product.get('company'):
        description_parts.append(f"生产企业：{product['company']}")

    # 根据产品信息推断商品类别
    category = "商品"
    if product.get('name'):
        name_lower = product['name'].lower()
        if any(word in name_lower for word in ['茶', '饮品', '饮料']):
            category = "茶饮品"
        elif any(word in name_lower for word in ['咖啡']):
            category = "咖啡饮品"
        elif any(word in name_lower for word in ['牛奶', '奶制品']):
            category = "乳制品"
        elif any(word in name_lower for word in ['零食', '饼干', '糖果']):
            category = "休闲食品"

    description_parts.append(f"商品类别：{category}")

    if product.get('picture_url'):
        description_parts.append("商品图片：点击查看图片")

    if product.get('keyword'):
        description_parts.append(f"关键词：{product['keyword']}")

    description_parts.append("")

    return "\n".join(description_parts)


def get_product_summary_from_image(image_url: str) -> Dict[str, Any]:
    """
    从图片URL获取产品摘要信息

    Args:
        image_url: 图片URL

    Returns:
        包含产品摘要的字典
    """
    try:
        result = get_barcode_from_image_and_query_info(image_url)

        if not result['success']:
            return {
                'success': False,
                'error': result.get('error', '未知错误')
            }

        barcode = result['final_result']['barcode']
        product = result['final_result']['product_info']

        # 生成摘要
        summary = {
            'success': True,
            'barcode': barcode,
            'name': product.get('name', ''),
            'brand': product.get('brand', ''),
            'specification': product.get('type', ''),
            'origin': product.get('origin_country', ''),
            'manufacturer': product.get('company', ''),
            'image_url': product.get('picture_url', ''),
            'keywords': product.get('keyword', ''),
            'category': _infer_category(product.get('name', '')),
            'descriptions': {
                'simple': _format_simple_description(barcode, product),
                'detailed': _format_detailed_description(barcode, product),
                'structured': _format_structured_description(barcode, product)
            }
        }

        return summary

    except Exception as e:
        return {
            'success': False,
            'error': f'获取产品摘要时出错: {str(e)}'
        }


def _infer_category(product_name: str) -> str:
    """根据产品名称推断商品类别"""
    if not product_name:
        return "商品"

    name_lower = product_name.lower()

    # 饮品类
    if any(word in name_lower for word in ['茶', '饮品', '饮料', '汽水', '果汁']):
        return "茶饮品" if '茶' in name_lower else "饮品"

    # 咖啡类
    if any(word in name_lower for word in ['咖啡', 'coffee']):
        return "咖啡饮品"

    # 乳制品
    if any(word in name_lower for word in ['牛奶', '奶制品', '酸奶', '奶粉']):
        return "乳制品"

    # 休闲食品
    if any(word in name_lower for word in ['零食', '饼干', '糖果', '巧克力', '薯片']):
        return "休闲食品"

    # 调味品
    if any(word in name_lower for word in ['酱油', '醋', '盐', '糖', '调料']):
        return "调味品"

    # 粮油
    if any(word in name_lower for word in ['米', '面', '油', '面粉']):
        return "粮油食品"

    return "食品"


# 示例和测试代码
if __name__ == "__main__":
    # 测试图片URL
    test_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    print("=== 图片条形码识别测试 ===\n")
    print(f"测试图片: {test_url}")
    print()
    
    # 测试简化函数
    print("1. 简化函数测试:")
    barcode = get_barcode_from_image(test_url)
    print(f"提取的条形码: {barcode}")
    print()
    
    # 测试详细分析
    print("2. 详细分析测试:")
    analysis = analyze_image_for_barcode(test_url)
    print(json.dumps(analysis, indent=2, ensure_ascii=False))
