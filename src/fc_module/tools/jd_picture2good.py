import json
import requests
import uuid
import numpy as np 
import time
"""
把接口返回的 data 字段转换成正常的中文商品列表。

示例输入（截取）：
response = {
    'status': True,
    'code': 0,
    'message': 'success',
    'data': '[{"name":"& 乐\\u200b\\u200b\\u200b缔\\u200b\\u200b\\u200b1\\u200b\\u200b\\u200b8\\u200b\\u200b\\u200b0\\u200b\\u200b\\u200b件\\u200b\\u200b\\u200b套\\u200b\\u200b\\u200b木\\u200b\\u200b\\u200b盒\\u200b\\u200b\\u200b画\\u200b\\u200b\\u200b笔\\u200b\\u200b\\u200b套\\u200b\\u200b\\u200b装\\u200b\\u200b\\u200b","price":"¥62.6","price_plus":"已补¥7.3"}, ...]',
    'context': 'uid=1956342083107160064&conversation_id=1111111&...'
}
"""

import json
import re

def clean_zero_width(text: str) -> str:
    """删除所有零宽空格（U+200B）以及多余的空格"""
    return text.replace("\u200b", "").strip()

def parse_response(response: dict) -> list:
    """
    把 `response['data']` 解析成普通的列表，每个元素都是
    {'name': '商品名称', 'price': '¥xx.xx', 'price_plus': '已补¥x.xx' (可选)}
    """
    # 第一步：取出 data 字段，它本身是一个 JSON 串
    raw_data = response.get("data")
    if not raw_data:
        return []

    # 有时候后端会把列表包装成字符串，还会出现 {}、[]、空对象等，需要先确保是合法 JSON
    # 若字符串两端多了单引号或多余的转义，需要先对它做一次 json.loads
    try:
        items = json.loads(raw_data)
    except json.JSONDecodeError as e:
        # 如果直接解析失败，尝试先去掉外层的引号再解析
        # 例如: "'[{\"a\":1}]'" -> "[{\"a\":1}]"
        cleaned = raw_data.strip("'\"")
        items = json.loads(cleaned)

    # 第二步：遍历每个商品，清理名称中的零宽空格
    cleaned_items = []
    for item in items:
        # 有可能出现空 dict {}，直接跳过
        if not isinstance(item, dict) or not item:
            continue

        name = clean_zero_width(item.get("name", ""))
        price = item.get("price", "")
        price_plus = item.get("price_plus", "")

        # 可选：把价格前面的 "¥" 去掉，只保留数字（如果你想保留请注释掉下面两行）
        # price = price.lstrip("¥")
        # price_plus = price_plus.lstrip("已补¥")

        cleaned_items.append({
            "name": name,
            "price": price,
            "price_plus": price_plus,
        })

    return cleaned_items



def jd_picture2good(
    scriptParams,
    subInteractiveName,
    req_id="717fb680-b3dc-42a8-a15a-fc8926984d61",
    uid="1956342083107160064",
    scriptId="1000012",
    conversation_id=None
):
    """
    将图片转换为商品信息的函数
    
    Args:
        scriptParams (dict): 脚本参数，包含图片URL等信息，必填
        subInteractiveName (str): 子交互名称，必填
        req_id (str): 请求ID，默认值为"717fb680-b3dc-42a8-a15a-fc8926984d61"
        uid (str): 用户ID，默认值为"1956342083107160064"
        scriptId (str): 脚本ID，默认值为"1000012"
        
    Returns:
        dict: API响应结果
    """
    # ------------------------------------------------------------
    # 1️⃣ 端点 URL
    # ------------------------------------------------------------
    url = "http://test-api-internal.guangfan-ai.online/agent-api/agent_interactive/run"

    # ------------------------------------------------------------
    # 2️⃣ 需要的请求头
    # ------------------------------------------------------------
    headers = {
        "appkey": "UMhmghBLK77BJyf5",
        "Authorization": (
            "eyJhbGciOiJIUzM4NCJ9."
            "eyJ1aWQiOjE5NTYzNDIwODMxMDcxNjAwNjQsImF1aCI6IlJPTEVfR0VFSyIs"
            "ImRpZCI6IjUwRjIxNEM1LTk2NEUtNDEzMC04NjUyLTJEOUZFMkRBNzM4MSIs"
            "ImV4cCI6MTc1OTE0NjYwMH0."
            "B6CAOXomfqpA_KLwHyBc-83sRaXbJrDVAhnNrbbuy46aLkLpkL4_0G1eNUrFsLru"
        ),
        "Content-Type": "application/json",
    }

    # ------------------------------------------------------------
    # 3️⃣ 请求体（payload）
    # ------------------------------------------------------------
    if not conversation_id:
        conversation_id = "1111111" + str(np.random.randint(1000000, 9999999))

    # 构建context字符串 - 注意这里需要是字符串格式的JSON
    context_dict = {
        "req_id": req_id,
        "uid": uid,
        "intent_id": "10002",
        "conversation_id": conversation_id,
        "coords": "1, 1",
        "app_id": "com.jingdong.app.mall",
        "ts": str(int(time.time() * 1000))  # 使用毫秒时间戳
    }

    payload = {
        "context": json.dumps(context_dict, ensure_ascii=False),
        "scriptId": scriptId,
        "subInteractiveName": subInteractiveName,
        "scriptParams": json.dumps(scriptParams, ensure_ascii=False)
    }

    # ------------------------------------------------------------
    # 4️⃣ 发送请求
    # ------------------------------------------------------------
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=120)
            response.raise_for_status()
            data = response.json()
            return data
        except requests.exceptions.RequestException as err:
            print(f"⚠️ 请求失败 (尝试 {attempt + 1}/{max_retries}):", err)
            if attempt == max_retries - 1:
                print("请求详情:", {
                    "url": url,
                    "headers": {k: "..." if k == "Authorization" else v for k, v in headers.items()},
                    "payload": payload
                })
                return {"error": str(err), "details": "请检查API服务状态和认证信息"}
            time.sleep(1)  # 等待1秒后重试

def jd_get_product_url(
    imageUrl,
    req_id="717fb680-b3dc-42a8-a15a-fc8926984d61",
    uid="1956342083107160064",
    scriptId="1000012"
):
    conv_id = str(uuid.uuid4())
    uuidNew = str(uuid.uuid4()) +".jpg"# 生成一个随机的 UUID
    scriptParams = {
        "imageUrl": imageUrl,
        "imageName": uuidNew
    }
    result_info = jd_picture2good(
        scriptParams=scriptParams,
        subInteractiveName="search_goods_by_photo",
        req_id=req_id, uid=uid, scriptId=scriptId,conversation_id=conv_id)
    result_url = jd_picture2good(
        scriptParams={"index":1},
        subInteractiveName="search_goods_by_photo",
        req_id=req_id, uid=uid, scriptId=scriptId,conversation_id=conv_id)
    return parse_response(result_info),result_url

def parse_response_json2markdown(response: dict) -> str:
    """
    将API返回的JSON数据转换为markdown格式，过滤掉"京言AI助手（测试版）"内容
    
    Args:
        response (dict): API返回的JSON数据，包含data字段
        
    Returns:
        str: 转换后的markdown格式文本
    """
    def process_node(node):
        if isinstance(node, dict):
            text = node.get('text', '')
            if text == "京言AI助手（测试版）":
                return ''
            
            children = node.get('children', [])
            processed_children = [process_node(child) for child in children]
            processed_children = [c for c in processed_children if c]
            
            if text and processed_children:
                return f"{text}\n\n" + "\n".join(processed_children)
            elif text:
                return text
            elif processed_children:
                return "\n".join(processed_children)
            return ''
        return str(node) if node else ''

    try:
        data = json.loads(response.get('data', '[]'))
        if not data:
            return ''
        
        markdown_content = []
        for item in data:
            if isinstance(item, dict) and 'ans' in item:
                ans_data = json.loads(item['ans'])
                markdown_content.append(process_node(ans_data))
        
        return "\n\n".join([c for c in markdown_content if c])
    except json.JSONDecodeError:
        return ''

def jd_picture2good_new(
    imageUrl,
    req_id="717fb680-b3dc-42a8-a15a-fc8926984d61",
    uid="1956342083107160064",
    scriptId="1000012"
):
    uuidNew = str(uuid.uuid4()) +".jpg"# 生成一个随机的 UUID
    
    scriptParams = {
        "imageUrl": imageUrl,
        "imageName": uuidNew
    }
    result_info = jd_picture2good(
        scriptParams=scriptParams,
        subInteractiveName="search_goods_by_photo",
        req_id=req_id, uid=uid, scriptId=scriptId)
    # print(type(parse_response(result_info)))
    # print(parse_response(result_info))
    return parse_response(result_info)
    
def jd_chatbot(
    search_text,
    req_id="717fb680-b3dc-42a8-a15a-fc8926984d61",
    uid="1956342083107160064",
    scriptId="1000012"
):
    """
    京东聊天机器人函数，根据文本搜索商品信息

    Args:
        search_text (str): 搜索文本，必填
        req_id (str): 请求ID，默认值为"717fb680-b3dc-42a8-a15a-fc8926984d61"
        uid (str): 用户ID，默认值为"1956342083107160064"
        scriptId (str): 脚本ID，默认值为"1000012"

    Returns:
        str: 转换后的markdown格式文本
    """
    # 生成随机对话ID
    conversation_id = str(int(time.time() * 1000))  # 使用时间戳作为对话ID

    # 构建脚本参数
    scriptParams = {"search_text": search_text}

    # 调用API获取结果
    result = jd_picture2good(
        scriptParams=scriptParams,
        subInteractiveName="query_goods_by_text",
        req_id=req_id,
        uid=uid,
        scriptId=scriptId,
        conversation_id=conversation_id
    )

    # 将结果转换为markdown格式并返回
    return parse_response_json2markdown(result)


# 示例调用（保留用于测试）
if __name__ == "__main__":
    # 示例参数
    # scriptParams = {
    #     "imageUrl": "https://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg",
    #     "imageName": "jd-e295b8c3-4d7a-4f01-9b2a-7c1d4e8f6a9b.jpg"
    # }
    
    # # 调用函数
    # result = jd_picture2good(
    #     scriptParams=scriptParams,
    #     subInteractiveName="search_goods_by_photo"
    # )
    # test = {"status":true,"code":0,"message":"success","data":"[{\"ans\":\"{\\\"text\\\":\\\"京言AI助手（测试版）\\\",\\\"children\\\":[{\\\"children\\\":[{\\\"text\\\":\\\"给我推荐一个鼠标要大品牌700元以下\\\"},{\\\"children\\\":[{\\\"children\\\":[{\\\"text\\\":\\\"查看分析推导过程 \\\"},{\\\"children\\\":[{\\\"text\\\":\\\"\uD83D\uDED2 这里为您精选了几款700元以内、来自微软、罗技、雷蛇等大品牌的优质鼠标，兼顾性能与性价比：\\\"},{\\\"children\\\":[{\\\"text\\\":\\\"微软简约鼠标\\\"},{\\\"text\\\":\\\"：经典品牌品质保障，有线稳定连接，适合日常办公与基础游戏需求\\\"}]}]},{\\\"children\\\":[{\\\"text\\\":\\\"微软 (Microsoft) 简约精准有线鼠标 自定义按键 3屏无缝切换\\\"},{\\\"text\\\":\\\"600+人种草\\\"},{\\\"text\\\":\\\"500+评价总结\\\"},{\\\"text\\\":\\\"￥\\\"},{\\\"text\\\":\\\"149\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"罗技M650\\\"},{\\\"text\\\":\\\"：双模连接+静音设计，人体工学造型提升办公舒适度\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"罗技（Logitech）优选系列M650无线蓝牙鼠标 办公蓝牙静音鼠标Mac苹果ipad鼠标人体工学双模鼠标大小手可选男女通用 M650中小手 黑M（M750入门款）\\\"},{\\\"text\\\":\\\"30天种草飙升5倍\\\"},{\\\"text\\\":\\\"领券满59享9折\\\"},{\\\"text\\\":\\\"20万+评价总结\\\"},{\\\"text\\\":\\\"￥\\\"},{\\\"text\\\":\\\"188.1\\\"},{\\\"text\\\":\\\"到手价\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"雷蛇V3 X\\\"},{\\\"text\\\":\\\"：2万DPI+RGB灯效，电竞级性能满足FPS\\\\/MOBA游戏需求\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"雷蛇（Razer）巴塞利斯蛇V3 X极速版 无线鼠标 小巴蛇 RGB幻彩灯效 吃鸡\\\\/LOL\\\\/CS GO电竞游戏鼠标\\\"},{\\\"text\\\":\\\"30天种草飙升5倍\\\"},{\\\"text\\\":\\\"10万+评价总结\\\"},{\\\"text\\\":\\\"￥\\\"},{\\\"text\\\":\\\"329\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"罗技G304\\\"},{\\\"text\\\":\\\"：轻量化设计+宏功能，兼顾游戏与办公场景\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"罗技（G）304 LIGHTSPEED无线鼠标 游戏鼠标 轻质便携 鼠标宏 绝地求生FPS英雄联盟吃鸡 生日礼物 黑色\\\"},{\\\"text\\\":\\\"30天种草飙升5倍\\\"},{\\\"text\\\":\\\"500万+评价总结\\\"},{\\\"text\\\":\\\"￥\\\"},{\\\"text\\\":\\\"199\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"罗技M720\\\"},{\\\"text\\\":\\\"：三设备切换+优联技术，适合多屏办公与移动办公场景\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"罗技（Logitech）M720无线双模蓝牙鼠标 家用办公Mac笔记本ipad多设备自定义按键带优联接收器10米无线便携右手商用 M720黑色\\\"},{\\\"text\\\":\\\"1万+人种草\\\"},{\\\"children\\\":[{\\\"text\\\":\\\"领券满59享9折\\\"},{\\\"text\\\":\\\"每209减33\\\"}]},{\\\"text\\\":\\\"2000+评价总结\\\"},{\\\"text\\\":\\\"￥\\\"},{\\\"text\\\":\\\"155.1\\\"},{\\\"text\\\":\\\"到手价\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"\uD83E\uDD14 您更倾向哪种使用场景？\\\"},{\\\"text\\\":\\\"办公使用\\\"},{\\\"text\\\":\\\"，\\\"},{\\\"text\\\":\\\"游戏娱乐\\\"},{\\\"text\\\":\\\"，\\\"},{\\\"text\\\":\\\"便携使用\\\"},{\\\"text\\\":\\\"，\\\"},{\\\"text\\\":\\\"设计制图\\\"},{\\\"text\\\":\\\"，\\\"},{\\\"text\\\":\\\"多功能使用\\\"}]},{\\\"children\\\":[{\\\"text\\\":\\\"全部200+商品\\\"},{\\\"text\\\":\\\"wvnfgAAAABJRU5ErkJggg==\\\"}]}]},{\\\"text\\\":\\\"GYR3GLfM9bLtOA92Ic8JPx07Xx62yCHy8jy19d0XzURYls9ZT6D8WaGTdYh6LBAAAAAElFTkSuQmCC\\\"},{\\\"text\\\":\\\"内容由AI生成\\\"}]},{\\\"text\\\":\\\"GsYsJ0AAAAASUVORK5CYII=\\\"}]}]}\"}]","context":"uid=1956342083107160064&conversation_id=1111111789966861&req_id=717fb680-b3dc-42a8-a15a-fc8926984d615&device_id=3b6d6191-748e-4b30-bcf3-9d8d860dcff8&seq_id=717fb680-b3dc-42a8-a15a-fc8926984d615&coords=1, 1&script_name=query_goods_by_text","extData":null}

    # print("✅ 请求成功，返回内容：")
    # print(json.dumps(result, indent=4, ensure_ascii=False))
    scriptParams  = {"search_text":"给我推荐一个手机，5000元以内"}
    result = jd_picture2good(
        scriptParams=scriptParams,
        subInteractiveName="query_goods_by_text"
    )
    print("result:",result)
    print("format_result:",parse_response_json2markdown(result))
