# -*- coding: utf-8 -*-
"""
商品比价工具模块
处理两张图片输入，输出商品价格范围和商品信息介绍
"""

import os
import time
from typing import Dict, List, Any, Tuple, Optional
import requests
from ..tools.batch_oss_search_excel import call_search_api
from ..tools.bijia import fanli_get, sousuo_get

# 图片搜索API配置
SEARCH_API_URL = "https://gw-openapi.zhidemai.com/img-search-service/v1/img_search_by_pic"
SEARCH_API_KEY = "25767dd78548ff5e590f4f02fa62ca00"

def analyze_image_quality(search_results: Dict[str, Any]) -> float:
    """
    分析图片搜索结果的质量评分
    
    Args:
        search_results: 图片搜索API返回的结果
        
    Returns:
        质量评分 (0-1)
    """
    if not search_results or 'data' not in search_results:
        return 0.0
    
    data = search_results['data']
    
    # 根据搜索结果的数量和质量进行评分
    score = 0.0
    
    # 如果data是列表，根据搜索结果数量和分数评分
    if isinstance(data, list) and data:
        score += min(len(data) / 10.0, 1.0) * 0.6  # 结果数量权重60%
        
        # 计算平均分数
        total_score = sum(item.get('score', 0) for item in data if isinstance(item, dict))
        avg_score = total_score / len(data) if len(data) > 0 else 0
        score += avg_score * 0.4  # 分数权重40%
    
    return min(score, 1.0)

def get_top_items(search_results: Dict[str, Any], top_n: int = 3) -> List[Dict[str, Any]]:
    """
    获取TOP-N的商品结果
    
    Args:
        search_results: 图片搜索API返回的结果
        top_n: 需要获取的前N个商品
        
    Returns:
        TOP-N商品列表
    """
    if not search_results or 'data' not in search_results:
        return []
    
    data = search_results['data']
    
    if not isinstance(data, list) or not data:
        return []
    
    # 按分数降序排序
    sorted_items = sorted(data, key=lambda x: x.get('score', 0), reverse=True)
    
    return sorted_items[:top_n]

def extract_item_info(item: Dict[str, Any]) -> Dict[str, Any]:
    """
    提取商品信息
    
    Args:
        item: 商品信息字典
        
    Returns:
        标准化的商品信息
    """
    return {
        'item_id': item.get('product_id', ''),
        'pro_id': item.get('pro_id', ''),
        'title': item.get('pro_name', ''),
        'score': item.get('score', 0),
        'brand': item.get('brand_name', ''),
        'category': item.get('last_cate_name', ''),
        'image_url': item.get('img_url', ''),
        'product_url': item.get('pro_url', '')
    }

def search_product_by_keyword(keyword: str) -> Optional[Dict[str, Any]]:
    """
    通过关键词搜索商品信息
    
    Args:
        keyword: 搜索关键词
        
    Returns:
        搜索结果或None
    """
    try:
        result = sousuo_get(keyword)
        if result and 'data' in result and result['data']:
            # 返回第一个搜索结果
            items = result['data'].get('list', [])
            if items:
                return items[0]
        return None
    except Exception as e:
        print(f"搜索商品时出错: {e}")
        return None

def get_price_info(store: str, item_id: str) -> Optional[List[Dict[str, Any]]]:
    """
    获取商品价格信息
    
    Args:
        store: 店铺平台 (如 'jd')
        item_id: 商品ID
        
    Returns:
        价格信息列表或None
    """
    try:
        result = fanli_get(store, item_id)
        if result and 'data' in result:
            data = result['data']
            # 返回list_2中的价格信息
            if 'list_2' in data and isinstance(data['list_2'], list):
                return data['list_2']
        return None
    except Exception as e:
        print(f"获取价格信息时出错: {e}")
        return None

def process_product_comparison(image_url1: str, image_url2: str) -> Dict[str, Any]:
    """
    处理两张图片的商品比价
    
    Args:
        image_url1: 第一张图片的URL
        image_url2: 第二张图片的URL
        
    Returns:
        比价结果，包含价格范围、商品信息和错误信息
    """
    result = {
        'success': False,
        'price_range': {},
        'product_info': {},
        'errors': [],
        'warnings': []
    }
    
    # 处理第一张图片
    try:
        success1, search_result1, raw_text1, err1 = call_search_api(
            api_url=SEARCH_API_URL,
            api_key=SEARCH_API_KEY,
            img_url=image_url1,
            is_auto_first_cate_name=0
        )
        
        if not success1:
            result['errors'].append(f"第一张图片搜索失败: {err1}")
            return result
            
        quality1 = analyze_image_quality(search_result1)
        top_items1 = get_top_items(search_result1, 3)
        
    except Exception as e:
        result['errors'].append(f"处理第一张图片时出错: {str(e)}")
        return result
    
    # 处理第二张图片
    try:
        success2, search_result2, raw_text2, err2 = call_search_api(
            api_url=SEARCH_API_URL,
            api_key=SEARCH_API_KEY,
            img_url=image_url2,
            is_auto_first_cate_name=0
        )
        
        if not success2:
            result['errors'].append(f"第二张图片搜索失败: {err2}")
            return result
            
        quality2 = analyze_image_quality(search_result2)
        top_items2 = get_top_items(search_result2, 3)
        
    except Exception as e:
        result['errors'].append(f"处理第二张图片时出错: {str(e)}")
        return result
    
    # 评估图片质量并选择最佳结果
    if quality1 > quality2 + 0.2:  # 第一张图片质量明显更好
        selected_items = top_items1
        result['warnings'].append("使用第一张图片的结果（质量更高）")
    elif quality2 > quality1 + 0.2:  # 第二张图片质量明显更好
        selected_items = top_items2
        result['warnings'].append("使用第二张图片的结果（质量更高）")
    else:  # 质量相近，尝试交叉验证
        selected_items = top_items1 + top_items2
        result['warnings'].append("两张图片质量相近，进行交叉验证")
    
    if not selected_items:
        result['errors'].append("未找到相关商品信息")
        return result
    
    # 处理商品信息
    product_infos = []
    prices = []
    
    for item in selected_items[:1]:
        item_info = extract_item_info(item)
        product_infos.append(item_info)
        
        # 优先使用商品标题进行搜索
        if item_info.get('title'):
            search_result = search_product_by_keyword(item_info['title'])
            if search_result:
                item_info['searched_item_id'] = search_result.get('item_id')
                item_info['searched_shop_id'] = search_result.get('shop_id')
                
                # 如果有搜索到的item_id，查询比价信息
                if search_result.get('item_id'):
                    price_list = get_price_info(search_result.get('platform', 'jd'), search_result['item_id'])
                    if price_list:
                        item_info['price_info'] = price_list
                        # 提取所有价格信息
                        for price_item in price_list:
                            if 'ds_price' in price_item and price_item['ds_price']:
                                try:
                                    prices.append(float(price_item['ds_price']))
                                except (ValueError, TypeError):
                                    pass
        # 如果标题搜索失败，再尝试使用item_id
        elif item_info['item_id']:
            price_list = get_price_info('jd', item_info['item_id'])
            if price_list:
                item_info['price_info'] = price_list
                # 提取所有价格信息
                for price_item in price_list:
                    if 'ds_price' in price_item and price_item['ds_price']:
                        try:
                            prices.append(float(price_item['ds_price']))
                        except (ValueError, TypeError):
                            pass
    
    # 计算价格范围
    if prices:
        result['price_range'] = {
            'min_price': min(prices),
            'max_price': max(prices),
            'avg_price': sum(prices) / len(prices),
            'price_count': len(prices)
        }
        result['success'] = True
    else:
        result['errors'].append("无法获取商品价格信息")
    
    result['product_info'] = {
        'items': product_infos,
        'image1_quality': quality1,
        'image2_quality': quality2,
        'total_items_found': len(product_infos)
    }
    
    return result

def get_product_price_by_keyword(keyword: str, limit: int = 5) -> Dict[str, Any]:
    """
    通过关键词获取商品价格范围和详细信息
    
    Args:
        keyword: 搜索关键词
        limit: 最大返回结果数量
        
    Returns:
        价格范围和商品信息
    """
    result = {
        'success': False,
        'price_range': {},
        'product_info': {},
        'errors': [],
        'warnings': []
    }
    
    if not keyword or not isinstance(keyword, str):
        result['errors'].append('请输入有效的搜索关键词')
        return result
    
    try:
        # 搜索商品
        search_result = search_product_by_keyword(keyword)
        if not search_result:
            result['errors'].append(f'未找到与"{keyword}"相关的商品')
            return result
        
        # 获取商品信息
        real_item_id = search_result.get('item_id')
        shop_id = search_result.get('shop_id')
        platform = search_result.get('platform', 'jd')
        
        # 获取价格信息 - 使用搜索结果的shop_id作为平台
        actual_platform = search_result.get('shop_id', platform)
        price_list = get_price_info(actual_platform, real_item_id)
        
        # 如果使用shop_id查询失败，再尝试使用默认平台
        if not price_list and actual_platform != platform:
            price_list = get_price_info(platform, real_item_id)
        
        product_infos = [{
            'title': search_result.get('item_title', keyword),
            'brand': '',
            'category': '',
            'score': 1.0,
            'item_id': real_item_id,
            'shop_id': shop_id,
            'platform': platform,
            'searched_keyword': keyword
        }]
        
        prices = []
        if price_list:
            product_infos[0]['price_info'] = price_list
            # 提取所有价格信息
            for price_item in price_list:
                if 'ds_price' in price_item and price_item['ds_price']:
                    try:
                        prices.append(float(price_item['ds_price']))
                    except (ValueError, TypeError):
                        pass
        
        # 计算价格范围
        if prices:
            result['price_range'] = {
                'min_price': min(prices),
                'max_price': max(prices),
                'avg_price': sum(prices) / len(prices),
                'price_count': len(prices)
            }
            result['success'] = True
        else:
            result['errors'].append("无法获取商品价格信息")
        
        result['product_info'] = {
            'items': product_infos,
            'total_items_found': len(product_infos),
            'search_keyword': keyword
        }
        
        return result
        
    except Exception as e:
        result['errors'].append(f"处理关键词搜索时出错: {str(e)}")
        return result

def get_product_price_range(image_urls: List[str]) -> Dict[str, Any]:
    """
    获取商品价格范围的主函数
    
    Args:
        image_urls: 图片URL列表（支持1-2张图片）
        
    Returns:
        价格范围和商品信息
    """
    if len(image_urls) < 1 or len(image_urls) > 2:
        return {
            'success': False,
            'errors': ['请提供1-2张图片URL'],
            'price_range': {},
            'product_info': {}
        }
    
    if len(image_urls) == 1:
        # 单张图片处理
        image_url = image_urls[0]
        try:
            success, search_result, raw_text, err = call_search_api(
                api_url=SEARCH_API_URL,
                api_key=SEARCH_API_KEY,
                img_url=image_url,
                is_auto_first_cate_name=0
            )
            
            if not success:
                return {
                    'success': False,
                    'errors': [f"图片搜索失败: {err}"],
                    'price_range': {},
                    'product_info': {}
                }
            
            top_items = get_top_items(search_result, 3)
            if not top_items:
                return {
                    'success': False,
                    'errors': ['未找到相关商品信息'],
                    'price_range': {},
                    'product_info': {}
                }
            
            # 处理商品信息
            product_infos = []
            prices = []
            
            for item in top_items:
                item_info = extract_item_info(item)
                product_infos.append(item_info)
                
                # 优先使用商品标题进行搜索
                if item_info.get('title'):
                    search_result = search_product_by_keyword(item_info['title'])
                    if search_result:
                        item_info['searched_item_id'] = search_result.get('item_id')
                        item_info['searched_shop_id'] = search_result.get('shop_id')
                        
                        # 如果有搜索到的item_id，查询比价信息
                        if search_result.get('item_id'):
                            price_list = get_price_info(search_result.get('platform', 'jd'), search_result['item_id'])
                            if price_list:
                                item_info['price_info'] = price_list
                                # 提取所有价格信息
                                for price_item in price_list:
                                    if 'ds_price' in price_item and price_item['ds_price']:
                                        try:
                                            prices.append(float(price_item['ds_price']))
                                        except (ValueError, TypeError):
                                            pass
                # 如果标题搜索失败，再尝试使用item_id
                elif item_info['item_id']:
                    price_list = get_price_info('jd', item_info['item_id'])
                    if price_list:
                        item_info['price_info'] = price_list
                        # 提取所有价格信息
                        for price_item in price_list:
                            if 'ds_price' in price_item and price_item['ds_price']:
                                try:
                                    prices.append(float(price_item['ds_price']))
                                except (ValueError, TypeError):
                                    pass
            
            # 计算价格范围
            result = {
                'success': bool(prices),
                'price_range': {},
                'product_info': {
                    'items': product_infos,
                    'image_quality': analyze_image_quality(search_result),
                    'total_items_found': len(product_infos)
                },
                'errors': [] if prices else ['无法获取商品价格信息'],
                'warnings': []
            }
            
            if prices:
                result['price_range'] = {
                    'min_price': min(prices),
                    'max_price': max(prices),
                    'avg_price': sum(prices) / len(prices),
                    'price_count': len(prices)
                }
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'errors': [f"处理图片时出错: {str(e)}"],
                'price_range': {},
                'product_info': {}
            }
    
    else:
        # 两张图片处理
        return process_product_comparison(image_urls[0], image_urls[1])
