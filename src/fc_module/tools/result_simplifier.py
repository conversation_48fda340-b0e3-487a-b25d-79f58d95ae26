# -*- coding: utf-8 -*-
"""
结果精简工具 - 将商品价格工具的结果转换为纯文本格式
"""

from typing import Dict, Any, List

def simplify_product_price_result(result: Dict[str, Any]) -> str:
    """
    精简商品价格结果，提取有效信息为纯文本
    
    Args:
        result: get_product_price_by_keyword 或 get_product_price_range 的返回结果
        
    Returns:
        精简后的纯文本结果
    """
    if not result:
        return "❌ 无结果数据"
    
    # 检查是否成功
    if not result.get('success', False):
        errors = result.get('errors', [])
        if errors:
            return f"❌ 搜索失败: {', '.join(errors)}"
        else:
            return "❌ 搜索失败，未知错误"
    
    # 提取价格范围信息
    price_range = result.get('price_range', {})
    min_price = price_range.get('min_price')
    max_price = price_range.get('max_price')
    avg_price = price_range.get('avg_price')
    price_count = price_range.get('price_count', 0)
    
    # 提取商品信息
    product_info = result.get('product_info', {})
    items = product_info.get('items', [])
    
    # 构建精简结果
    lines = []
    
    # 价格范围信息
    if min_price is not None and max_price is not None:
        lines.append(f"💰 价格范围: {min_price:.2f} - {max_price:.2f} 元")
        if avg_price is not None:
            lines.append(f"📊 平均价格: {avg_price:.2f} 元")
        lines.append(f"🔢 价格样本数: {price_count} 个")
    
    # 商品信息
    if items:
        lines.append("\n🛒 商品信息:")
        for i, item in enumerate(items, 1):
            title = item.get('title', '未知商品')
            brand = item.get('brand')
            category = item.get('category')
            
            item_line = f"{i}. {title}"
            if brand:
                item_line += f" | 品牌: {brand}"
            if category:
                item_line += f" | 分类: {category}"
            
            lines.append(item_line)
    
    # 警告信息
    warnings = result.get('warnings', [])
    if warnings:
        lines.append(f"\n⚠️  注意: {', '.join(warnings)}")
    
    # 搜索关键词（如果有）
    search_keyword = product_info.get('search_keyword')
    if search_keyword:
        lines.append(f"\n🔍 搜索关键词: {search_keyword}")
    
    return "\n".join(lines)

def simplify_keyword_search_result(result: Dict[str, Any]) -> str:
    """
    精简关键词搜索结果（get_product_price_by_keyword的专用版本）
    
    Args:
        result: get_product_price_by_keyword 的返回结果
        
    Returns:
        精简后的纯文本结果
    """
    return simplify_product_price_result(result)

def simplify_image_search_result(result: Dict[str, Any]) -> str:
    """
    精简图片搜索结果（get_product_price_range的专用版本）
    
    Args:
        result: get_product_price_range 的返回结果
        
    Returns:
        精简后的纯文本结果
    """
    return simplify_product_price_result(result)

# 测试函数
def test_simplifier():
    """测试精简功能"""
    # 模拟成功结果
    success_result = {
        'success': True,
        'price_range': {
            'min_price': 199.0,
            'max_price': 299.0,
            'avg_price': 249.5,
            'price_count': 4
        },
        'product_info': {
            'items': [
                {
                    'title': '苹果 iPhone 15 Pro Max',
                    'brand': 'Apple',
                    'category': '手机',
                    'item_id': '123456'
                }
            ],
            'search_keyword': '苹果手机',
            'total_items_found': 1
        },
        'errors': [],
        'warnings': ['使用第一张图片的结果（质量更高）']
    }
    
    # 模拟失败结果
    fail_result = {
        'success': False,
        'errors': ['未找到相关商品信息'],
        'price_range': {},
        'product_info': {}
    }
    
    print("=== 测试精简功能 ===")
    print("\n1. 成功结果:")
    print(simplify_product_price_result(success_result))
    
    print("\n2. 失败结果:")
    print(simplify_product_price_result(fail_result))

if __name__ == "__main__":
    test_simplifier()
