# -*- coding: utf-8 -*-
"""
测试API返回结果结构
用于验证fanli_get、sousuo_get和call_search_api的返回数据结构
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from src.fc_module.tools.batch_oss_search_excel import call_search_api
from src.fc_module.tools.bijia import fanli_get, sousuo_get

# 测试图片URL（需要替换为实际的测试图片URL）
TEST_IMAGE_URL = "http://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg"
SEARCH_API_URL = "https://gw-openapi.zhidemai.com/img-search-service/v1/img_search_by_pic"
SEARCH_API_KEY = "25767dd78548ff5e590f4f02fa62ca00"

def test_call_search_api():
    """测试图片搜索API的结构"""
    print("=== 测试 call_search_api ===")
    try:
        success, result, raw_text, err = call_search_api(
            api_url=SEARCH_API_URL,
            api_key=SEARCH_API_KEY,
            img_url=TEST_IMAGE_URL,
            is_auto_first_cate_name=0
        )
        
        print(f"Success: {success}")
        print(f"Error: {err}")
        print(f"Raw text length: {len(raw_text)}")
        
        if success and result:
            print("API返回结果结构:")
            print(f"结果类型: {type(result)}")
            print(f"包含的键: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
            
            if isinstance(result, dict):
                if 'data' in result:
                    print("data字段结构:")
                    data = result['data']
                    print(f"data类型: {type(data)}")
                    
                    if isinstance(data, list) and data:
                        print(f"data列表长度: {len(data)}")
                        if len(data) > 0:
                            print("第一个data元素的结构:")
                            first_data_item = data[0]
                            print(f"元素类型: {type(first_data_item)}")
                            if isinstance(first_data_item, dict):
                                print(f"元素包含的键: {list(first_data_item.keys())}")
                                for key, value in first_data_item.items():
                                    print(f"  {key}: {type(value)} - {value}")
                    
                    # 检查是否有goods_list
                    if isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                        first_item = data[0]
                        if 'goods_list' in first_item:
                            goods_list = first_item['goods_list']
                            print(f"goods_list类型: {type(goods_list)}")
                            print(f"goods_list长度: {len(goods_list) if isinstance(goods_list, list) else 'N/A'}")
                            
                            if isinstance(goods_list, list) and goods_list:
                                print("第一个商品的结构:")
                                first_good = goods_list[0]
                                print(f"商品类型: {type(first_good)}")
                                print(f"商品包含的键: {list(first_good.keys()) if isinstance(first_good, dict) else 'N/A'}")
                                if isinstance(first_good, dict):
                                    for key, value in first_good.items():
                                        print(f"  {key}: {type(value)} - {value}")
        
        print("\n" + "="*50 + "\n")
        
    except Exception as e:
        print(f"测试call_search_api时出错: {e}")

def test_fanli_get():
    """测试比价API的结构"""
    print("=== 测试 fanli_get ===")
    try:
        # 使用已知的测试商品ID
        test_item_id = "6vVtwT0MNfQBvpCeFERtsceg_sweEvrt2exzoXBKCA5"
        result = fanli_get("jd", test_item_id)
        
        print(f"fanli_get结果类型: {type(result)}")
        
        if result is not None:
            print(f"结果包含的键: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
            
            if isinstance(result, dict):
                if 'data' in result:
                    data = result['data']
                    print(f"data字段类型: {type(data)}")
                    print(f"data包含的键: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
                    
                    if isinstance(data, dict):
                        for key, value in data.items():
                            print(f"  {key}: {type(value)} - {value}")
                else:
                    print("完整的fanli_get结果:")
                    for key, value in result.items():
                        print(f"  {key}: {type(value)} - {value}")
        else:
            print("fanli_get返回None")
        
        print("\n" + "="*50 + "\n")
        
    except Exception as e:
        print(f"测试fanli_get时出错: {e}")

def test_sousuo_get():
    """测试搜索API的结构"""
    print("=== 测试 sousuo_get ===")
    try:
        # 测试搜索关键词
        test_keyword = "笔记本电脑"
        result = sousuo_get(test_keyword)
        
        print(f"sousuo_get结果类型: {type(result)}")
        
        if result is not None:
            print(f"结果包含的键: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
            
            if isinstance(result, dict):
                if 'data' in result:
                    data = result['data']
                    print(f"data字段类型: {type(data)}")
                    print(f"data包含的键: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
                    
                    if isinstance(data, dict) and 'items' in data:
                        items = data['items']
                        print(f"items类型: {type(items)}")
                        print(f"items长度: {len(items) if isinstance(items, list) else 'N/A'}")
                        
                        if isinstance(items, list) and items:
                            print("第一个搜索结果的结构:")
                            first_item = items[0]
                            print(f"商品类型: {type(first_item)}")
                            print(f"商品包含的键: {list(first_item.keys()) if isinstance(first_item, dict) else 'N/A'}")
                            if isinstance(first_item, dict):
                                for key, value in first_item.items():
                                    print(f"  {key}: {type(value)} - {value}")
                else:
                    print("完整的sousuo_get结果:")
                    for key, value in result.items():
                        print(f"  {key}: {type(value)} - {value}")
        else:
            print("sousuo_get返回None")
        
        print("\n" + "="*50 + "\n")
        
    except Exception as e:
        print(f"测试sousuo_get时出错: {e}")

if __name__ == "__main__":
    print("开始测试API返回结果结构...\n")
    
    test_call_search_api()
    test_fanli_get()
    test_sousuo_get()
    
    print("测试完成！")
