# -*- coding: utf-8 -*-
"""
测试关键词搜索工具 - 使用真实API调用验证功能
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from src.fc_module.tools.product_price_tools import get_product_price_by_keyword
from src.fc_module.tools.result_simplifier import simplify_keyword_search_result

def test_keyword_search_real():
    """测试真实的关键词搜索功能"""
    print("=== 关键词搜索工具真实API测试 ===\n")
    
    # 测试几个真实的关键词
    test_keywords = [
        "苹果手机",
        "笔记本电脑",
        "华为平板",
        "小米手环"
    ]
    
    for keyword in test_keywords:
        print(f"测试关键词: {keyword}")
        print("-" * 50)
        
        try:
            result = get_product_price_by_keyword(keyword)
            
            print(f"成功状态: {result['success']}")
            print(f"错误信息: {result['errors']}")
            print(f"警告信息: {result['warnings']}")
            
            if result['success']:
                # 显示精简结果
                simplified = simplify_keyword_search_result(result)
                print("\n精简结果:")
                print(simplified)
            else:
                print("❌ 搜索失败")
                print(f"错误信息: {result['errors']}")
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    test_keyword_search_real()
