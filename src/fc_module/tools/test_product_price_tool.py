# -*- coding: utf-8 -*-
"""
测试商品比价工具
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from src.fc_module.tools.product_price_tools import get_product_price_range, get_price_info
from src.fc_module.tools.result_simplifier import simplify_image_search_result

def test_product_price_tool():
    """测试商品比价工具"""
    print("=== 测试商品比价工具 ===")
    
    # 测试图片URL（需要替换为实际的测试图片URL）
    test_image_urls = [
        "http://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg"
    ]
    
    try:
        result = get_product_price_range(test_image_urls)
        
        print(f"测试结果: {result['success']}")
        print(f"错误信息: {result['errors']}")
        print(f"警告信息: {result['warnings']}")
        
        if result['success']:
            # 显示精简结果
            simplified = simplify_image_search_result(result)
            print("\n精简结果:")
            print(simplified)
        else:
            print("测试失败，请检查错误信息")
            print(f"错误信息: {result['errors']}")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def debug_search_results():
    """调试搜索结果"""
    print("=== 调试搜索结果 ===")
    
    # 测试几个商品标题
    test_titles = [
        "柯达（KODAK) USB3.0读卡器 多功能合一高速读卡器 支持SD/TF/Micro SD/CF存储卡等  T200A 黑色",
        "适用realme真我数据线65W闪充gt充电器neo 2t 3快充q3 q2 x7 x50 pro type-c超级闪充super vooc充电线1米",
        "双头usb3.0数据连接线公对公两头双公超长1米高速移动硬盘盒笔记本电 [usb2.0]1条装-经典黑 0.5米"
    ]
    
    for title in test_titles:
        print(f"\n调试商品标题: {title}")
        
        # 直接调用sousuo_get来查看完整响应
        from src.fc_module.tools.bijia import sousuo_get
        result = sousuo_get(title)
        
        if result:
            print(f"API响应状态: {result.get('status', 'N/A')}")
            print(f"API响应信息: {result.get('info', 'N/A')}")
            
            if 'data' in result:
                data = result['data']
                print(f"data字段包含的键: {list(data.keys())}")
                
                if 'list' in data:
                    item_list = data['list']
                    print(f"list类型: {type(item_list)}")
                    print(f"list长度: {len(item_list) if isinstance(item_list, list) else 'N/A'}")
                    
                    if isinstance(item_list, list) and item_list:
                        print(f"找到 {len(item_list)} 个搜索结果")
                        for i, search_item in enumerate(item_list[:2]):  # 只显示前2个
                            print(f"  结果 {i+1}:")
                            print(f"    商品ID: {search_item.get('item_id', 'N/A')}")
                            print(f"    店铺ID: {search_item.get('shop_id', 'N/A')}")
                            print(f"    标题: {search_item.get('item_title', 'N/A')}")
                            print(f"    平台: {search_item.get('platform', 'N/A')}")
                    else:
                        print("  list为空或不是列表")
                else:
                    print("  data中没有list字段")
            else:
                print("  响应中没有data字段")
        else:
            print("  API调用返回None")

if __name__ == "__main__":
    test_product_price_tool()
    print("\n" + "="*50 + "\n")
    debug_search_results()
