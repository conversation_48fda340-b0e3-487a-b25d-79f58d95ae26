# -*- coding: utf-8 -*-
"""
测试之前成功获取价格的商品ID
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

from src.fc_module.tools.bijia import fanli_get

def test_working_items():
    """测试之前成功获取价格的商品ID"""
    print("=== 测试之前成功获取价格的商品ID ===\n")
    
    # 之前图片搜索中成功获取价格的商品ID
    working_item_ids = [
        "iwUBd42pdSlAqSRt06QV2NbO_3df4wfBhtdt3QTPIYO",  # 读卡器
        "qu2iQQS2tratmptWaEhnERIt_3sdbusi0ltekSLoW2G"   # 数据线
    ]
    
    for item_id in working_item_ids:
        print(f"测试商品ID: {item_id}")
        print("-" * 50)
        
        try:
            result = fanli_get('jd', item_id)
            
            if result:
                print(f"API响应状态: {result.get('status', 'N/A')}")
                print(f"API响应信息: {result.get('info', 'N/A')}")
                
                if 'data' in result:
                    data = result['data']
                    print(f"data字段包含的键: {list(data.keys())}")
                    
                    # 检查所有可能包含价格信息的字段
                    for key in data:
                        if isinstance(data[key], list):
                            print(f"字段 {key}: 类型={type(data[key])}, 长度={len(data[key])}")
                            
                            if data[key]:
                                print(f"  前3个元素示例:")
                                for i, item in enumerate(data[key][:3]):
                                    if isinstance(item, dict):
                                        print(f"    元素 {i+1}:")
                                        for item_key, item_value in list(item.items())[:5]:  # 只显示前5个键值对
                                            print(f"      {item_key}: {item_value}")
                                    else:
                                        print(f"    元素 {i+1}: {item}")
                                print()
                        else:
                            print(f"字段 {key}: {data[key]}")
                            
                else:
                    print("  响应中没有data字段")
            else:
                print("  API调用返回None")
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    test_working_items()
