import os
import requests
import hashlib
import time
import uuid
import json


def generate_sign(params: dict, app_secret: str) -> str:
    """
    根据文档规则生成sign签名
    :param params: 请求参数（不含sign）
    :param app_secret: 接口秘钥
    :return: 32位大写MD5签名
    """
    # 1. 过滤空值参数（值为None或空字符串的参数不参与签名）
    filtered_params = {k: v for k, v in params.items() if v is not None and v != ""}
    
    # 2. 按参数名ASCII码从小到大排序（字典序）
    sorted_keys = sorted(filtered_params.keys())
    
    # 3. 拼接"参数名+参数值"字符串
    param_str = "".join([f"{key}{filtered_params[key]}" for key in sorted_keys])
    
    # 4. 前后加入app_secret
    sign_str = f"{app_secret}{param_str}{app_secret}"
    
    # 5. MD5加密并转大写
    md5 = hashlib.md5(sign_str.encode("utf-8"))
    return md5.hexdigest().upper()


def get_product_info(para: str, shop: str = None, sort: int = None) -> dict:
    """
    调用接口获取商品信息
    :param para: 商品标题（必填）
    :param shop: 平台标识（非必填，默认返回淘宝、京东、拼多多）
    :param sort: 排序方式（非必填，1-低价优先 2-高价优先 3-销量低到高 4-销量高到低）
    :return: 接口响应JSON
    """
    # 从环境变量读取配置（需提前设置APP_KEY和APP_SECRET）
    app_key = "HY9O13MN"
    # os.getenv("APP_KEY")
    app_secret = "451f5f41678aad6d90d0064e25d0a7ea"
    # os.getenv("APP_SECRET")
    
    if not app_key or not app_secret:
        raise EnvironmentError("请先设置环境变量APP_KEY和APP_SECRET！")
    
    # 生成请求必要参数
    timestamp = str(int(time.time()))  # 精确到秒的时间戳
    request_id = uuid.uuid4().hex      # 唯一请求ID（32位字符串）
    
    # 构造基础请求参数
    params = {
        "app_key": app_key,
        "timestamp": timestamp,
        "request_id": request_id,
        "para": para
    }
    
    # 添加可选参数
    if shop:
        params["shop"] = shop
    if sort:
        params["sort"] = sort
    
    # 生成签名
    sign = generate_sign(params, app_secret)
    request_body = {**params, "sign": sign}
    
    # 接口请求配置
    url = "https://fl.oozk.cn/api/gn/search"
    headers = {"Content-Type": "application/json"}
    
    try:
        # 发送POST请求（超时10秒）
        response = requests.post(
            url=url,
            json=request_body,
            headers=headers,
            timeout=10
        )
        response.raise_for_status()  # 检查HTTP错误（如404、500）
        return response.json()
    
    except requests.exceptions.Timeout:
        raise Exception("请求超时，请检查网络！")
    except requests.exceptions.HTTPError as e:
        raise Exception(f"HTTP错误：{e.response.status_code} - {e.response.text}")
    except Exception as e:
        raise Exception(f"请求失败：{str(e)}")


if __name__ == "__main__":
    # ---------------------- 测试参数 ----------------------
    # product_para = "移速 （MOVE SPEED）（3C认证）移速充电宝迷你便携10000毫安大容量双向快充移动电源淡"  # 商品标题
    # product_para = "金字塔原理实战篇2（新版）"
    # product_para = "精益数据分析"
    # product_para = "Azeada【120档小空调丨冰敷制冷】高速手持风扇折叠挂脖便携式户外迷你桌面usb充电静音长续航学生宿舍白"
    # product_para = "国内现货 LouisVuittonLv德训鞋 25年新款 sneakerina 休闲鞋"
    product_para = "Shokz韶音OpenRun Pro 2 S820骨传导蓝牙运动耳机跑步骑行开放式"
    # product_para = "优衣库白衣服"
    shop = "tb/jd/pdd"  # 可选：指定平台（淘宝/京东/拼多多）
    sort = 1            # 可选：1-价格由低到高
    
    # ---------------------- 执行请求 ----------------------
    try:
        import time 
        start_time = time.time()
        result = get_product_info(product_para, shop=shop, sort=sort)
        end_time = time.time()
        print("end_time-start_time:", end_time - start_time)
        print("\n请求成功！响应结果：")
        print(json.dumps(result, indent=2, ensure_ascii=False))  # 格式化输出中文
    except Exception as e:
        print(f"\n请求失败：{str(e)}")