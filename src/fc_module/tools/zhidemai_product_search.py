url = 'https://mcp-api.zhidemai.com/v1/agent/product_search'

import pandas as pd
import time
import requests
import json

def product_search(url, product_query):
    payload = {
        "query": product_query
    }
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer bd4183be-ea06-492b-8511-83f15c5f204b',
        'User-Agent': 'ProductSearchTester/1.0'
    }
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    if response.status_code == 200:
        result = response.json()
    return result

if __name__=="__main__":

    product_query = "iphone17"
    result = product_search(url, product_query)
    print(result)
    print(len(result["data"]))