# -*- coding: utf-8 -*-
"""
工具调用历史消息转换器
将包含工具调用的OpenAI历史上下文转换为多模态格式
"""

import json
from typing import List, Dict, Any

def transform_tool_history_to_multimodal(messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    将包含工具调用的OpenAI历史消息转换为多模态格式
    
    Args:
        messages: OpenAI格式的历史消息列表，可能包含工具调用
        
    Returns:
        转换为多模态格式的消息列表
    """
    multimodal_messages = []
    
    for message in messages:

        role = message.get("role")
        content = message.get("content")
        
        # print("role:",role)
        # print('hh:',(bool(role == "assistant" and message.get("tool_calls"))),"\n")
        # print("content:",content)
        # print("message:",message)
        # print("message.get(\"tool_calls\"):",message.get("tool_calls"))
        # 处理工具调用消息
        if role == "tool" and isinstance(content, str):
            # print("1")
            
            multimodal_messages.append(message)
            # try:
            #     # 解析工具调用结果
            #     tool_data = json.loads(content)
                
            #     # 转换为多模态格式
            #     multimodal_content = [{
            #         "type": "text",
            #         "text": f"工具调用结果: {json.dumps(tool_data, ensure_ascii=False, indent=2)}"
            #     }]
                
            #     multimodal_messages.append({
            #         "role": role,
            #         "content": multimodal_content
            #     })
                
            # except json.JSONDecodeError:
            #     # 如果无法解析为JSON，保持原样
            #     multimodal_messages.append({
            #         "role": role,
            #         "content": [{"type": "text", "text": content}]
            #     })
        
        # 处理函数调用消息
        elif bool(role == "assistant" and message.get("tool_calls")) :
            # print("2")
            # 构建包含工具调用的多模态消息
            # multimodal_content = []
            
            # 添加文本内容（如果有）
            # if content:
            # multimodal_content.append()
            
            # # 添加工具调用信息
            # for tool_call in message.get("tool_calls", []):
            #     tool_info = {
            #         "type": "tool_call",
            #         "function": tool_call.get("function", {}),
            #         "id": tool_call.get("id", "")
            #     }
            #     multimodal_content.append(tool_info)
            
            multimodal_messages.append({
                "role": role,
                "content": [{"type": "text", "text": ""}],
                "tool_calls":message.get("tool_calls", [])
            })
        
        # 处理普通文本消息
        elif isinstance(content, str):
            # print("3")
            multimodal_messages.append({
                "role": role,
                "content": [{"type": "text", "text": content}]
            })
        
        # 处理已经是多模态格式的消息
        elif isinstance(content, list):
            # print("4")
            # 确保每个内容项都有type字段
            validated_content = []
            for item in content:
                if isinstance(item, dict) and "type" in item:
                    validated_content.append(item)
                elif isinstance(item, str):
                    validated_content.append({"type": "text", "text": item})
                elif isinstance(item, dict):
                    # 尝试转换为文本格式
                    validated_content.append({"type": "text", "text": str(item)})
            
            multimodal_messages.append({
                "role": role,
                "content": validated_content
            })
        
        # 处理其他格式的消息
        else:
            # print("5")
            multimodal_messages.append({
                "role": role,
                "content": [{"type": "text", "text": str(content)}]
            })
    
    return multimodal_messages

def extract_tool_calls_from_history(messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    从历史消息中提取工具调用信息
    
    Args:
        messages: 历史消息列表
        
    Returns:
        工具调用信息列表
    """
    tool_calls = []
    
    for message in messages:
        if message.get("role") == "assistant" and message.get("tool_calls"):
            tool_calls.extend(message.get("tool_calls", []))
        
        # 检查多模态格式中的工具调用
        content = message.get("content", [])
        if isinstance(content, list):
            for item in content:
                if isinstance(item, dict) and item.get("type") == "tool_call":
                    tool_calls.append({
                        "id": item.get("id", ""),
                        "function": item.get("function", {})
                    })
    
    return tool_calls

def create_tool_response_message(tool_call_id: str, function_name: str, result: Any) -> Dict[str, Any]:
    """
    创建工具响应消息
    
    Args:
        tool_call_id: 工具调用ID
        function_name: 函数名称
        result: 工具执行结果
        
    Returns:
        工具响应消息
    """
    return {
        "role": "tool",
        "content": json.dumps({
            "tool_call_id": tool_call_id,
            "function_name": function_name,
            "result": result
        }, ensure_ascii=False),
        "tool_call_id": tool_call_id
    }

# 使用示例
if __name__ == "__main__":
    # 示例历史消息（包含工具调用）
    example_history = [
        {
            "role": "user",
            "content": "请帮我查询iPhone-16的价格"
        },
        {
            "role": "assistant",
            "content": "我将为您查询iPhone-16的价格信息",
            "tool_calls": [
                {
                    "id": "call_123",
                    "type": "function",
                    "function": {
                        "name": "get_product_price_by_keyword",
                        "arguments": '{"keyword": "iPhone-16", "limit": 5}'
                    }
                }
            ]
        },
        {
            "role": "tool",
            "content": '{"tool_call_id": "call_123", "function_name": "get_product_price_by_keyword", "result": {"success": true, "price_range": {"min_price": 3468.0, "max_price": 6415.0, "avg_price": 4646.37, "price_count": 30}, "product_info": {"total_items_found": 1}}}',
            "tool_call_id": "call_123"
        }
    ]
    
    print("原始历史消息:")
    print(json.dumps(example_history, ensure_ascii=False, indent=2))
    
    print("\n转换为多模态格式:")
    multimodal_history = transform_tool_history_to_multimodal(example_history)
    print(json.dumps(multimodal_history, ensure_ascii=False, indent=2))
    
    print("\n提取的工具调用:")
    tool_calls = extract_tool_calls_from_history(example_history)
    print(json.dumps(tool_calls, ensure_ascii=False, indent=2))
