import unittest
from ..recommendation_service import get_recommendations

class TestRecommendationService(unittest.TestCase):
    def test_get_recommendations_success(self):
        # 调用真实API
        result = get_recommendations(
            uid="1956635736409112576",
            start_place="方正大厦",
            end_place="机场"
        )

        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "推荐获取成功")
        self.assertIn("recommended_start_places", result["data"])
        self.assertIn("recommended_end_places", result["data"])

    def test_get_recommendations_without_optional_params(self):
        # 调用真实API（仅必选参数）
        result = get_recommendations(uid="1956635736409112576")

        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "推荐获取成功")

if __name__ == "__main__":
    unittest.main()
