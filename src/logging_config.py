"""
统一的日志配置；优先使用 structlog，不存在则回退到标准 logging。
"""

import logging
import sys

import structlog  # type: ignore


def configure_structlog(log_level: str = "INFO", json_format: bool = True):
    """
    配置日志。若可用则配置 structlog，否则配置标准 logging。

    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
        json_format: 是否使用 JSON 格式输出
    """
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    root_logger.setLevel(log_level)
    root_logger.addHandler(console_handler)

    # 配置 structlog 处理器
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]

    if json_format:
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer())

    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str = None):
    """
    获取 logger；若 structlog 可用则返回 structlog logger，否则返回标准 logging logger。
    """
    return structlog.getLogger(name)
