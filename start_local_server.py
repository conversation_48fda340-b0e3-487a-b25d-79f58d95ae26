#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动本地OAuth2回调服务器
监听localhost:8080端口自动捕获授权码
"""

import threading
import webbrowser
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs, urlencode
import requests
import json

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

class OAuth2CallbackHandler(BaseHTTPRequestHandler):
    """OAuth2回调处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        if self.path.startswith('/callback'):
            self.handle_oauth_callback()
        else:
            self.send_404()
    
    def handle_oauth_callback(self):
        """处理OAuth2回调"""
        # 解析URL参数
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        code = query_params.get('code', [None])[0]
        state = query_params.get('state', [None])[0]
        error = query_params.get('error', [None])[0]
        
        print(f"\n🔔 收到回调请求: {self.path}")
        
        if error:
            print(f"❌ 授权失败: {error}")
            self.send_error_page(f"授权失败: {error}")
            self.server.result = {'success': False, 'error': error}
        elif code:
            print(f"✅ 收到授权码: {code[:10]}...")
            if state:
                print(f"🔐 状态参数: {state}")
            
            # 立即交换access_token
            print("🔄 正在交换access_token...")
            token_result = self.exchange_access_token(code)
            
            if token_result:
                self.send_success_page(token_result)
                self.server.result = {'success': True, 'token_data': token_result}
            else:
                self.send_error_page("获取access_token失败")
                self.server.result = {'success': False, 'error': '获取token失败'}
        else:
            print("❌ 未收到授权码")
            self.send_error_page("未收到授权码")
            self.server.result = {'success': False, 'error': '未收到授权码'}
    
    def exchange_access_token(self, code):
        """交换access_token"""
        url = "https://openapi.smzdm.com/v1/oauth/check/code"
        params = {
            "app_key": APP_KEY,
            "code": code,
            "app_secret": APP_SECRET
        }
        
        try:
            response = requests.post(url, data=params, timeout=10)
            print(f"Token API状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Token API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                error_code = result.get("error_code", "")
                if error_code == "0":
                    data = result.get("data", {})
                    access_token = data.get("access_token", "")
                    expires_in = data.get("expires_in", 0)
                    union_id = data.get("union_id", "")
                    
                    print(f"🎉 access_token获取成功!")
                    print(f"🔑 access_token: {access_token}")
                    print(f"⏰ expires_in: {expires_in}秒")
                    print(f"👤 union_id: {union_id}")
                    
                    # 保存到文件
                    with open('access_token.txt', 'w') as f:
                        f.write(f"access_token={access_token}\n")
                        f.write(f"expires_in={expires_in}\n")
                        f.write(f"union_id={union_id}\n")
                        f.write(f"code={code}\n")
                    
                    print("💾 Token信息已保存到 access_token.txt")
                    return data
                else:
                    error_msg = result.get("error_msg", "未知错误")
                    print(f"❌ Token API错误: {error_code} - {error_msg}")
                    return None
            else:
                print(f"❌ Token API请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Token交换异常: {e}")
            return None
    
    def send_success_page(self, token_data):
        """发送成功页面"""
        access_token = token_data.get('access_token', '')
        expires_in = token_data.get('expires_in', 0)
        union_id = token_data.get('union_id', '')
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>OAuth2授权成功</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }}
                .container {{ background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .success {{ color: #28a745; }}
                .token {{ word-break: break-all; font-family: monospace; background: #e9ecef; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                .info {{ background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="success">🎉 OAuth2.0 授权成功!</h1>
                <div class="info">
                    <h3>Access Token 信息:</h3>
                    <p><strong>Token:</strong></p>
                    <div class="token">{access_token}</div>
                    <p><strong>有效期:</strong> {expires_in}秒 ({expires_in//3600}小时)</p>
                    <p><strong>用户ID:</strong> {union_id}</p>
                </div>
                <p>✅ Token已自动保存到 access_token.txt 文件</p>
                <p>🚀 现在可以使用这个access_token调用Zhidemai API了！</p>
                <p>💡 您可以关闭此页面，返回终端查看详细信息</p>
            </div>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_error_page(self, error_msg):
        """发送错误页面"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>OAuth2授权失败</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }}
                .container {{ background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .error {{ color: #dc3545; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="error">❌ OAuth2授权失败</h1>
                <p><strong>错误信息:</strong> {error_msg}</p>
                <p>请返回终端重试授权流程</p>
            </div>
        </body>
        </html>
        """
        
        self.send_response(400)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_404(self):
        """发送404页面"""
        html = """
        <!DOCTYPE html>
        <html>
        <head><title>404 Not Found</title><meta charset="utf-8"></head>
        <body style="font-family: Arial; text-align: center; margin-top: 100px;">
            <h1>404 - 页面未找到</h1>
            <p>OAuth2回调服务器正在运行</p>
            <p>请访问正确的授权URL</p>
        </body>
        </html>
        """
        
        self.send_response(404)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志输出"""
        print(f"[{time.strftime('%H:%M:%S')}] {format % args}")

def generate_authorization_url():
    """生成授权URL"""
    redirect_uri = "http://localhost:8080/callback"
    state = f"server_{int(time.time())}"
    
    base_url = "https://smzdm.com/oauth2/authorize"
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    query_string = urlencode(params)
    return f"{base_url}?{query_string}", state

def start_oauth_server():
    """启动OAuth2服务器"""
    print("🚀 启动本地OAuth2回调服务器")
    print("=" * 50)
    
    port = 8080
    
    # 生成授权URL
    auth_url, state = generate_authorization_url()
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"📍 回调地址: http://localhost:{port}/callback")
    print(f"🔐 状态参数: {state}")
    print(f"🔗 授权URL: {auth_url}")
    
    try:
        # 启动HTTP服务器
        server = HTTPServer(('localhost', port), OAuth2CallbackHandler)
        server.result = None
        
        print(f"\n🌐 本地服务器启动成功 (端口: {port})")
        
        # 在新线程中运行服务器
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        print("✅ 服务器线程已启动")
        
        # 自动打开浏览器
        print("\n🌍 正在打开浏览器进行授权...")
        try:
            webbrowser.open(auth_url)
            print("✅ 浏览器已打开")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"请手动复制以下URL到浏览器:")
            print(f"{auth_url}")
        
        print(f"\n⏳ 等待授权完成...")
        print("💡 完成授权后，浏览器会自动跳转到本地页面")
        print("🛑 按 Ctrl+C 可以随时停止")
        
        # 等待授权结果
        timeout = 300  # 5分钟超时
        start_time = time.time()
        
        while server.result is None:
            if time.time() - start_time > timeout:
                print("\n⏰ 等待超时 (5分钟)，请重试")
                break
            time.sleep(1)
        
        # 处理结果
        if server.result:
            if server.result['success']:
                print(f"\n🎉 OAuth2.0流程完成!")
                print("Token信息已保存，可以开始使用API了！")
                
                # 询问是否测试token
                test_choice = input("\n是否测试获取到的token? (y/n): ").strip().lower()
                if test_choice == 'y':
                    print("请运行: python test_access_token.py")
            else:
                print(f"\n❌ 授权失败: {server.result.get('error', '未知错误')}")
        else:
            print(f"\n⏰ 授权超时，请重试")
        
        # 关闭服务器
        server.shutdown()
        print("🔚 服务器已关闭")
        
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ 端口 {port} 已被占用")
            print("请关闭占用该端口的程序，或者等待几秒后重试")
        else:
            print(f"❌ 启动服务器失败: {e}")
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断，服务器关闭")
        if 'server' in locals():
            server.shutdown()

if __name__ == "__main__":
    start_oauth_server()
