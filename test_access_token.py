#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试access_token是否有效的工具
使用获取到的access_token调用Zhidemai API
"""

import requests
import json

# 配置信息
APP_KEY = "z13408f3a0"

def test_user_info_api(access_token):
    """
    测试用户信息接口
    
    Args:
        access_token (str): OAuth2.0获取的访问令牌
    
    Returns:
        dict: API响应数据
    """
    url = "https://openapi.smzdm.com/v1/oauth/user/info"
    
    params = {
        "app_key": APP_KEY,
        "access_token": access_token
    }
    
    print(f"测试用户信息接口...")
    print(f"请求URL: {url}")
    print(f"请求参数: {{'app_key': '{APP_KEY}', 'access_token': '{access_token[:20]}...'}}")
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            error_code = result.get("error_code", "")
            if error_code == "0":
                print("✅ 用户信息获取成功!")
                data = result.get("data", {})
                print(f"  用户信息: {data}")
                return result
            else:
                error_msg = result.get("error_msg", "未知错误")
                print(f"❌ 用户信息获取失败: {error_code} - {error_msg}")
                return result
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_haojia_detail_api(access_token, article_id="13284568"):
    """
    测试好价详情接口 (需要OAuth2.0认证)
    
    Args:
        access_token (str): OAuth2.0获取的访问令牌
        article_id (str): 文章ID
    
    Returns:
        dict: API响应数据
    """
    url = "https://openapi.smzdm.com/v1/youhui/detail/show"
    
    params = {
        "app_key": APP_KEY,
        "access_token": access_token,
        "article_id": article_id
    }
    
    print(f"测试好价详情接口...")
    print(f"请求URL: {url}")
    print(f"请求参数: {{'app_key': '{APP_KEY}', 'access_token': '{access_token[:20]}...', 'article_id': '{article_id}'}}")
    
    try:
        response = requests.get(url, params=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            error_code = result.get("error_code", "")
            if error_code == "0":
                print("✅ 好价详情获取成功!")
                data = result.get("data", {})
                print(f"  标题: {data.get('title', 'N/A')}")
                print(f"  价格: {data.get('subtitle', 'N/A')}")
                print(f"  商城: {data.get('mall', 'N/A')}")
                return result
            else:
                error_msg = result.get("error_msg", "未知错误")
                print(f"❌ 好价详情获取失败: {error_code} - {error_msg}")
                return result
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def load_token_from_file():
    """
    从文件中加载access_token
    
    Returns:
        str: access_token或None
    """
    try:
        with open('access_token.txt', 'r') as f:
            content = f.read()
            for line in content.split('\n'):
                if line.startswith('access_token='):
                    return line.split('=', 1)[1].strip()
        return None
    except FileNotFoundError:
        return None

def main():
    """
    主函数
    """
    print("🧪 Access Token 测试工具")
    print("=" * 40)
    
    # 获取access_token
    access_token = None
    
    # 尝试从文件加载
    file_token = load_token_from_file()
    if file_token:
        print(f"📁 从文件中找到token: {file_token[:20]}...")
        use_file = input("是否使用文件中的token? (y/n): ").strip().lower()
        if use_file == 'y':
            access_token = file_token
    
    # 手动输入
    if not access_token:
        access_token = input("请输入access_token: ").strip()
    
    if not access_token:
        print("❌ 未提供access_token，程序退出")
        return
    
    print(f"\n🔑 使用token: {access_token[:20]}...")
    
    # 测试API
    print("\n" + "=" * 40)
    print("🧪 开始API测试")
    print("-" * 40)
    
    # 测试1: 用户信息接口
    print("\n1️⃣ 测试用户信息接口")
    user_result = test_user_info_api(access_token)
    
    # 测试2: 好价详情接口
    print("\n2️⃣ 测试好价详情接口")
    article_id = input("请输入文章ID (默认: 13284568): ").strip()
    if not article_id:
        article_id = "13284568"
    
    haojia_result = test_haojia_detail_api(access_token, article_id)
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 测试总结")
    print("-" * 40)
    
    user_success = user_result and user_result.get("error_code") == "0"
    haojia_success = haojia_result and haojia_result.get("error_code") == "0"
    
    print(f"用户信息接口: {'✅ 成功' if user_success else '❌ 失败'}")
    print(f"好价详情接口: {'✅ 成功' if haojia_success else '❌ 失败'}")
    
    if user_success or haojia_success:
        print("\n🎉 access_token有效，可以正常调用API!")
    else:
        print("\n⚠️ access_token可能无效或已过期，请重新获取")

if __name__ == "__main__":
    main()
