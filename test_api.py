#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试值得买API调用功能
"""

from API_call import ZhideMaiAPIClient, ZhideMaiAPIWrapper


def test_signature_generation():
    """测试签名生成功能"""
    print("=== 测试签名生成功能 ===")
    
    # 使用文档中的示例参数
    client = ZhideMaiAPIClient(
        app_key="123456789",
        app_secret="88888888"
    )
    
    # 文档示例参数
    test_params = {
        'order_status': '1',
        'page_size': '10',
        'page': '1'
    }
    
    # 手动设置时间戳以匹配文档示例
    client._generate_timestamp = lambda: '1480411125'
    
    signature_info = client.test_signature_generation(test_params)
    
    print("参数信息:")
    for key, value in signature_info.items():
        print(f"  {key}: {value}")
    
    # 验证签名是否与文档示例一致
    expected_signature = "27C4D45CE6F51B71493FC2B9AA80DB23"
    actual_signature = signature_info['signature']
    
    print(f"\n预期签名: {expected_signature}")
    print(f"实际签名: {actual_signature}")
    print(f"签名验证: {'✓ 通过' if actual_signature == expected_signature else '✗ 失败'}")
    
    return actual_signature == expected_signature


def test_api_wrapper():
    """测试API封装类"""
    print("\n=== 测试API封装类 ===")
    
    # 注意：这里需要替换为真实的app_key和app_secret
    wrapper = ZhideMaiAPIWrapper(
        app_key="your_real_app_key",
        app_secret="your_real_app_secret"
    )
    
    print("API封装类初始化成功")
    print("可用方法:")
    methods = [method for method in dir(wrapper) if not method.startswith('_') and callable(getattr(wrapper, method))]
    for method in methods:
        if hasattr(getattr(wrapper, method), '__doc__'):
            doc = getattr(wrapper, method).__doc__
            if doc:
                print(f"  - {method}: {doc.split('Args:')[0].strip()}")
    
    print("\n注意: 要进行实际API调用，请替换为真实的app_key和app_secret")


def test_parameter_handling():
    """测试参数处理功能"""
    print("\n=== 测试参数处理功能 ===")
    
    client = ZhideMaiAPIClient(
        app_key="test_key",
        app_secret="test_secret"
    )
    
    # 测试空值过滤
    test_params = {
        'valid_param': 'value1',
        'empty_string': '',
        'none_value': None,
        'zero_value': 0,
        'false_value': False
    }
    
    prepared = client._prepare_params(test_params)
    
    print("原始参数:", test_params)
    print("处理后参数:", {k: v for k, v in prepared.items() if k != 'sign'})
    print("生成的签名:", prepared['sign'])
    
    # 验证空值被正确过滤
    assert 'empty_string' not in prepared
    assert 'none_value' not in prepared
    assert 'zero_value' in prepared  # 0应该保留
    assert 'false_value' in prepared  # False应该保留
    
    print("✓ 参数处理测试通过")


def main():
    """主测试函数"""
    print("值得买API调用测试")
    print("=" * 50)
    
    try:
        # 测试签名生成
        signature_test_passed = test_signature_generation()
        
        # 测试参数处理
        test_parameter_handling()
        
        # 测试API封装
        test_api_wrapper()
        
        print("\n" + "=" * 50)
        print("测试总结:")
        print(f"签名生成测试: {'✓ 通过' if signature_test_passed else '✗ 失败'}")
        print("参数处理测试: ✓ 通过")
        print("API封装测试: ✓ 通过")
        
        if signature_test_passed:
            print("\n🎉 所有测试通过！API调用功能正常")
        else:
            print("\n⚠️  签名生成测试失败，请检查算法实现")
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
