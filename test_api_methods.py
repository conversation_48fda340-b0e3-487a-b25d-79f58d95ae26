#!/usr/bin/env python3
"""
测试Zhidemai API的不同调用方式
验证好价信息详情接口是否支持传统签名方式

使用方法:
python test_api_methods.py
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    get_haojia_info_detail,
    get_haojia_info_detail_traditional,
    test_api_methods_comparison,
    make_api_request,
    make_oauth2_api_request,
    APP_KEY,
    APP_SECRET
)

def analyze_api_documentation():
    """
    分析API文档，判断支持的调用方式
    """
    print("📚 API文档分析")
    print("=" * 50)
    
    print("🔍 好价信息详情接口文档分析:")
    print("   接口路径: v1/youhui/detail/show")
    print("   公共参数: '参考新手指南'")
    print("   必需参数: article_id, version")
    
    print("\n🔍 新手指南中的公共参数:")
    print("   - app_key: 开放平台分配的AppKey")
    print("   - timestamp: 时间戳")
    print("   - sign: API参数签名")
    
    print("\n🔍 OAuth2.0调用方法中的公共参数:")
    print("   - app_key: 开放平台分配的AppKey")
    print("   - access_token: OAuth2.0访问令牌")
    
    print("\n💡 分析结论:")
    print("   1. 文档提到'参考新手指南'，暗示支持传统签名方式")
    print("   2. 接口位于OAuth2.0相关API分类下，暗示支持OAuth2.0方式")
    print("   3. 可能两种方式都支持，需要实际测试验证")

def test_with_mock_data():
    """
    使用模拟数据测试不同调用方式
    """
    print("\n🧪 模拟数据测试")
    print("=" * 50)
    
    test_params = {
        "article_id": "13284568",
        "version": "2"
    }
    mock_token = "mock_access_token_for_testing"
    
    print(f"📋 测试参数: {test_params}")
    print(f"🎫 模拟token: {mock_token[:20]}...")
    
    # 测试OAuth2.0方式的参数构建
    print(f"\n1️⃣ OAuth2.0方式参数构建:")
    oauth_params = {
        **test_params,
        "app_key": APP_KEY,
        "access_token": mock_token
    }
    print(f"   参数: {list(oauth_params.keys())}")
    print(f"   特点: 无签名，参数简洁")
    
    # 测试传统签名方式的参数构建
    print(f"\n2️⃣ 传统签名方式参数构建:")
    import time
    traditional_params = {
        **test_params,
        "app_key": APP_KEY,
        "timestamp": str(int(time.time() * 1000)),
        "v": "1.0",
        "format": "json",
        "access_token": mock_token
    }
    
    # 生成签名
    from fc_module.tools.zhidemai_info import generate_sign
    sign = generate_sign(traditional_params, APP_SECRET)
    traditional_params["sign"] = sign
    
    print(f"   参数: {list(traditional_params.keys())}")
    print(f"   特点: 包含签名，参数较多")
    print(f"   签名: {sign[:20]}...")

def test_real_api_calls():
    """
    测试真实API调用（需要用户提供token）
    """
    print(f"\n🔥 真实API调用测试")
    print("=" * 50)
    
    print("💡 要进行真实测试，需要有效的access_token")
    print("   可以通过以下方式获取:")
    print("   1. 运行 python get_access_token_manual.py")
    print("   2. 运行 python local_callback_server.py")
    print("   3. 手动完成OAuth2.0授权流程")
    
    token = input("\n请输入access_token进行真实测试 (直接回车跳过): ").strip()
    
    if not token:
        print("⏭️  跳过真实API测试")
        return
    
    test_article_id = "13284568"
    print(f"\n🔍 测试文章ID: {test_article_id}")
    print(f"🎫 使用token: {token[:20]}...")
    
    results = {}
    
    # 测试OAuth2.0方式
    print(f"\n📋 测试OAuth2.0方式:")
    try:
        oauth_result = get_haojia_info_detail(test_article_id, version="2", access_token=token)
        if oauth_result:
            error_code = oauth_result.get("error_code", "")
            if error_code == "0":
                print("✅ OAuth2.0方式: 调用成功!")
                results["oauth2"] = "success"
            else:
                error_msg = oauth_result.get("error_msg", "未知错误")
                print(f"❌ OAuth2.0方式: {error_code} - {error_msg}")
                results["oauth2"] = f"error_{error_code}"
        else:
            print("❌ OAuth2.0方式: 请求失败")
            results["oauth2"] = "failed"
    except Exception as e:
        print(f"❌ OAuth2.0方式异常: {e}")
        results["oauth2"] = f"exception_{type(e).__name__}"
    
    # 测试传统签名方式
    print(f"\n📋 测试传统签名方式:")
    try:
        traditional_result = get_haojia_info_detail_traditional(test_article_id, version="2", access_token=token)
        if traditional_result:
            error_code = traditional_result.get("error_code", "")
            if error_code == "0":
                print("✅ 传统签名方式: 调用成功!")
                results["traditional"] = "success"
            else:
                error_msg = traditional_result.get("error_msg", "未知错误")
                print(f"❌ 传统签名方式: {error_code} - {error_msg}")
                results["traditional"] = f"error_{error_code}"
        else:
            print("❌ 传统签名方式: 请求失败")
            results["traditional"] = "failed"
    except Exception as e:
        print(f"❌ 传统签名方式异常: {e}")
        results["traditional"] = f"exception_{type(e).__name__}"
    
    # 分析结果
    print(f"\n📊 测试结果分析:")
    print("-" * 30)
    
    oauth2_status = results.get("oauth2", "unknown")
    traditional_status = results.get("traditional", "unknown")
    
    print(f"OAuth2.0方式: {oauth2_status}")
    print(f"传统签名方式: {traditional_status}")
    
    if oauth2_status == "success" and traditional_status == "success":
        print("\n🎉 结论: 两种方式都支持!")
        print("   建议: 优先使用OAuth2.0方式（参数更简洁）")
    elif oauth2_status == "success":
        print("\n✅ 结论: 仅支持OAuth2.0方式")
        print("   建议: 使用OAuth2.0方式调用接口")
    elif traditional_status == "success":
        print("\n✅ 结论: 仅支持传统签名方式")
        print("   建议: 使用传统签名方式调用接口")
    else:
        print("\n❓ 结论: 两种方式都失败")
        print("   可能原因: token无效、接口权限、网络问题等")
        print("   建议: 检查token有效性和接口权限")

def main():
    """
    主函数
    """
    print("🔬 Zhidemai API调用方式测试")
    print("=" * 60)
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"🔐 APP_SECRET: {APP_SECRET[:8]}..." if APP_SECRET else "APP_SECRET: 未设置")
    
    if not APP_KEY or not APP_SECRET:
        print("\n❌ 错误: APP_KEY和APP_SECRET必须设置")
        return
    
    try:
        # 1. 分析API文档
        analyze_api_documentation()
        
        # 2. 模拟数据测试
        test_with_mock_data()
        
        # 3. 运行对比测试
        print(f"\n🔄 运行自动对比测试...")
        test_api_methods_comparison()
        
        # 4. 真实API调用测试
        test_real_api_calls()
        
        print(f"\n" + "=" * 60)
        print("📋 总结和建议")
        print("=" * 60)
        
        print("🔍 基于文档分析:")
        print("   - 好价信息详情接口文档提到'参考新手指南'")
        print("   - 这暗示可能支持传统签名方式")
        print("   - 但接口归类在OAuth2.0相关API下")
        
        print("\n💡 实际使用建议:")
        print("   1. 优先尝试OAuth2.0方式（参数简洁，现代化）")
        print("   2. 如果OAuth2.0失败，再尝试传统签名方式")
        print("   3. 根据实际测试结果选择最适合的方式")
        
        print("\n🛠️  使用方法:")
        print("   # OAuth2.0方式")
        print("   data = get_haojia_info_detail('13284568', version='2', access_token='your_token')")
        print("")
        print("   # 传统签名方式")
        print("   data = get_haojia_info_detail_traditional('13284568', version='2', access_token='your_token')")
        
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
