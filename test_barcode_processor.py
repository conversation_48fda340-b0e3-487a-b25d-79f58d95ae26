#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试条形码处理器功能
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from src.fc_module.tools.barcode_processor import (
    extract_barcode_from_url,
    get_barcode_info,
    process_barcode_url
)


def test_extract_barcode_from_url():
    """测试从URL提取条形码功能"""
    print("=== 测试从URL提取条形码 ===\n")
    
    test_cases = [
        # URL参数形式
        ("https://example.com/barcode?code=1234567890123", "1234567890123"),
        ("https://api.com/product?barcode=6901234567890", "6901234567890"),
        ("https://shop.com/item?ean=9876543210987", "9876543210987"),
        ("https://store.com/lookup?upc=123456789012", "123456789012"),
        
        # URL路径形式
        ("https://barcode.com/product/6901234567890", "6901234567890"),
        ("https://shop.com/item/1234567890123/details", "1234567890123"),
        ("https://api.barcode.com/lookup/barcode_6901234567890", "6901234567890"),
        ("https://store.com/product_9876543210987", "9876543210987"),
        
        # 无效URL
        ("https://invalid-url.com/no-barcode", None),
        ("https://example.com/short/123", None),
        ("", None),
        (None, None),
    ]
    
    for url, expected in test_cases:
        result = extract_barcode_from_url(url)
        status = "✅" if result == expected else "❌"
        print(f"{status} URL: {url}")
        print(f"   期望: {expected}")
        print(f"   结果: {result}")
        print()


def test_get_barcode_info():
    """测试获取条形码信息功能"""
    print("=== 测试获取条形码信息 ===\n")
    
    test_codes = [
        "6901234567890",  # EAN-13 (中国)
        "123456789012",   # UPC-A
        "12345678",       # EAN-8
        "1234567890123",  # EAN-13
        "12345678901234", # GTIN-14
        "invalid",        # 无效条形码
        "",               # 空字符串
    ]
    
    for code in test_codes:
        print(f"条形码: {code}")
        info = get_barcode_info(code)
        print(f"结果: {info}")
        print("-" * 40)


def test_process_barcode_url():
    """测试完整的条形码URL处理功能"""
    print("=== 测试完整条形码URL处理 ===\n")
    
    test_urls = [
        "https://example.com/barcode?code=6901234567890",
        "https://barcode.com/product/1234567890123",
        "https://shop.com/item/123456789012/details",
        "https://api.barcode.com/lookup/barcode_12345678",
        "https://invalid-url.com/no-barcode",
        "https://example.com/short/123",
    ]
    
    for url in test_urls:
        print(f"处理URL: {url}")
        result = process_barcode_url(url)
        
        if result['success']:
            print(f"✅ 成功提取条形码: {result['barcode_code']}")
            print(f"   条形码类型: {result['barcode_info']['type']}")
            print(f"   校验结果: {'有效' if result['barcode_info']['is_valid'] else '无效'}")
            if result['barcode_info']['country_code']:
                print(f"   国家代码: {result['barcode_info']['country_code']}")
        else:
            print(f"❌ 处理失败: {result['error']}")
        
        print("-" * 50)


def main():
    """主测试函数"""
    print("条形码处理器测试程序")
    print("=" * 60)
    
    test_extract_barcode_from_url()
    test_get_barcode_info()
    test_process_barcode_url()
    
    print("\n=== 使用示例 ===")
    print("from src.fc_module.tools.barcode_processor import process_barcode_url")
    print()
    print("# 处理条形码URL")
    print("result = process_barcode_url('https://example.com/barcode?code=6901234567890')")
    print("if result['success']:")
    print("    print(f'条形码: {result[\"barcode_code\"]}')")
    print("    print(f'类型: {result[\"barcode_info\"][\"type\"]}')")
    print("else:")
    print("    print(f'错误: {result[\"error\"]}')")


if __name__ == "__main__":
    main()
