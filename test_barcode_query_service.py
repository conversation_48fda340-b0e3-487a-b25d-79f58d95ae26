#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
条码查询MCP服务测试脚本
演示如何使用条码查询功能
"""

from src.fc_module.tools.image_barcode_processor import (
    configure_barcode_query_service,
    query_barcode_info,
    get_barcode_from_image_and_query_info,
    get_barcode_from_image
)
import json


def test_configuration():
    """测试服务配置"""
    print("🔧 测试1: 服务配置")
    print("=" * 50)
    
    # 注意：这里需要用户提供实际的MCP服务器URL
    print("📝 配置说明:")
    print("   请将 'YOUR_MCP_SERVER_URL' 替换为实际的MCP服务器地址")
    print("   例如: http://your-server.com/mcp")
    print()
    
    # 示例配置（需要用户替换）
    server_url = "YOUR_MCP_SERVER_URL"  # 用户需要替换这个URL
    
    if server_url == "YOUR_MCP_SERVER_URL":
        print("⚠️  请先配置MCP服务器URL")
        print("   在代码中将 'YOUR_MCP_SERVER_URL' 替换为实际地址")
        return False
    
    try:
        configure_barcode_query_service(server_url)
        print("✅ 服务配置成功")
        return True
    except Exception as e:
        print(f"❌ 服务配置失败: {e}")
        return False


def test_barcode_query():
    """测试条码查询功能"""
    print("\n🔍 测试2: 条码查询")
    print("=" * 50)
    
    # 测试条码（来自您提供的示例）
    test_barcode = "6973497203336"
    
    print(f"📊 查询条码: {test_barcode}")
    print("🚀 开始查询...")
    
    try:
        result = query_barcode_info(test_barcode)
        
        if result['success']:
            print("✅ 查询成功!")
            
            product = result['product_info']
            print(f"\n📦 商品信息:")
            print(f"   名称: {product.get('name', 'N/A')}")
            print(f"   品牌: {product.get('brand', 'N/A')}")
            print(f"   规格: {product.get('type', 'N/A')}")
            print(f"   产地: {product.get('origin_country', 'N/A')}")
            print(f"   厂商: {product.get('company', 'N/A')}")
            
            if product.get('picture_url'):
                print(f"   图片: {product['picture_url']}")
            
            # 显示完整信息（可选）
            print(f"\n📋 完整信息:")
            for key, value in product.items():
                if value and value != 'N/A':
                    print(f"   {key}: {value}")
                    
        else:
            print(f"❌ 查询失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 查询过程出错: {e}")


def test_image_to_product_info():
    """测试从图片到商品信息的完整流程"""
    print("\n🖼️  测试3: 图片到商品信息完整流程")
    print("=" * 50)
    
    # 测试图片URL
    image_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    print(f"📷 图片URL: {image_url}")
    print("🚀 开始完整流程...")
    
    try:
        result = get_barcode_from_image_and_query_info(image_url)
        
        print(f"\n📊 流程结果:")
        print(f"   整体成功: {result['success']}")
        
        # 条形码识别结果
        barcode_result = result['barcode_recognition']
        print(f"   条形码识别: {'✅' if barcode_result['success'] else '❌'}")
        if barcode_result['success']:
            print(f"   识别的条形码: {barcode_result['barcode']}")
        
        # 商品查询结果
        if 'product_query' in result:
            query_result = result['product_query']
            print(f"   商品查询: {'✅' if query_result['success'] else '❌'}")
            
            if query_result['success']:
                product = query_result['product_info']
                print(f"   商品名称: {product.get('name', 'N/A')}")
                print(f"   商品品牌: {product.get('brand', 'N/A')}")
            else:
                print(f"   查询错误: {query_result.get('error', 'N/A')}")
        
        if result.get('error'):
            print(f"   错误信息: {result['error']}")
            
    except Exception as e:
        print(f"❌ 完整流程出错: {e}")


def test_batch_processing():
    """测试批量处理"""
    print("\n📦 测试4: 批量处理")
    print("=" * 50)
    
    # 测试条码列表
    test_barcodes = [
        "6973497203336",  # 示例条码
        "6975512173235",  # 从图片识别的条码
        "1234567890123"   # 可能不存在的条码
    ]
    
    print(f"📋 准备查询 {len(test_barcodes)} 个条码")
    
    results = []
    for i, barcode in enumerate(test_barcodes, 1):
        print(f"\n🔍 查询 {i}/{len(test_barcodes)}: {barcode}")
        
        try:
            result = query_barcode_info(barcode)
            results.append(result)
            
            if result['success']:
                product_name = result['product_info'].get('name', '未知商品')
                print(f"   ✅ {product_name}")
            else:
                print(f"   ❌ {result.get('error', '查询失败')}")
                
        except Exception as e:
            print(f"   ❌ 查询出错: {e}")
            results.append({'success': False, 'error': str(e)})
    
    # 统计结果
    successful = len([r for r in results if r.get('success', False)])
    print(f"\n📊 批量处理结果:")
    print(f"   成功: {successful}/{len(test_barcodes)}")
    print(f"   成功率: {successful/len(test_barcodes)*100:.1f}%")


def show_usage_examples():
    """显示使用示例"""
    print("\n📚 使用示例")
    print("=" * 50)
    
    examples = '''
🔧 1. 配置服务:
   from src.fc_module.tools.image_barcode_processor import configure_barcode_query_service
   configure_barcode_query_service("http://your-mcp-server.com/api")

🔍 2. 查询条码信息:
   from src.fc_module.tools.image_barcode_processor import query_barcode_info
   result = query_barcode_info("6973497203336")
   if result['success']:
       print(result['product_info']['name'])

🖼️  3. 从图片识别并查询:
   from src.fc_module.tools.image_barcode_processor import get_barcode_from_image_and_query_info
   result = get_barcode_from_image_and_query_info("https://example.com/barcode.png")
   if result['success']:
       print(f"条码: {result['final_result']['barcode']}")
       print(f"商品: {result['final_result']['product_info']['name']}")

📊 4. 分步处理:
   from src.fc_module.tools.image_barcode_processor import get_barcode_from_image, query_barcode_info
   
   # 步骤1: 识别条码
   barcode = get_barcode_from_image("https://example.com/image.png")
   
   # 步骤2: 查询信息
   if barcode:
       info = query_barcode_info(barcode)
       if info['success']:
           print(info['product_info'])

🔧 MCP服务配置格式:
   {
     "mcpServers": {
       "cmapi011806": {
         "url": "YOUR_SERVER_URL",
         "type": "streamableHttp"
       }
     }
   }
'''
    
    print(examples)


def main():
    """主测试函数"""
    print("🚀 条码查询MCP服务测试")
    print("=" * 60)
    
    # 测试配置
    if not test_configuration():
        print("\n⚠️  由于服务未配置，跳过实际查询测试")
        print("   请配置MCP服务器URL后重新运行")
        show_usage_examples()
        return
    
    # 运行所有测试
    test_barcode_query()
    test_image_to_product_info()
    test_batch_processing()
    show_usage_examples()
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    main()
