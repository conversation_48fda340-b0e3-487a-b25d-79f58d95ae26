#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带时间预估的条形码识别测试脚本
测试时间预估功能和实际耗时统计
"""

from src.fc_module.tools.image_barcode_processor import (
    get_barcode_from_image_with_timing, 
    estimate_processing_time,
    analyze_image_for_barcode
)
import json


def test_time_estimation():
    """测试时间预估功能"""
    
    print("=" * 60)
    print("⏱️  条形码识别时间预估测试")
    print("=" * 60)
    
    # 测试图片URL
    test_image_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    print(f"📷 测试图片: {test_image_url}")
    print()
    
    # 1. 仅预估时间
    print("📊 步骤1: 仅预估处理时间")
    print("-" * 40)
    
    estimation = estimate_processing_time(test_image_url)
    print(f"⏱️  预估总时间: {estimation['estimated_total_time']:.1f}秒")
    print(f"📥 下载时间: {estimation['breakdown']['download_time']:.1f}秒")
    print(f"🔄 处理时间: {estimation['breakdown']['image_processing_time']:.1f}秒")
    print(f"🔍 检测时间: {estimation['breakdown']['barcode_detection_time']:.1f}秒")
    print(f"🎯 置信度: {estimation['confidence']}")
    
    if estimation['factors']:
        print(f"📋 影响因素:")
        for factor in estimation['factors']:
            print(f"   • {factor}")
    
    print()
    
    # 2. 带时间统计的完整识别
    print("🚀 步骤2: 完整识别（含时间统计）")
    print("-" * 40)
    
    result = get_barcode_from_image_with_timing(test_image_url)
    
    print()
    print("📊 识别结果:")
    if result['success']:
        print(f"✅ 条形码: {result['barcode_code']}")
        print(f"🔧 使用方法: {result['method_used']}")
        if 'note' in result:
            print(f"📝 备注: {result['note']}")
    else:
        print(f"❌ 识别失败: {result.get('error', '未知错误')}")
    
    print()
    
    # 3. 详细分析（含各方法时间）
    print("🔬 步骤3: 详细分析（各方法耗时）")
    print("-" * 40)
    
    analysis = analyze_image_for_barcode(test_image_url)
    
    if 'timing' in analysis:
        print(f"⏱️  总分析时间: {analysis['timing']['total_time']:.2f}秒")
        print("📊 各方法耗时:")
        for method, time_cost in analysis['timing']['method_times'].items():
            print(f"   • {method}: {time_cost:.2f}秒")
    
    print()
    print("=" * 60)
    print("✅ 时间预估测试完成！")
    print("=" * 60)


def test_multiple_images():
    """测试多个图片的时间预估准确性"""
    
    print("\n🔄 多图片时间预估准确性测试")
    print("-" * 40)
    
    # 测试不同类型的图片URL
    test_urls = [
        {
            'name': 'OSS存储PNG图片',
            'url': 'https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png'
        }
    ]
    
    for i, test_case in enumerate(test_urls, 1):
        print(f"\n📷 测试 {i}: {test_case['name']}")
        print(f"🔗 URL: {test_case['url']}")
        
        # 预估时间
        estimation = estimate_processing_time(test_case['url'])
        estimated_time = estimation['estimated_total_time']
        
        print(f"📊 预估时间: {estimated_time:.1f}秒")
        
        # 实际测试（简化版，不输出详细过程）
        import time
        start_time = time.time()
        
        from src.fc_module.tools.image_barcode_processor import get_barcode_from_image
        barcode = get_barcode_from_image(test_case['url'])
        
        actual_time = time.time() - start_time
        
        # 计算准确性
        time_diff = actual_time - estimated_time
        accuracy = (1 - abs(time_diff) / estimated_time) * 100 if estimated_time > 0 else 0
        
        print(f"⚡ 实际时间: {actual_time:.2f}秒")
        print(f"📈 时间差异: {time_diff:+.2f}秒")
        print(f"🎯 预估准确度: {accuracy:.1f}%")
        
        if barcode:
            print(f"✅ 识别结果: {barcode}")
        else:
            print("❌ 未识别到条形码")


def show_timing_summary():
    """显示时间预估功能总结"""
    
    print("\n" + "=" * 60)
    print("📋 时间预估功能总结")
    print("=" * 60)
    
    features = [
        "⏱️  智能时间预估 - 根据URL特征预估处理时间",
        "📊 详细时间分解 - 下载、处理、检测各阶段时间",
        "🎯 准确度评估 - 预估与实际时间对比分析",
        "🔍 影响因素分析 - 识别影响处理时间的关键因素",
        "📈 实时进度显示 - 处理过程中的实时状态更新",
        "🔧 多方法时间统计 - 各种识别方法的耗时对比"
    ]
    
    print("🚀 主要功能:")
    for feature in features:
        print(f"   {feature}")
    
    print("\n📝 使用方法:")
    print("   1. estimate_processing_time(url) - 仅预估时间")
    print("   2. get_barcode_from_image_with_timing(url) - 完整识别+时间统计")
    print("   3. analyze_image_for_barcode(url) - 详细分析+各方法时间")
    
    print("\n⚡ 性能优化:")
    print("   • 自动选择最快的识别方法")
    print("   • 根据图片特征调整处理策略")
    print("   • 提供处理时间预期，改善用户体验")


if __name__ == "__main__":
    test_time_estimation()
    test_multiple_images()
    show_timing_summary()
