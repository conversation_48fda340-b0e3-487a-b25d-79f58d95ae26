#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整产品描述功能测试
从图片URL到产品文本描述的完整流程测试
"""

from src.fc_module.tools.image_barcode_processor import (
    get_product_description_from_image,
    get_product_summary_from_image
)


def test_complete_workflow():
    """测试完整工作流程"""
    print("🚀 完整产品描述功能测试")
    print("=" * 60)
    
    # 测试图片URL
    image_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    print(f"📷 测试图片: {image_url}")
    print()
    
    # 测试1: 结构化描述（按您的示例格式）
    print("📋 测试1: 结构化描述")
    print("-" * 40)
    
    structured_desc = get_product_description_from_image(image_url, "structured")
    print(structured_desc)
    print()
    
    # 测试2: 详细描述
    print("📋 测试2: 详细描述")
    print("-" * 40)
    
    detailed_desc = get_product_description_from_image(image_url, "detailed")
    print(detailed_desc)
    print()
    
    # 测试3: 简单描述
    print("📋 测试3: 简单描述")
    print("-" * 40)
    
    simple_desc = get_product_description_from_image(image_url, "simple")
    print(simple_desc)
    print()


def test_product_summary():
    """测试产品摘要功能"""
    print("📊 产品摘要功能测试")
    print("=" * 40)
    
    image_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    summary = get_product_summary_from_image(image_url)
    
    if summary['success']:
        print("✅ 摘要生成成功!")
        print(f"条形码: {summary['barcode']}")
        print(f"商品名称: {summary['name']}")
        print(f"品牌: {summary['brand']}")
        print(f"规格: {summary['specification']}")
        print(f"产地: {summary['origin']}")
        print(f"厂商: {summary['manufacturer']}")
        print(f"类别: {summary['category']}")
        print(f"关键词: {summary['keywords']}")
        
        if summary['image_url']:
            print(f"商品图片: {summary['image_url']}")
        
        print("\n📝 可用的描述格式:")
        print("- 简单格式")
        print("- 详细格式") 
        print("- 结构化格式")
        
    else:
        print(f"❌ 摘要生成失败: {summary['error']}")


def demo_usage_examples():
    """演示使用示例"""
    print("\n💡 使用示例")
    print("=" * 40)
    
    examples = '''
🔧 1. 获取结构化描述（推荐）:
   from src.fc_module.tools.image_barcode_processor import get_product_description_from_image
   
   description = get_product_description_from_image(image_url, "structured")
   print(description)

🔧 2. 获取详细描述:
   description = get_product_description_from_image(image_url, "detailed")
   print(description)

🔧 3. 获取简单描述:
   description = get_product_description_from_image(image_url, "simple")
   print(description)

🔧 4. 获取产品摘要:
   from src.fc_module.tools.image_barcode_processor import get_product_summary_from_image
   
   summary = get_product_summary_from_image(image_url)
   if summary['success']:
       print(f"商品: {summary['name']}")
       print(f"品牌: {summary['brand']}")
       # 可以选择不同格式的描述
       print(summary['descriptions']['structured'])

🔧 5. 一行代码获取产品描述:
   # 最简单的使用方式
   description = get_product_description_from_image("图片URL")
   print(description)
'''
    
    print(examples)


def test_different_formats():
    """测试不同格式的输出"""
    print("🎨 不同格式输出对比")
    print("=" * 40)
    
    image_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    formats = [
        ("简单格式", "simple"),
        ("结构化格式", "structured"), 
        ("详细格式", "detailed")
    ]
    
    for format_name, format_style in formats:
        print(f"\n📋 {format_name}:")
        print("-" * 30)
        
        description = get_product_description_from_image(image_url, format_style)
        print(description)
        
        print()


def show_function_reference():
    """显示函数参考"""
    print("📚 函数参考")
    print("=" * 40)
    
    reference = '''
🔧 主要函数:

1. get_product_description_from_image(image_url, format_style="detailed")
   参数:
   - image_url: 图片URL
   - format_style: 格式样式 ("simple", "detailed", "structured")
   返回: 产品描述文本字符串

2. get_product_summary_from_image(image_url)
   参数:
   - image_url: 图片URL
   返回: 包含完整产品信息的字典

📊 格式样式说明:
- "simple": 简洁的一行描述
- "structured": 按您的示例格式，适合正式文档
- "detailed": 详细的多字段描述，包含所有可用信息

🎯 返回的摘要字典包含:
- barcode: 条形码
- name: 商品名称
- brand: 品牌
- specification: 规格
- origin: 产地
- manufacturer: 生产商
- category: 商品类别
- image_url: 商品图片URL
- descriptions: 三种格式的描述文本
'''
    
    print(reference)


def main():
    """主测试函数"""
    print("🎉 从图片URL到产品描述的完整功能测试")
    print("=" * 70)
    
    # 运行所有测试
    test_complete_workflow()
    test_product_summary()
    test_different_formats()
    demo_usage_examples()
    show_function_reference()
    
    print("\n🎊 测试完成!")
    print("现在您可以使用一个函数从图片URL直接获取完整的产品描述！")


if __name__ == "__main__":
    main()
