"""
Final test script for Elasticsearch Service
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, 'src')

def test_elasticsearch_service():
    """Test the ElasticsearchService class"""
    print("Testing Elasticsearch Service...")
    print("=" * 50)
    
    try:
        # Import the service
        from fc_module.utils.elastic_search_service import ElasticsearchService
        print("✓ Successfully imported ElasticsearchService")
        
        # Create an instance
        es_service = ElasticsearchService()
        print("✓ Successfully created ElasticsearchService instance")
        print(f"  Host: {es_service.host}")
        print(f"  Port: {es_service.port}")
        print(f"  Index name: {es_service.index_name}")
        print(f"  ES URL: {es_service.es_url}")
        
        # Check all methods exist
        methods = [
            'create_router_index',
            'add_router_document',
            'bulk_add_router_documents',
            'search_by_text',
            'search_by_keyword',
            'search_by_dense_vector',
            'get_document',
            'delete_document'
        ]
        
        print("\nChecking methods:")
        for method in methods:
            if hasattr(es_service, method):
                print(f"✓ Method '{method}' exists")
            else:
                print(f"✗ Method '{method}' is missing")
                return False
        
        # Test main function exists
        import inspect
        if hasattr(es_service.__class__, 'main') or 'main' in globals():
            print("✓ Main function exists")
        else:
            print("ℹ Main function not in class (it's a module-level function)")
        
        print("\n" + "=" * 50)
        print("🎉 All basic tests passed!")
        print("The ElasticsearchService class is ready for use.")
        print("Note: Connection tests require a running Elasticsearch server.")
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_elasticsearch_service()
    sys.exit(0 if success else 1)
