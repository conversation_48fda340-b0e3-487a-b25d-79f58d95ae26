#!/usr/bin/env python3
"""
测试增强版LLM Router Evaluator
"""

import asyncio
import sys
import pathlib

# 添加路径
sys.path.append(str(pathlib.Path(__file__).parent))

from src.fc_module.offline_procedure.llm_router_evaluator import LLMRouterEvaluator

async def test_enhanced_evaluation():
    """测试增强版评估器"""
    print("=== 开始测试增强版LLM Router Evaluator ===")
    
    evaluator = LLMRouterEvaluator()
    
    try:
        # 测试获取最大requestId
        max_id = evaluator.get_max_request_id_from_es()
        print(f"✓ ES最大requestId: {max_id}")
        
        # 测试获取新数据
        new_records = evaluator.fetch_new_function_calling_data(max_id)
        print(f"✓ 获取新数据: {len(new_records)} 条记录")
        
        if len(new_records) > 0:
            print(f"  第一条记录ID: {new_records[0].get('id')}")
            print(f"  第一条记录tools: {new_records[0].get('tools', '[]')[:100]}...")
        
        # 运行增强版评估
        print("\n=== 运行增强版评估 ===")
        result = await evaluator.run_enhanced_evaluation(max_records=800)
        print("result:",result)
        print(f"\n=== 评估结果摘要 ===")
        print(f"总记录数: {result['total_records']}")
        print(f"处理记录数: {result['processed_records']}")
        print(f"替换机会: {result['replacement_opportunities']}")
        
        # 打印替换分析
        if result['replacement_analysis']:
            print("\n=== 模型替换分析 ===")
            for analysis in result['replacement_analysis']:
                record_id = analysis['record_id']
                can_replace = analysis['analysis']['qwen-max']['can_be_replaced']
                # if can_replace:
                    
                print(f"\n记录ID: {record_id}")
                if can_replace:
                    print("✓ 235b模型可以被替换:")
                    for candidate in analysis['analysis']['qwen-max']['replacement_candidates']:
                        print(f"  - {candidate['model']}: {candidate['speedup_ratio']:.2f}x 加速")
                else:
                    print("✗ 235b模型暂无可替换的小尺寸模型")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_enhanced_evaluation())
