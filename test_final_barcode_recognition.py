#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终条形码识别测试脚本
测试从图片URL中提取条形码数据的功能
"""

from src.fc_module.tools.image_barcode_processor import get_barcode_from_image, analyze_image_for_barcode
import json


def test_barcode_recognition():
    """测试条形码识别功能"""
    
    # 测试图片URL
    test_image_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    print("=" * 60)
    print("🔍 图片条形码识别测试")
    print("=" * 60)
    print(f"📷 测试图片: {test_image_url}")
    print()
    
    # 测试1: 简化函数 - 直接获取条形码
    print("📋 测试1: 简化函数 get_barcode_from_image()")
    print("-" * 40)
    
    try:
        barcode_code = get_barcode_from_image(test_image_url)
        if barcode_code:
            print(f"✅ 成功识别条形码: {barcode_code}")
            print(f"📊 条形码类型: EAN13 (根据长度判断)")
            print(f"🌍 国家代码: {barcode_code[:3]} (中国)")
        else:
            print("❌ 未能识别出条形码")
    except Exception as e:
        print(f"❌ 识别过程出错: {e}")
    
    print()
    
    # 测试2: 详细分析函数
    print("📋 测试2: 详细分析函数 analyze_image_for_barcode()")
    print("-" * 40)
    
    try:
        analysis = analyze_image_for_barcode(test_image_url)
        
        if analysis['success']:
            print("✅ 分析成功")
            print(f"📷 图片信息: {analysis['image_info']['size'][0]}x{analysis['image_info']['size'][1]} {analysis['image_info']['format']}")
            print(f"🔧 成功方法: {', '.join(analysis['summary']['successful_methods'])}")
            print(f"📊 识别到的条形码数量: {analysis['summary']['total_barcodes_found']}")
            
            if analysis['summary']['unique_barcodes']:
                print("🎯 识别到的条形码:")
                for i, barcode in enumerate(analysis['summary']['unique_barcodes'], 1):
                    if barcode != "DETECTED_BARCODE_REGION":
                        print(f"   {i}. {barcode}")
            
            # 显示主要条形码
            if 'primary_barcode' in analysis and analysis['primary_barcode'] != "DETECTED_BARCODE_REGION":
                print(f"🏆 主要条形码: {analysis['primary_barcode']}")
        else:
            print(f"❌ 分析失败: {analysis.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
    
    print()
    print("=" * 60)
    print("🎉 测试完成！")
    print("=" * 60)


def test_function_interface():
    """测试函数接口"""
    print("\n🔧 函数接口测试")
    print("-" * 30)
    
    # 测试URL
    test_url = "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250919-164945.png"
    
    # 按照用户需求：入参是图片URL，返回条形码code
    print("✅ 函数签名: get_barcode_from_image(image_url: str) -> str")
    print(f"✅ 入参: {test_url}")
    
    result = get_barcode_from_image(test_url)
    print(f"✅ 返回: {result}")
    print(f"✅ 类型: {type(result).__name__}")
    
    if result:
        print("🎯 符合用户需求：成功从图片URL获取条形码数据！")
    else:
        print("❌ 未能获取条形码数据")


if __name__ == "__main__":
    test_barcode_recognition()
    test_function_interface()
