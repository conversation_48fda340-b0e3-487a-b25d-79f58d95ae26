#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的OAuth2授权URL
"""

from urllib.parse import urlencode
import webbrowser

# 配置信息
APP_KEY = "z13408f3a0"

def generate_authorization_url(redirect_uri, state=None):
    """生成OAuth2.0授权URL"""
    # 使用正确的授权端点
    base_url = "https://smzdm.com/oauth2/authorize"
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri
    }
    
    if state:
        params["state"] = state
    
    query_string = urlencode(params)
    return f"{base_url}?{query_string}"

def main():
    """测试修复后的授权URL"""
    print("🔧 测试修复后的OAuth2授权URL")
    print("=" * 40)
    
    redirect_uri = "http://localhost:8080/callback"
    state = f"test_fixed_{int(__import__('time').time())}"
    
    # 生成授权URL
    auth_url = generate_authorization_url(redirect_uri, state)
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"📍 回调地址: {redirect_uri}")
    print(f"🔐 状态参数: {state}")
    print(f"\n🔗 修复后的授权URL:")
    print(f"{auth_url}")
    
    print(f"\n📝 测试步骤:")
    print("1. 复制上面的URL")
    print("2. 在浏览器中打开")
    print("3. 检查是否能正常显示登录页面")
    
    # 询问是否自动打开浏览器
    open_browser = input("\n是否自动打开浏览器测试? (y/n): ").strip().lower()
    
    if open_browser == 'y':
        try:
            print("🌍 正在打开浏览器...")
            webbrowser.open(auth_url)
            print("✅ 浏览器已打开")
            print("\n💡 如果能看到值得买的登录页面，说明授权端点修复成功！")
        except Exception as e:
            print(f"❌ 无法打开浏览器: {e}")
            print("请手动复制URL到浏览器测试")
    else:
        print("👍 请手动复制URL到浏览器测试")
    
    print(f"\n🎯 期望结果:")
    print("- 浏览器显示值得买登录页面")
    print("- 登录后显示授权确认页面")
    print("- 点击授权后跳转到localhost:8080/callback")

if __name__ == "__main__":
    main()
