#!/usr/bin/env python3
"""
Zhidemai 好价信息详情接口测试脚本
演示如何使用封装的好价信息详情接口

使用方法:
python test_haojia_detail.py
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    get_haojia_info_detail,
    get_haojia_detail_simple,
    parse_haojia_detail_v1,
    parse_haojia_detail_v2,
    print_haojia_detail_summary,
    test_haojia_detail_api,
    APP_KEY,
    APP_SECRET,
    ACCESS_TOKEN
)

def demo_api_usage():
    """
    演示API使用方法
    """
    print("🚀 Zhidemai 好价信息详情接口演示")
    print("=" * 60)
    
    # 示例文章ID
    demo_article_id = "13284568"
    
    print(f"📋 演示文章ID: {demo_article_id}")
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"🔐 APP_SECRET: {APP_SECRET[:8]}..." if APP_SECRET else "APP_SECRET: 未设置")
    print(f"🎫 ACCESS_TOKEN: {'已设置' if ACCESS_TOKEN and ACCESS_TOKEN != 'YOUR_MOCKED_ACCESS_TOKEN_FOR_DEMO' else '需要设置'}")
    
    print("\n" + "=" * 60)
    
    if ACCESS_TOKEN and ACCESS_TOKEN != "YOUR_MOCKED_ACCESS_TOKEN_FOR_DEMO":
        print("🔥 执行真实API调用测试...")
        
        # 1. 测试简化版函数
        print("\n1️⃣ 测试简化版函数:")
        simple_data = get_haojia_detail_simple(demo_article_id)
        if simple_data:
            print("✅ 简化版调用成功!")
            print_haojia_detail_summary(simple_data)
        else:
            print("❌ 简化版调用失败")
        
        # 2. 测试版本1
        print("\n2️⃣ 测试版本1接口:")
        v1_response = get_haojia_info_detail(demo_article_id, version="1")
        if v1_response:
            parsed_v1 = parse_haojia_detail_v1(v1_response)
            if parsed_v1:
                print("✅ 版本1解析成功!")
                print(f"   📰 标题: {parsed_v1.get('title', 'N/A')}")
                print(f"   📝 内容: {parsed_v1.get('content', 'N/A')[:50]}...")
                print(f"   🛒 购买链接: {parsed_v1.get('buy_url', 'N/A')}")
            else:
                print("❌ 版本1解析失败")
        
        # 3. 测试版本2
        print("\n3️⃣ 测试版本2接口:")
        v2_response = get_haojia_info_detail(demo_article_id, version="2")
        if v2_response:
            parsed_v2 = parse_haojia_detail_v2(v2_response)
            if parsed_v2:
                print("✅ 版本2解析成功!")
                print(f"   📰 标题: {parsed_v2.get('title', 'N/A')}")
                print(f"   💰 价格: {parsed_v2.get('subtitle', 'N/A')}")
                print(f"   🏪 商城: {parsed_v2.get('mall', 'N/A')}")
                print(f"   👤 爆料人: {parsed_v2.get('user', {}).get('nickname', 'N/A')}")
                
                # 显示优惠券信息
                coupons = parsed_v2.get('coupons', [])
                if coupons:
                    print(f"   🎫 优惠券: {len(coupons)}张")
                    for i, coupon in enumerate(coupons[:2], 1):
                        print(f"      {i}. {coupon.get('title', 'N/A')}")
            else:
                print("❌ 版本2解析失败")
    
    else:
        print("⚠️  演示模式 - 需要有效的ACCESS_TOKEN进行真实调用")
        print("\n📋 API接口信息:")
        print(f"   🌐 接口地址: https://openapi.zhidemai.com/v1/youhui/detail/show")
        print(f"   📝 必需参数: article_id, version")
        print(f"   🔢 支持版本: 1 (基础信息), 2 (详细信息)")
        
        print("\n💡 使用示例:")
        print("```python")
        print("# 简单调用")
        print(f'data = get_haojia_detail_simple("{demo_article_id}")')
        print("print_haojia_detail_summary(data)")
        print("")
        print("# 高级调用")
        print(f'response = get_haojia_info_detail("{demo_article_id}", version="2")')
        print("parsed = parse_haojia_detail_v2(response)")
        print("```")

def main():
    """
    主函数
    """
    try:
        # 演示API使用
        demo_api_usage()
        
        # 运行完整测试
        print("\n" + "=" * 60)
        print("🧪 运行完整测试套件...")
        test_haojia_detail_api()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成!")
        print("\n📚 更多功能:")
        print("   - get_haojia_info_detail(): 核心API调用函数")
        print("   - parse_haojia_detail_v1/v2(): 数据解析函数")
        print("   - get_haojia_detail_simple(): 简化调用函数")
        print("   - print_haojia_detail_summary(): 格式化输出函数")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
