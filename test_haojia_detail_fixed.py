#!/usr/bin/env python3
"""
修复后的好价信息详情接口测试脚本
测试OAuth2.0 API请求功能和接口修复情况

使用方法:
python test_haojia_detail_fixed.py
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    get_haojia_info_detail,
    get_haojia_detail_simple,
    parse_haojia_detail_v2,
    print_haojia_detail_summary,
    make_oauth2_api_request,
    test_haojia_detail_with_real_token,
    APP_KEY,
    APP_SECRET
)

def test_oauth2_request_function():
    """
    测试OAuth2.0请求函数
    """
    print("🔧 测试OAuth2.0请求函数")
    print("=" * 50)
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"🔐 APP_SECRET: {APP_SECRET[:8]}..." if APP_SECRET else "APP_SECRET: 未设置")
    
    # 模拟参数测试
    test_url = "https://openapi.smzdm.com/v1/youhui/detail/show"
    test_params = {
        "article_id": "13284568",
        "version": "2"
    }
    mock_token = "mock_access_token_for_testing"
    
    print(f"\n📋 测试参数:")
    print(f"   URL: {test_url}")
    print(f"   参数: {test_params}")
    print(f"   Token: {mock_token[:20]}...")
    
    print(f"\n⚠️  注意: 这是模拟测试，需要真实token才能成功调用")
    
    try:
        # 这会失败，但可以测试函数逻辑
        print(f"\n🔄 尝试OAuth2.0请求...")
        result = make_oauth2_api_request("GET", test_url, test_params, mock_token)
        
        if result:
            print("✅ 请求函数执行成功（虽然可能返回错误）")
        else:
            print("❌ 请求函数返回None（预期行为，因为token无效）")
            
    except Exception as e:
        print(f"❌ 请求函数异常: {e}")

def test_interface_improvements():
    """
    测试接口改进情况
    """
    print("\n🔍 测试接口改进情况")
    print("=" * 50)
    
    improvements = [
        "✅ 添加了专门的OAuth2.0 API请求函数",
        "✅ 区分了签名API和OAuth2.0 API的请求方式", 
        "✅ 增强了错误处理和调试信息",
        "✅ 添加了详细的请求日志输出",
        "✅ 提供了真实token测试功能",
        "✅ 改进了参数验证和异常处理"
    ]
    
    print("📋 主要改进:")
    for improvement in improvements:
        print(f"   {improvement}")
    
    print(f"\n🔧 关键修复:")
    print("   1. 好价信息详情接口现在使用OAuth2.0请求方式")
    print("   2. 不再对OAuth2.0接口进行签名处理")
    print("   3. 添加了详细的请求和响应日志")
    print("   4. 提供了更好的错误诊断信息")

def test_api_differences():
    """
    测试API类型差异
    """
    print("\n📊 API类型差异说明")
    print("=" * 50)
    
    print("🔐 签名API (make_api_request):")
    print("   - 需要APP_KEY, APP_SECRET, access_token")
    print("   - 需要生成MD5签名")
    print("   - 包含timestamp, v, format等公共参数")
    print("   - 适用于大部分业务API")
    
    print("\n🎫 OAuth2.0 API (make_oauth2_api_request):")
    print("   - 只需要APP_KEY, access_token")
    print("   - 不需要签名")
    print("   - 参数更简洁")
    print("   - 适用于OAuth2.0授权的接口")
    
    print("\n📋 好价信息详情接口特点:")
    print("   - 使用OAuth2.0方式")
    print("   - 接口地址: https://openapi.smzdm.com/v1/youhui/detail/show")
    print("   - 必需参数: article_id, version")
    print("   - 需要有效的OAuth2.0 access_token")

def interactive_test():
    """
    交互式测试
    """
    print("\n🎮 交互式测试")
    print("=" * 50)
    
    print("选择测试选项:")
    print("1. 测试OAuth2.0请求函数")
    print("2. 使用真实token测试接口")
    print("3. 查看API差异说明")
    print("4. 运行所有测试")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        test_oauth2_request_function()
    elif choice == "2":
        test_haojia_detail_with_real_token()
    elif choice == "3":
        test_api_differences()
    elif choice == "4":
        test_oauth2_request_function()
        test_interface_improvements()
        test_api_differences()
        
        # 询问是否进行真实token测试
        real_test = input("\n是否进行真实token测试? (y/N): ").strip().lower()
        if real_test == 'y':
            test_haojia_detail_with_real_token()
    else:
        print("❌ 无效选择")

def main():
    """
    主函数
    """
    print("🔧 好价信息详情接口修复测试")
    print("=" * 60)
    
    print("📋 修复内容:")
    print("- 添加了专门的OAuth2.0 API请求函数")
    print("- 修复了好价信息详情接口的请求方式")
    print("- 增强了错误处理和调试功能")
    print("- 提供了真实token测试选项")
    
    try:
        # 显示接口改进
        test_interface_improvements()
        
        # 交互式测试
        interactive_test()
        
        print("\n" + "=" * 60)
        print("🎉 测试完成!")
        print("\n💡 使用建议:")
        print("1. 通过OAuth2.0流程获取真实access_token")
        print("2. 使用get_haojia_detail_simple()进行简单调用")
        print("3. 检查返回的error_code判断调用是否成功")
        print("4. 使用parse_haojia_detail_v2()解析详细数据")
        
        print("\n📚 相关函数:")
        print("- get_haojia_info_detail(): 核心接口函数")
        print("- make_oauth2_api_request(): OAuth2.0请求函数")
        print("- parse_haojia_detail_v2(): 数据解析函数")
        print("- print_haojia_detail_summary(): 格式化输出")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
