#!/usr/bin/env python3
"""
测试修改后的 jd_chatbot 函数
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.jd_picture2good import jd_chatbot

def test_jd_chatbot():
    """测试 jd_chatbot 函数"""
    print("🚀 开始测试 jd_chatbot 函数...")
    
    # 测试参数 - 使用你提供的示例
    search_text = "iPhone 17 promax 256GB 如何？"
    
    try:
        # 调用函数
        result = jd_chatbot(search_text)
        
        print("✅ 函数调用成功!")
        print(f"📝 搜索文本: {search_text}")
        print(f"📄 返回结果类型: {type(result)}")
        print(f"📏 返回结果长度: {len(result) if result else 0}")
        
        if result:
            print("\n📋 返回结果预览 (前500字符):")
            print("-" * 50)
            print(result[:500])
            if len(result) > 500:
                print("...")
            print("-" * 50)
        else:
            print("⚠️ 返回结果为空")
            
        return True
        
    except Exception as e:
        print(f"❌ 函数调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_jd_chatbot()
    if success:
        print("\n🎉 测试完成!")
    else:
        print("\n💥 测试失败!")
        sys.exit(1)
