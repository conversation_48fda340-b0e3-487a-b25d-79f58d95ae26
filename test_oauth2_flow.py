#!/usr/bin/env python3
"""
Zhidemai OAuth2.0授权流程测试脚本
演示完整的OAuth2.0授权码模式流程

使用方法:
python test_oauth2_flow.py
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    generate_authorization_url,
    get_access_token_by_code,
    parse_access_token_response,
    is_access_token_valid,
    oauth2_complete_flow,
    test_oauth2_flow,
    test_token_management,
    APP_KEY,
    APP_SECRET
)

def interactive_oauth2_demo():
    """
    交互式OAuth2.0演示
    """
    print("🔐 Zhidemai OAuth2.0授权流程交互式演示")
    print("=" * 60)
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"🔐 APP_SECRET: {APP_SECRET[:8]}..." if APP_SECRET else "APP_SECRET: 未设置")
    
    if not APP_KEY or not APP_SECRET:
        print("\n❌ 错误: APP_KEY和APP_SECRET必须设置才能进行OAuth2.0流程")
        return
    
    print("\n📋 OAuth2.0授权码模式流程:")
    print("1️⃣ 生成授权URL")
    print("2️⃣ 用户访问URL并授权")
    print("3️⃣ 获取授权码")
    print("4️⃣ 交换access_token")
    print("5️⃣ 使用token调用API")
    
    # 步骤1: 配置回调地址
    print("\n" + "="*60)
    print("步骤1: 配置回调地址")
    print("-" * 30)
    
    default_redirect = "https://your-app.com/callback"
    redirect_uri = input(f"请输入回调地址 (默认: {default_redirect}): ").strip()
    if not redirect_uri:
        redirect_uri = default_redirect
    
    state = input("请输入状态参数 (可选，用于防CSRF): ").strip() or None
    
    # 步骤2: 生成授权URL
    print("\n" + "="*60)
    print("步骤2: 生成授权URL")
    print("-" * 30)
    
    try:
        auth_url = generate_authorization_url(redirect_uri, state)
        print(f"✅ 授权URL生成成功!")
        print(f"\n🔗 请在浏览器中打开以下URL进行授权:")
        print(f"{auth_url}")
        
        print(f"\n📝 授权完成后，您将被重定向到:")
        print(f"{redirect_uri}?code=AUTHORIZATION_CODE&state={state or 'N/A'}")
        
    except Exception as e:
        print(f"❌ 授权URL生成失败: {e}")
        return
    
    # 步骤3: 获取授权码
    print("\n" + "="*60)
    print("步骤3: 获取授权码")
    print("-" * 30)
    
    print("请完成以下操作:")
    print("1. 在浏览器中打开上面的授权URL")
    print("2. 登录您的值得买账号")
    print("3. 点击授权按钮")
    print("4. 从重定向的URL中复制code参数")
    
    code = input("\n请输入获取到的授权码 (code参数): ").strip()
    
    if not code:
        print("❌ 未输入授权码，演示结束")
        return
    
    # 步骤4: 交换access_token
    print("\n" + "="*60)
    print("步骤4: 交换access_token")
    print("-" * 30)
    
    print(f"🔄 正在使用授权码获取access_token...")
    print(f"授权码: {code[:10]}...")
    
    try:
        token_response = get_access_token_by_code(code)
        
        if token_response and token_response.get("error_code") == "0":
            print("✅ access_token获取成功!")
            
            # 解析token信息
            token_info = parse_access_token_response(token_response)
            
            if token_info:
                print(f"\n📋 Token信息:")
                print(f"  🎫 access_token: {token_info['access_token'][:30]}...")
                print(f"  ⏰ expires_in: {token_info['expires_in']}秒")
                print(f"  👤 union_id: {token_info['union_id']}")
                print(f"  📅 expires_at: {token_info['expires_at']}")
                print(f"  ✅ 有效性: {'有效' if is_access_token_valid(token_info) else '已过期'}")
                
                # 步骤5: 使用token
                print("\n" + "="*60)
                print("步骤5: 使用access_token")
                print("-" * 30)
                
                print("🎉 恭喜! 您已成功获取access_token")
                print("\n💡 现在您可以使用这个token调用API:")
                print("```python")
                print(f"access_token = '{token_info['access_token']}'")
                print("")
                print("# 调用好价详情接口")
                print("from src.fc_module.tools.zhidemai_info import get_haojia_detail_simple")
                print("data = get_haojia_detail_simple('13284568', access_token)")
                print("```")
                
                # 询问是否测试API调用
                test_api = input("\n是否使用此token测试API调用? (y/N): ").strip().lower()
                if test_api == 'y':
                    test_api_with_token(token_info['access_token'])
                
            else:
                print("❌ Token信息解析失败")
        else:
            error_msg = token_response.get("error_msg", "未知错误") if token_response else "请求失败"
            print(f"❌ access_token获取失败: {error_msg}")
            
            # 显示常见错误解决方案
            print("\n🔧 常见错误解决方案:")
            print("- 200002 code empty: 检查授权码是否正确输入")
            print("- 200003 invalid code: 授权码无效或已过期，请重新获取")
            print("- 200004 code is inconsistent: 授权码与APP_KEY不匹配")
            
    except Exception as e:
        print(f"❌ 获取access_token过程中发生异常: {e}")

def test_api_with_token(access_token):
    """
    使用获取到的token测试API调用
    """
    print("\n🧪 使用真实token测试API调用")
    print("-" * 40)
    
    try:
        from fc_module.tools.zhidemai_info import get_haojia_detail_simple
        
        test_article_id = "13284568"
        print(f"测试文章ID: {test_article_id}")
        print(f"使用token: {access_token[:20]}...")
        
        data = get_haojia_detail_simple(test_article_id, access_token)
        
        if data:
            print("✅ API调用成功!")
            print(f"  标题: {data.get('title', 'N/A')}")
            print(f"  价格: {data.get('subtitle', 'N/A')}")
            print(f"  商城: {data.get('mall', 'N/A')}")
        else:
            print("❌ API调用失败")
            
    except Exception as e:
        print(f"❌ API测试过程中发生异常: {e}")

def main():
    """
    主函数
    """
    print("🚀 Zhidemai OAuth2.0授权流程测试")
    print("选择测试模式:")
    print("1. 交互式OAuth2.0演示 (推荐)")
    print("2. 自动化测试套件")
    print("3. 完整流程演示")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    try:
        if choice == "1":
            interactive_oauth2_demo()
        elif choice == "2":
            print("\n🧪 运行自动化测试套件...")
            test_oauth2_flow()
            test_token_management()
        elif choice == "3":
            print("\n📋 完整流程演示...")
            redirect_uri = "https://example.com/callback"
            state = "demo_state_123"
            flow_info = oauth2_complete_flow(redirect_uri, state)
            print(f"\n✅ 演示完成，请查看上方输出")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
