#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Zhidemai OAuth2.0授权端点
找到正确的授权URL
"""

import requests
import time
from urllib.parse import urlencode

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def test_authorization_endpoints():
    """
    测试不同的授权端点，找到正确的URL
    """
    print("🔍 测试Zhidemai OAuth2.0授权端点")
    print("=" * 50)
    
    # 可能的授权端点列表
    possible_endpoints = [
        "https://openapi.smzdm.com/oauth2/authorize",
        "https://openapi.smzdm.com/oauth/authorize", 
        "https://www.smzdm.com/oauth2/authorize",
        "https://www.smzdm.com/oauth/authorize",
        "https://smzdm.com/oauth2/authorize",
        "https://smzdm.com/oauth/authorize",
        "https://zhidemai.com/oauth2/authorize",
        "https://zhidemai.com/oauth/authorize",
        "https://openapi.zhidemai.com/oauth2/authorize",
        "https://openapi.zhidemai.com/oauth/authorize",
        "https://auth.smzdm.com/oauth2/authorize",
        "https://auth.smzdm.com/oauth/authorize",
        "https://login.smzdm.com/oauth2/authorize",
        "https://login.smzdm.com/oauth/authorize"
    ]
    
    redirect_uri = "http://localhost:8080/callback"
    state = "test_state_123"
    
    # 构建测试参数
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    query_string = urlencode(params)
    working_endpoints = []
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"📍 回调地址: {redirect_uri}")
    print(f"🔐 状态参数: {state}")
    print(f"\n开始测试 {len(possible_endpoints)} 个可能的端点...\n")
    
    for i, base_url in enumerate(possible_endpoints, 1):
        full_url = f"{base_url}?{query_string}"
        
        print(f"{i:2d}. 测试: {base_url}")
        
        try:
            # 发送HEAD请求检查端点是否存在
            response = requests.head(full_url, timeout=5, allow_redirects=False)
            status_code = response.status_code
            
            if status_code == 200:
                print(f"    ✅ 200 OK - 端点可用")
                working_endpoints.append((base_url, full_url, "200 OK"))
            elif status_code == 302 or status_code == 301:
                print(f"    🔄 {status_code} 重定向 - 可能是正确的授权端点")
                location = response.headers.get('Location', 'N/A')
                print(f"    📍 重定向到: {location}")
                working_endpoints.append((base_url, full_url, f"{status_code} 重定向"))
            elif status_code == 405:
                print(f"    ⚠️  405 方法不允许 - 端点存在但不支持HEAD请求")
                # 尝试GET请求
                try:
                    get_response = requests.get(full_url, timeout=5, allow_redirects=False)
                    if get_response.status_code in [200, 302, 301]:
                        print(f"    ✅ GET请求: {get_response.status_code} - 端点可用")
                        working_endpoints.append((base_url, full_url, f"GET {get_response.status_code}"))
                except:
                    print(f"    ❌ GET请求也失败")
            elif status_code == 404:
                print(f"    ❌ 404 未找到")
            elif status_code == 403:
                print(f"    🚫 403 禁止访问 - 端点存在但需要特定权限")
                working_endpoints.append((base_url, full_url, "403 需要权限"))
            else:
                print(f"    ❓ {status_code} - 未知状态")
                
        except requests.exceptions.Timeout:
            print(f"    ⏰ 请求超时")
        except requests.exceptions.ConnectionError:
            print(f"    🔌 连接错误")
        except Exception as e:
            print(f"    ❌ 异常: {str(e)[:50]}...")
        
        # 避免请求过快
        time.sleep(0.5)
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("-" * 50)
    
    if working_endpoints:
        print(f"✅ 找到 {len(working_endpoints)} 个可能的工作端点:")
        for i, (base_url, full_url, status) in enumerate(working_endpoints, 1):
            print(f"\n{i}. {base_url}")
            print(f"   状态: {status}")
            print(f"   完整URL: {full_url}")
        
        print(f"\n💡 推荐使用:")
        # 优先推荐重定向的端点
        redirect_endpoints = [ep for ep in working_endpoints if "重定向" in ep[2]]
        if redirect_endpoints:
            recommended = redirect_endpoints[0]
            print(f"   {recommended[0]} (因为有重定向行为)")
        else:
            recommended = working_endpoints[0]
            print(f"   {recommended[0]} (第一个可用端点)")
            
        return recommended[0]
    else:
        print("❌ 未找到任何可用的授权端点")
        print("\n🤔 可能的原因:")
        print("1. OAuth2.0功能可能未启用或需要特殊配置")
        print("2. 需要在开发者平台注册回调地址")
        print("3. APP_KEY可能无效或未激活OAuth2.0权限")
        print("4. 授权端点可能使用了不同的路径结构")
        
        return None

def test_manual_browser_access():
    """
    提供手动浏览器测试的建议
    """
    print("\n🌐 手动浏览器测试建议")
    print("=" * 30)
    
    redirect_uri = "http://localhost:8080/callback"
    state = "manual_test_123"
    
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    query_string = urlencode(params)
    
    # 最可能的几个端点
    likely_endpoints = [
        "https://www.smzdm.com/oauth2/authorize",
        "https://openapi.smzdm.com/oauth2/authorize",
        "https://login.smzdm.com/oauth2/authorize"
    ]
    
    print("请手动在浏览器中测试以下URL:")
    for i, base_url in enumerate(likely_endpoints, 1):
        full_url = f"{base_url}?{query_string}"
        print(f"\n{i}. {full_url}")
    
    print(f"\n📝 如果某个URL能正常显示登录页面，那就是正确的授权端点！")

def main():
    """
    主函数
    """
    print("🔐 Zhidemai OAuth2.0端点测试工具")
    print("选择测试方式:")
    print("1. 自动测试所有可能的端点")
    print("2. 获取手动浏览器测试URL")
    print("3. 两种方式都执行")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    try:
        if choice == "1":
            working_endpoint = test_authorization_endpoints()
            if working_endpoint:
                print(f"\n🎉 建议使用端点: {working_endpoint}")
        elif choice == "2":
            test_manual_browser_access()
        elif choice == "3":
            working_endpoint = test_authorization_endpoints()
            test_manual_browser_access()
            if working_endpoint:
                print(f"\n🎉 自动测试建议端点: {working_endpoint}")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断测试")
    except Exception as e:
        print(f"❌ 发生异常: {e}")

if __name__ == "__main__":
    main()
