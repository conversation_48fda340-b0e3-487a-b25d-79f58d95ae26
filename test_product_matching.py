from src.fc_module.product_matching import evaluate_product_match

# 测试数据
test_data = {
    "user_images": [
        {"image_url": "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/%E5%85%85%E7%94%B5%E5%AE%9D.jpeg"}
    ],
    "product_name": "充电宝",
    "product_image_url": "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250917-222045.png",
    "product_title": "移速 （MOVE SPEED）（3C认证）移速充电宝迷你便携10000毫安大容量双向快充移动电源淡"
}
# test_data = {
#     "user_images": [
#         {"image_url": "https://experience-center.tos-cn-beijing.volces.com/landscape_compress/00000151.jpg?X-Tos-Algorithm=TOS4-HMAC-SHA256&X-Tos-Credential=AKTP0W2SmSuRexIcRYjWtYHgbq479HDCAESvcauNSkRpbP%2F20250917%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Date=20250917T143057Z&X-Tos-Expires=86400&X-Tos-Security-Token=nChBNWFhGQ01Uc0ZsRzh4a0Rt.CiQKEHd1MUF3VFM1VTRkTTM1S2sSEHJ1bTh8fk8vmIyONUAzjCMQpoSrxgYYtqCrxgYg_4zM6QcoBDDm3pkBOihTZXJ2aWNlUm9sZUZvck1MUGxhdGZvcm0vdmlraW5nZGJfc2VydmVyQgttbF9wbGF0Zm9ybVIPdmlraW5nZGJfc2VydmVyWAN6C21sX3BsYXRmb3Jt.16s4B2liiwzAJ0WHl76IcO8h839k_ph6ivBDmZERDeEe2K5a9OSLXeo6hr_iMjBncqHUH3dRjoTSnE77-S9ZUA&X-Tos-Signature=0519aefd4bd3b2f8b4c24eee1a482ef56aa5c7ad6eab5ead8e7037c1ba9110b1&X-Tos-SignedHeaders=host"}
#     ],
#     "product_name": "充电宝",
#     "product_image_url": "https://test-sl-bj-oss-multi-modal.oss-cn-beijing.aliyuncs.com/%E6%B5%8B%E8%AF%95%E5%9B%BE%E7%89%87%E6%96%B0/20250917-222045.png",
#     "product_title": "移速 （MOVE SPEED）（3C认证）移速充电宝迷你便携10000毫安大容量双向快充移动电源淡"
# }


# 调用函数测试
result = evaluate_product_match(
    user_images=test_data["user_images"],
    product_name=test_data["product_name"],
    product_image_url=test_data["product_image_url"],
    product_title=test_data["product_title"]
)

# 打印结果
print("测试结果:")
print(f"图片中是否有商品: {'是' if result['has_product'] else '否'}")
print(f"是否同一商品: {'是' if result['is_same_product'] else '否'}")
print(f"匹配分数: {result['match_score']}/10")
print("格式化输出:")
print(result['formatted_output'])
# -----
# 测试结果:
# 图片中是否有商品: 否
# 是否同一商品: 否
# 匹配分数: 0/10
# 格式化输出:
# <withGood>否</withGood>
# <GoodScore>0</GoodScore>
