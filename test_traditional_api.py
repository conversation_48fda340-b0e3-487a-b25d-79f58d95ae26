#!/usr/bin/env python3
"""
测试传统签名方式调用Zhidemai API
不需要access_token，只使用APP_KEY和APP_SECRET

使用方法:
python test_traditional_api.py
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    get_haojia_info_detail_traditional,
    make_traditional_api_request,
    generate_sign,
    APP_KEY,
    APP_SECRET
)

def test_traditional_signature():
    """
    测试传统签名算法
    """
    print("🔐 传统签名算法测试")
    print("=" * 50)
    
    if not APP_KEY or not APP_SECRET:
        print("❌ 错误: APP_KEY和APP_SECRET必须设置")
        return False
    
    print(f"🔑 APP_KEY: {APP_KEY}")
    print(f"🔐 APP_SECRET: {APP_SECRET[:8]}...")
    
    # 测试参数
    import time
    test_params = {
        "app_key": APP_KEY,
        "timestamp": str(int(time.time() * 1000)),
        "v": "1.0",
        "format": "json",
        "article_id": "13284568",
        "version": "2"
    }
    
    print(f"\n📋 测试参数:")
    for key, value in test_params.items():
        print(f"   {key}: {value}")
    
    # 生成签名
    try:
        sign = generate_sign(test_params, APP_SECRET)
        print(f"\n🔏 生成的签名: {sign}")
        print(f"✅ 签名算法测试通过")
        return True
    except Exception as e:
        print(f"❌ 签名算法测试失败: {e}")
        return False

def test_traditional_api_call():
    """
    测试传统API调用
    """
    print(f"\n🚀 传统API调用测试")
    print("=" * 50)
    
    test_article_id = "13284568"
    print(f"📋 测试文章ID: {test_article_id}")
    print(f"🔧 调用方式: 纯传统签名（无access_token）")
    
    try:
        # 调用传统签名方式的API
        result = get_haojia_info_detail_traditional(test_article_id, version="2")
        
        if result:
            print(f"\n📊 API调用结果分析:")
            print("-" * 30)
            
            error_code = result.get("error_code", "")
            error_msg = result.get("error_msg", "")
            
            print(f"错误码: {error_code}")
            print(f"错误信息: {error_msg}")
            
            if error_code == "0":
                print("🎉 API调用成功!")
                
                # 分析返回数据
                data = result.get("data", {})
                if data:
                    print(f"\n📄 返回数据字段:")
                    for key in list(data.keys())[:10]:  # 只显示前10个字段
                        value = data.get(key, "")
                        if isinstance(value, str) and len(value) > 50:
                            value = value[:50] + "..."
                        print(f"   {key}: {value}")
                    
                    if len(data.keys()) > 10:
                        print(f"   ... 还有 {len(data.keys()) - 10} 个字段")
                else:
                    print("⚠️  返回数据为空")
                    
                return True
                
            else:
                print(f"❌ API返回错误")
                
                # 分析常见错误码
                if error_code == "200001":
                    print("💡 可能原因: 参数错误或缺失")
                elif error_code == "200002":
                    print("💡 可能原因: 签名验证失败")
                elif error_code == "200003":
                    print("💡 可能原因: 时间戳过期")
                elif error_code == "200004":
                    print("💡 可能原因: APP_KEY无效")
                elif error_code == "200005":
                    print("💡 可能原因: 接口权限不足")
                elif error_code == "404":
                    print("💡 可能原因: 接口地址错误或不存在")
                else:
                    print("💡 建议: 检查参数和权限配置")
                
                return False
        else:
            print("❌ API调用失败，未收到响应")
            return False
            
    except Exception as e:
        print(f"❌ API调用过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_versions():
    """
    测试不同版本的API
    """
    print(f"\n🔄 测试不同API版本")
    print("=" * 50)
    
    test_article_id = "13284568"
    versions = ["1", "2"]
    
    results = {}
    
    for version in versions:
        print(f"\n📋 测试版本 {version}:")
        print("-" * 20)
        
        try:
            result = get_haojia_info_detail_traditional(test_article_id, version=version)
            
            if result:
                error_code = result.get("error_code", "")
                if error_code == "0":
                    print(f"✅ 版本 {version}: 调用成功")
                    results[version] = "success"
                else:
                    print(f"❌ 版本 {version}: {error_code} - {result.get('error_msg', '')}")
                    results[version] = f"error_{error_code}"
            else:
                print(f"❌ 版本 {version}: 调用失败")
                results[version] = "failed"
                
        except Exception as e:
            print(f"❌ 版本 {version}: 异常 - {e}")
            results[version] = f"exception_{type(e).__name__}"
    
    print(f"\n📊 版本测试结果:")
    print("-" * 20)
    for version, status in results.items():
        print(f"版本 {version}: {status}")
    
    return results

def analyze_api_support():
    """
    分析API支持情况
    """
    print(f"\n🔍 API支持情况分析")
    print("=" * 50)
    
    print("📋 传统签名方式特点:")
    print("   ✅ 不需要OAuth2.0授权流程")
    print("   ✅ 不需要access_token")
    print("   ✅ 只需要APP_KEY和APP_SECRET")
    print("   ✅ 适合服务端调用")
    
    print(f"\n📋 可能的结果:")
    print("   1. 成功 (error_code=0): 接口支持传统签名方式")
    print("   2. 权限错误: 接口可能只支持OAuth2.0方式")
    print("   3. 签名错误: 签名算法或参数有问题")
    print("   4. 接口错误: 接口地址或参数不正确")
    
    print(f"\n💡 如果传统方式失败:")
    print("   - 可能该接口只支持OAuth2.0方式")
    print("   - 需要先获取access_token")
    print("   - 运行: python get_access_token_manual.py")

def main():
    """
    主函数
    """
    print("🔬 Zhidemai传统签名API测试")
    print("=" * 60)
    
    try:
        # 1. 测试签名算法
        if not test_traditional_signature():
            print("❌ 签名算法测试失败，无法继续")
            return
        
        # 2. 分析API支持情况
        analyze_api_support()
        
        # 3. 测试传统API调用
        success = test_traditional_api_call()
        
        # 4. 测试不同版本
        if success:
            test_different_versions()
        
        print(f"\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        
        if success:
            print("🎉 传统签名方式测试成功!")
            print("✅ 好价信息详情接口支持传统签名调用")
            print("💡 你可以直接使用传统方式，无需access_token")
            
            print(f"\n🛠️  使用方法:")
            print("from src.fc_module.tools.zhidemai_info import get_haojia_info_detail_traditional")
            print("data = get_haojia_info_detail_traditional('13284568', version='2')")
            
        else:
            print("❌ 传统签名方式测试失败")
            print("💡 该接口可能只支持OAuth2.0方式")
            print("🔧 建议获取access_token后使用OAuth2.0方式:")
            print("   python get_access_token_manual.py")
        
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
