#!/usr/bin/env python3
"""
Zhidemai API 测试脚本
测试API调用方法详解中的签名生成和请求构建过程

使用方法:
python test_zhidemai_api.py
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from fc_module.tools.zhidemai_info import (
    run_all_tests,
    test_api_calling_method,
    test_real_api_call_with_mock_token,
    APP_KEY,
    APP_SECRET
)

def main():
    """
    主测试函数
    """
    print("Zhidemai API 调用方法测试")
    print("=" * 60)
    print(f"APP_KEY: {APP_KEY}")
    print(f"APP_SECRET: {APP_SECRET[:8]}..." if APP_SECRET else "APP_SECRET: 未设置")
    print("=" * 60)
    
    try:
        # 运行所有测试
        result = run_all_tests()
        
        print("\n" + "=" * 60)
        print("测试结果总结:")
        print(f"✓ 签名算法验证: {'通过' if result['verification_passed'] else '失败'}")
        print("✓ 参数排序和拼接: 完成")
        print("✓ MD5签名生成: 完成")
        print("✓ URL构建: 完成")
        
        if result['verification_passed']:
            print("\n🎉 所有测试通过！API调用方法实现正确。")
        else:
            print("\n❌ 签名验证失败，请检查实现。")
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
