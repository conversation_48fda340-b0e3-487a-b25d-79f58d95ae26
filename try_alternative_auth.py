#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
尝试值得买API的替代认证方式
包括客户端凭据模式、预授权token等
"""

import requests
import json
import time
import hashlib
from urllib.parse import urlencode

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def try_client_credentials_flow():
    """尝试客户端凭据模式 (Client Credentials Grant)"""
    print("🔐 尝试客户端凭据模式")
    print("=" * 40)
    
    # 常见的客户端凭据端点
    token_endpoints = [
        "https://openapi.smzdm.com/oauth2/token",
        "https://openapi.smzdm.com/oauth/token", 
        "https://openapi.smzdm.com/v1/oauth/token",
        "https://openapi.smzdm.com/v1/oauth2/token"
    ]
    
    for endpoint in token_endpoints:
        print(f"\n测试端点: {endpoint}")
        
        # 客户端凭据模式参数
        params = {
            "grant_type": "client_credentials",
            "client_id": APP_KEY,
            "client_secret": APP_SECRET
        }
        
        try:
            response = requests.post(endpoint, data=params, timeout=10)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"✅ 成功响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    if "access_token" in result:
                        access_token = result["access_token"]
                        print(f"🎉 获取到access_token: {access_token}")
                        
                        # 保存token
                        with open('access_token.txt', 'w') as f:
                            f.write(f"access_token={access_token}\n")
                            f.write(f"expires_in={result.get('expires_in', 0)}\n")
                            f.write(f"token_type={result.get('token_type', 'Bearer')}\n")
                            f.write(f"grant_type=client_credentials\n")
                        
                        print("💾 Token已保存到 access_token.txt")
                        return True
                        
                except json.JSONDecodeError:
                    print(f"响应内容: {response.text}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return False

def try_direct_token_request():
    """尝试直接token请求 (可能是预授权模式)"""
    print("\n🎯 尝试直接token请求")
    print("=" * 40)
    
    # 尝试直接使用APP_KEY和APP_SECRET获取token
    token_endpoints = [
        "https://openapi.smzdm.com/v1/oauth/check/code",
        "https://openapi.smzdm.com/v1/oauth/access_token",
        "https://openapi.smzdm.com/v1/oauth/get_token"
    ]
    
    for endpoint in token_endpoints:
        print(f"\n测试端点: {endpoint}")
        
        # 尝试不同的参数组合
        param_sets = [
            {
                "app_key": APP_KEY,
                "app_secret": APP_SECRET
            },
            {
                "client_id": APP_KEY,
                "client_secret": APP_SECRET,
                "grant_type": "client_credentials"
            },
            {
                "app_key": APP_KEY,
                "app_secret": APP_SECRET,
                "grant_type": "app_credentials"
            }
        ]
        
        for i, params in enumerate(param_sets, 1):
            print(f"  参数组合 {i}: {params}")
            
            try:
                response = requests.post(endpoint, data=params, timeout=10)
                print(f"  HTTP状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"  ✅ 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                        
                        if result.get("error_code") == "0" and "data" in result:
                            data = result["data"]
                            if "access_token" in data:
                                access_token = data["access_token"]
                                print(f"  🎉 获取到access_token: {access_token}")
                                
                                # 保存token
                                with open('access_token.txt', 'w') as f:
                                    f.write(f"access_token={access_token}\n")
                                    f.write(f"expires_in={data.get('expires_in', 0)}\n")
                                    f.write(f"union_id={data.get('union_id', '')}\n")
                                    f.write(f"grant_type=direct\n")
                                
                                print("  💾 Token已保存到 access_token.txt")
                                return True
                                
                    except json.JSONDecodeError:
                        print(f"  响应内容: {response.text}")
                else:
                    print(f"  ❌ 请求失败: {response.status_code}")
                    if response.text:
                        print(f"  响应内容: {response.text[:200]}...")
                        
            except Exception as e:
                print(f"  ❌ 请求异常: {e}")
    
    return False

def try_signature_based_auth():
    """尝试基于签名的认证方式"""
    print("\n✍️ 尝试签名认证方式")
    print("=" * 40)
    
    # 生成签名
    timestamp = str(int(time.time()))
    
    # 按照值得买API文档的签名方式
    params = {
        'app_key': APP_KEY,
        'timestamp': timestamp
    }
    
    # 按ASCII升序排序
    sorted_keys = sorted(params.keys())
    param_string = ''.join([f"{key}{params[key]}" for key in sorted_keys])
    sign_string = f"{APP_SECRET}{param_string}{APP_SECRET}"
    signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
    
    params['sign'] = signature
    
    print(f"签名参数: {params}")
    
    # 尝试获取token的端点
    endpoints = [
        "https://openapi.smzdm.com/v1/oauth/app_token",
        "https://openapi.smzdm.com/v1/oauth/signature_token"
    ]
    
    for endpoint in endpoints:
        print(f"\n测试端点: {endpoint}")
        
        try:
            response = requests.post(endpoint, data=params, timeout=10)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"✅ 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    if result.get("error_code") == "0":
                        print("🎉 签名认证可能成功!")
                        return True
                        
                except json.JSONDecodeError:
                    print(f"响应内容: {response.text}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return False

def check_existing_apis():
    """检查现有API是否可以直接调用"""
    print("\n🧪 测试现有API接口")
    print("=" * 40)
    
    # 尝试调用一些不需要OAuth2的API
    test_apis = [
        ("好价列表", "https://openapi.smzdm.com/haojia/third/list"),
        ("分类查询", "https://openapi.smzdm.com/category/query"),
        ("品牌查询", "https://openapi.smzdm.com/brand/query")
    ]
    
    timestamp = str(int(time.time()))
    
    for name, endpoint in test_apis:
        print(f"\n测试 {name}: {endpoint}")
        
        # 生成签名
        params = {
            'app_key': APP_KEY,
            'timestamp': timestamp,
            'page': '1',
            'page_size': '5'
        }
        
        sorted_keys = sorted(params.keys())
        param_string = ''.join([f"{key}{params[key]}" for key in sorted_keys])
        sign_string = f"{APP_SECRET}{param_string}{APP_SECRET}"
        signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
        params['sign'] = signature
        
        try:
            response = requests.get(endpoint, params=params, timeout=10)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"✅ API调用成功!")
                    print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)[:300]}...")
                    return True
                except json.JSONDecodeError:
                    print(f"响应内容: {response.text[:200]}...")
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    return False

def main():
    """主函数"""
    print("🔍 值得买API替代认证方式测试")
    print("=" * 50)
    
    print("由于OAuth2授权确认页面无法正常显示，尝试其他认证方式...")
    
    success = False
    
    # 1. 尝试客户端凭据模式
    if try_client_credentials_flow():
        success = True
    
    # 2. 尝试直接token请求
    if not success and try_direct_token_request():
        success = True
    
    # 3. 尝试签名认证
    if not success and try_signature_based_auth():
        success = True
    
    # 4. 检查现有API
    if not success and check_existing_apis():
        print("\n💡 发现可以直接使用签名方式调用API，无需OAuth2 token")
        success = True
    
    if success:
        print(f"\n🎉 找到可用的认证方式!")
        print("可以开始使用API了")
    else:
        print(f"\n❌ 所有认证方式都失败")
        print("建议:")
        print("1. 联系值得买技术支持")
        print("2. 检查开发者平台配置")
        print("3. 确认API权限设置")

if __name__ == "__main__":
    main()
