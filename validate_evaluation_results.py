#!/usr/bin/env python3
"""
验证LLM路由评估结果的完整性和正确性
"""

import json
import pandas as pd
from datetime import datetime
import sys
import os
import pathlib

# 添加路径
current_dir = pathlib.Path(__file__).parent
sys.path.append(str(current_dir))

from src.fc_module.utils.elastic_search_service import ElasticsearchService

def validate_evaluation_results():
    """验证评估结果的完整性和正确性"""
    print("开始验证评估结果...")
    
    # 1. 检查最新的评估结果文件
    json_files = [f for f in os.listdir('.') if f.startswith('llm_router_evaluation_full_') and f.endswith('.json')]
    csv_files = [f for f in os.listdir('.') if f.startswith('model_replacement_opportunities_') and f.endswith('.csv')]
    
    if not json_files or not csv_files:
        print("❌ 未找到评估结果文件")
        return False
    
    latest_json = sorted(json_files)[-1]
    latest_csv = sorted(csv_files)[-1]
    
    print(f"📊 使用评估文件:")
    print(f"  JSON: {latest_json}")
    print(f"  CSV: {latest_csv}")
    
    # 2. 验证JSON文件结构
    try:
        with open(latest_json, 'r', encoding='utf-8') as f:
            evaluation_data = json.load(f)
        
        print(f"\n✅ JSON文件加载成功，共 {len(evaluation_data)} 条记录")
        
        # 检查数据结构完整性
        required_fields = ['user_query', 'messages', 'embedding', 'tools', 'reference_model', 
                          'reference_function_calls', 'model_comparisons', 'candidates']
        
        for i, entry in enumerate(evaluation_data):
            missing_fields = [field for field in required_fields if field not in entry]
            if missing_fields:
                print(f"❌ 记录 {i} 缺少字段: {missing_fields}")
                return False
        
        print("✅ 所有记录数据结构完整")
        
    except Exception as e:
        print(f"❌ JSON文件验证失败: {e}")
        return False
    
    # 3. 验证CSV文件
    try:
        df = pd.read_csv(latest_csv)
        print(f"\n✅ CSV文件加载成功，共 {len(df)} 条替换记录")
        
        # 检查CSV结构
        required_columns = ['用户查询', '参考模型', '可替换模型', '替换类型']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ CSV缺少列: {missing_columns}")
            return False
        
        print("✅ CSV文件结构完整")
        
        # 统计信息
        print("\n📈 替换统计:")
        print(f"  总替换机会: {len(df)}")
        print(f"  不同参考模型: {df['参考模型'].nunique()}")
        print(f"  可替换模型分布:")
        for model, count in df['可替换模型'].value_counts().items():
            print(f"    {model}: {count} 次")
        
        print(f"  替换类型分布:")
        for replace_type, count in df['替换类型'].value_counts().items():
            print(f"    {replace_type}: {count} 次")
            
    except Exception as e:
        print(f"❌ CSV文件验证失败: {e}")
        return False
    
    # 4. 验证Elasticsearch数据
    try:
        es_service = ElasticsearchService(index_name="llm_router_evaluation_results")
        
        # 检查索引是否存在
        if es_service.client.indices.exists(index="llm_router_evaluation_results"):
            print("\n✅ Elasticsearch索引存在")
            
            # 获取文档数量
            count_result = es_service.client.count(index="llm_router_evaluation_results")
            doc_count = count_result.get('count', 0)
            print(f"  文档数量: {doc_count}")
            
            # 搜索最新文档
            search_result = es_service.client.search(
                index="llm_router_evaluation_results",
                body={
                    "query": {"match_all": {}},
                    "size": 1,
                    "sort": [{"evaluationMetadata.evaluation_timestamp": {"order": "desc"}}]
                }
            )
            
            if search_result['hits']['hits']:
                latest_doc = search_result['hits']['hits'][0]['_source']
                print("✅ 最新文档验证成功")
                print(f"  用户查询: {latest_doc.get('userContent', 'N/A')[:50]}...")
                print(f"
