#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作的OAuth2流程 - 已绕过CloudWAF
使用正确的请求头获取access_token
"""

import requests
import json
import time
import webbrowser
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlencode, urlparse, parse_qs
import random

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

def get_working_headers():
    """获取能绕过WAF的请求头"""
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
    ]
    
    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'cross-site',
        'Origin': 'https://www.smzdm.com',
        'Referer': 'https://www.smzdm.com/',
        'X-Requested-With': 'XMLHttpRequest'
    }
    return headers

def exchange_code_for_token(authorization_code):
    """使用授权码交换access_token"""
    print(f"🔄 正在交换授权码: {authorization_code[:10]}...")
    
    token_url = "https://openapi.smzdm.com/v1/oauth/check/code"
    headers = get_working_headers()
    
    params = {
        "app_key": APP_KEY,
        "code": authorization_code,
        "app_secret": APP_SECRET
    }
    
    try:
        response = requests.post(token_url, data=params, headers=headers, timeout=15)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get("error_code") == "0":
                data = result.get("data", {})
                access_token = data.get("access_token", "")
                
                if access_token:
                    print(f"🎉 成功获取access_token!")
                    print(f"Token: {access_token}")
                    
                    # 保存token到文件
                    with open('access_token.txt', 'w') as f:
                        f.write(f"access_token={access_token}\n")
                        f.write(f"expires_in={data.get('expires_in', 0)}\n")
                        f.write(f"union_id={data.get('union_id', '')}\n")
                        f.write(f"obtained_at={int(time.time())}\n")
                    
                    print("💾 Token已保存到 access_token.txt")
                    return access_token
                else:
                    print("❌ 响应中没有access_token")
            else:
                print(f"❌ API返回错误: {result.get('error_msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None

class OAuth2CallbackHandler(BaseHTTPRequestHandler):
    """OAuth2回调处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        if parsed_url.path == '/callback':
            code = query_params.get('code', [None])[0]
            state = query_params.get('state', [None])[0]
            error = query_params.get('error', [None])[0]
            
            if error:
                self.send_response(400)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>授权失败</title>
                </head>
                <body>
                    <h1>❌ 授权失败</h1>
                    <p>错误: {error}</p>
                    <p>请关闭此页面并重试</p>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
                
            elif code:
                print(f"\n✅ 收到授权码: {code}")
                print(f"状态参数: {state}")
                
                # 交换token
                access_token = exchange_code_for_token(code)
                
                if access_token:
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.end_headers()
                    
                    html = f"""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <title>授权成功</title>
                        <style>
                            body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
                            .success {{ color: #28a745; }}
                            .token {{ background: #f8f9fa; padding: 10px; border-radius: 5px; word-break: break-all; }}
                        </style>
                    </head>
                    <body>
                        <h1 class="success">🎉 授权成功!</h1>
                        <p>已成功获取access_token</p>
                        <div class="token">
                            <strong>Token:</strong> {access_token[:20]}...
                        </div>
                        <p>Token已保存到 access_token.txt 文件</p>
                        <p>你现在可以关闭此页面</p>
                    </body>
                    </html>
                    """
                    self.wfile.write(html.encode('utf-8'))
                    
                    # 设置标志停止服务器
                    self.server.should_stop = True
                else:
                    self.send_response(500)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.end_headers()
                    
                    html = """
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <title>Token获取失败</title>
                    </head>
                    <body>
                        <h1>❌ Token获取失败</h1>
                        <p>授权码交换失败，请查看控制台输出</p>
                        <p>你可以关闭此页面并重试</p>
                    </body>
                    </html>
                    """
                    self.wfile.write(html.encode('utf-8'))
            else:
                self.send_response(400)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>参数错误</title>
                </head>
                <body>
                    <h1>❌ 参数错误</h1>
                    <p>未收到授权码，请重新授权</p>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        """禁用日志输出"""
        pass

def start_callback_server():
    """启动回调服务器"""
    server = HTTPServer(('localhost', 8080), OAuth2CallbackHandler)
    server.should_stop = False
    
    print("🌐 启动本地回调服务器 (localhost:8080)...")
    
    def serve():
        while not getattr(server, 'should_stop', False):
            server.handle_request()
        print("🛑 回调服务器已停止")
    
    server_thread = threading.Thread(target=serve)
    server_thread.daemon = True
    server_thread.start()
    
    return server

def generate_authorization_url():
    """生成授权URL"""
    redirect_uri = "http://localhost:8080/callback"
    state = f"working_{int(time.time())}"
    
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri,
        "state": state
    }
    
    # 使用正确的授权端点
    auth_url = f"https://smzdm.com/oauth2/authorize?{urlencode(params)}"
    
    return auth_url, state

def test_token(access_token):
    """测试获取到的token"""
    print(f"\n🧪 测试access_token...")
    
    # 测试用户信息接口
    user_info_url = "https://openapi.smzdm.com/v1/user/info"
    headers = get_working_headers()
    
    params = {
        "access_token": access_token
    }
    
    try:
        response = requests.get(user_info_url, params=params, headers=headers, timeout=10)
        print(f"用户信息接口状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"用户信息: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"用户信息接口失败: {response.text}")
            
    except Exception as e:
        print(f"测试token异常: {e}")

def main():
    """主函数"""
    print("🚀 值得买OAuth2完整流程 (已绕过WAF)")
    print("=" * 50)
    
    print("✅ 已确认可以绕过CloudWAF")
    print("使用正确的请求头进行API调用")
    print()
    
    # 1. 启动回调服务器
    server = start_callback_server()
    
    # 2. 生成授权URL
    auth_url, state = generate_authorization_url()
    
    print(f"🔗 授权URL: {auth_url}")
    print(f"🔐 状态参数: {state}")
    print()
    
    # 3. 打开浏览器
    try:
        print("🌍 正在打开浏览器进行授权...")
        webbrowser.open(auth_url)
        print("✅ 浏览器已打开")
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        print("请手动复制上面的URL到浏览器中")
    
    print()
    print("📝 请在浏览器中:")
    print("1. 登录你的值得买账号")
    print("2. 点击授权按钮")
    print("3. 等待自动跳转到成功页面")
    print()
    print("⏳ 等待授权完成...")
    print("💡 完成后浏览器会显示成功页面，token会自动保存")
    print("🛑 按 Ctrl+C 可以随时停止")
    
    try:
        # 等待用户完成授权
        while not getattr(server, 'should_stop', False):
            time.sleep(1)
        
        print("\n🎉 OAuth2流程完成!")
        
        # 检查是否有保存的token
        try:
            with open('access_token.txt', 'r') as f:
                content = f.read()
                if 'access_token=' in content:
                    token_line = [line for line in content.split('\n') if line.startswith('access_token=')][0]
                    access_token = token_line.split('=')[1]
                    
                    print(f"📄 从文件读取token: {access_token[:20]}...")
                    
                    # 测试token
                    test_token(access_token)
                    
        except FileNotFoundError:
            print("❌ 未找到保存的token文件")
            
    except KeyboardInterrupt:
        print("\n🛑 用户取消操作")

if __name__ == "__main__":
    main()
